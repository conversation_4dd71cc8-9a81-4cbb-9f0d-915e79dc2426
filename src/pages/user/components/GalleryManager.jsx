import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentGallery } from '../../../contexts/ContentContext';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { 
  FiImage, FiPlus, FiArrowLeft, FiSave, FiTrash2, FiEdit, 
  FiSearch, FiEye, FiEyeOff, FiUpload, FiMenu
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const GalleryManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const {
    galleries, loading, getGalleryById, createGallery, updateGallery,
    deleteGallery, uploadImage, updateImageDetails, deleteImage, 
    updateAlbumOrder, updateImageOrder
  } = useContentGallery();
  
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentGallery, setCurrentGallery] = useState(null);
  const [galleryImages, setGalleryImages] = useState([]);
  const [imageUploading, setImageUploading] = useState(false);

  useEffect(() => {
    if (mode === 'edit' && id) {
      const fetchGallery = async () => {
        try {
          const galleryData = await getGalleryById(id);
          setCurrentGallery(galleryData);
          setGalleryImages(galleryData.images || []);
        } catch (err) {
          // hook will show toast
        }
      };
      fetchGallery();
    } else if (mode === 'add') {
      setCurrentGallery({ title: '', description: '', link: '', is_active: true });
      setGalleryImages([]);
    }
  }, [mode, id, getGalleryById]);

  const handleGalleryInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCurrentGallery(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };
  
  const handleSaveGallery = async (e) => {
    e.preventDefault();
    if (!currentGallery.title.trim()) {
      showToast.warning('Galeri başlığı zorunludur!');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'add') {
        const newGallery = await createGallery({
          title: currentGallery.title,
          description: currentGallery.description,
          link: currentGallery.link,
          is_active: currentGallery.is_active,
        });
        navigate(`/dashboard/galleries/edit/${newGallery.id}`);
      } else if (mode === 'edit') {
        await updateGallery(id, {
          title: currentGallery.title,
          description: currentGallery.description,
          link: currentGallery.link,
          is_active: currentGallery.is_active,
        });
      }
    } catch (err) {
      // hook will show toast
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteGallery = async (galleryId) => {
    if (!window.confirm('Bu galeriyi ve içindeki tüm resimleri silmek istediğinizden emin misiniz?')) return;
    try {
      await deleteGallery(galleryId);
    } catch (err) {
      // hook will show toast
    }
  };

  const handleToggleStatus = async (gallery) => {
    try {
      await updateGallery(gallery.id, { is_active: !gallery.is_active });
    } catch (err) {
      // hook will show toast
    }
  };

  const handleImageUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0 || !id) return;
    setImageUploading(true);
    try {
      for (const file of files) {
        const newImage = await uploadImage(file, id);
        setGalleryImages(prev => [...prev, newImage]);
      }
    } catch (err) {
      // hook will show toast
    } finally {
      setImageUploading(false);
    }
  };
  
  const handleUpdateImageTitle = async (imageId, title) => {
      try {
          await updateImageDetails(imageId, { image_title: title });
          setGalleryImages(prev => prev.map(img => img.id === imageId ? {...img, 'image_title': title} : img));
      } catch (err) {
          // hook will show toast
      }
  };

  const handleDeleteImage = async (imageId, imageUrl) => {
    if (!window.confirm('Bu resmi silmek istediğinizden emin misiniz?')) return;
    try {
      await deleteImage(imageId, imageUrl);
      setGalleryImages(prev => prev.filter(img => img.id !== imageId));
    } catch (err) {
      // hook will show toast
    }
  };

  const handleAlbumDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(galleries);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    try {
      await updateAlbumOrder(items);
    } catch (err) {
      // hook will show toast
    }
  };
  
  const handleImageDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(galleryImages);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setGalleryImages(items);
    try {
      await updateImageOrder(id, items);
    } catch (err) {
      // hook will show toast
    }
  };

  const filteredGalleries = galleries.filter(g => g.title.toLowerCase().includes(searchTerm.toLowerCase()));

  // RENDER LOGIC
  const renderListMode = () => (
    <div className="content-main-area">
      {/* Header */}
      <div className="content-header">
        <div className="content-header-info">
            <FiImage size={24} className="content-icon-fi" />
            <div className="content-title">
                <h2>Fotoğraf Galerileri</h2>
                <p>Albümlerinizi ve görsellerinizi buradan yönetin.</p>
            </div>
        </div>
        <div className="content-actions">
          <button className="action-button primary" onClick={() => navigate('/dashboard/galleries/add')}>
            <FiPlus size={18} /><span>Yeni Galeri</span>
          </button>
        </div>
      </div>
      {/* Toolbar */}
      <div className="content-toolbar">
          <div className="search-box">
              <FiSearch className="search-icon" />
              <input type="text" placeholder="Galerilerde ara..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
          </div>
      </div>
      {/* Body */}
      {loading ? (<p>Yükleniyor...</p>) : (
      <DragDropContext onDragEnd={handleAlbumDragEnd}>
        <Droppable droppableId="galleries">
          {(provided) => (
            <div className="table-wrapper" {...provided.droppableProps} ref={provided.innerRef}>
              <table className="custom-table">
                <thead><tr><th>Sıra</th><th>Kapak</th><th>Başlık</th><th>Resim Sayısı</th><th>Durum</th><th>Eylemler</th></tr></thead>
                <tbody>
                  {filteredGalleries.map((gallery, index) => (
                    <Draggable key={gallery.id} draggableId={gallery.id.toString()} index={index}>
                      {(provided) => (
                        <tr ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                          <td><FiMenu /></td>
                          <td><img src={gallery.cover_image || 'https://via.placeholder.com/80x60'} alt={gallery.title} className="table-image-preview"/></td>
                          <td>{gallery.title}</td>
                          <td>{gallery.gallery_images[0]?.count || 0}</td>
                          <td>
                            <span className={`status-badge ${gallery.is_active ? 'active' : 'inactive'}`}>
                              {gallery.is_active ? 'Yayında' : 'Gizli'}
                            </span>
                          </td>
                          <td>
                            <button onClick={() => handleToggleStatus(gallery)}>{gallery.is_active ? <FiEyeOff/> : <FiEye/>}</button>
                            <button onClick={() => navigate(`/dashboard/galleries/edit/${gallery.id}`)}><FiEdit/></button>
                            <button onClick={() => handleDeleteGallery(gallery.id)}><FiTrash2/></button>
                          </td>
                        </tr>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </tbody>
              </table>
            </div>
          )}
        </Droppable>
      </DragDropContext>
      )}
    </div>
  );

  const renderEditAddMode = () => (
    <div className="content-main-area">
        {/* Header */}
        <div className="content-header">
            <div className="content-header-info">
                <FiImage size={24} className="content-icon-fi" />
                <div className="content-title">
                    <h2>{mode === 'edit' ? 'Galeriyi Düzenle' : 'Yeni Galeri Oluştur'}</h2>
                    <p>{mode === 'edit' ? 'Galeri detaylarını ve resimlerini yönetin.' : 'Yeni bir galeri albümü oluşturun.'}</p>
                </div>
            </div>
            <div className="content-actions">
              <button className="action-button secondary" onClick={() => navigate('/dashboard/galleries/list')}><FiArrowLeft /><span>Listeye Dön</span></button>
            </div>
        </div>
        {/* Body */}
        <form onSubmit={handleSaveGallery}>
          {/* Gallery Details Form */}
          <input name="title" value={currentGallery?.title || ''} onChange={handleGalleryInputChange} placeholder="Galeri Başlığı" required />
          <textarea name="description" value={currentGallery?.description || ''} onChange={handleGalleryInputChange} placeholder="Açıklama" />
          <input name="link" value={currentGallery?.link || ''} onChange={handleGalleryInputChange} placeholder="İlgili Link (opsiyonel)" />
          <label><input type="checkbox" name="is_active" checked={currentGallery?.is_active || false} onChange={handleGalleryInputChange} /> Aktif/Yayında</label>
          <button type="submit" disabled={saving}>{saving ? 'Kaydediliyor...' : 'Galeriyi Kaydet'}</button>
        </form>

        {mode === 'edit' && (
          <div>
            <h3>Resimler</h3>
            <input type="file" multiple accept="image/*" onChange={handleImageUpload} disabled={imageUploading}/>
            {imageUploading && <p>Resimler yükleniyor...</p>}
            <DragDropContext onDragEnd={handleImageDragEnd}>
              <Droppable droppableId="images">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {galleryImages.map((image, index) => (
                      <Draggable key={image.id} draggableId={image.id.toString()} index={index}>
                        {(provided) => (
                          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                            <img src={image.image_url} alt={image.image_title || ''} style={{width: '100px'}}/>
                            <input 
                              type="text" 
                              value={image.image_title || ''} 
                              placeholder="Resim Başlığı"
                              onChange={(e) => {
                                const newImages = [...galleryImages];
                                newImages[index].image_title = e.target.value;
                                setGalleryImages(newImages);
                              }}
                              onBlur={(e) => handleUpdateImageTitle(image.id, e.target.value)}
                            />
                            <button onClick={() => handleDeleteImage(image.id, image.image_url)}><FiTrash2/></button>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        )}
    </div>
  );

  return (
    <>
      {mode === 'list' ? renderListMode() : renderEditAddMode()}
    </>
  );
};

export default GalleryManager; 