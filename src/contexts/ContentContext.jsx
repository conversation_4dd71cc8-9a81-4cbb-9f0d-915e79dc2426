import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useFaqs } from '../hooks/useFaqs';
import { useLinks } from '../hooks/useLinks';
import { useVideos } from '../hooks/useVideos';
import { useCatalog } from '../hooks/useCatalog';
import { usePodcasts } from '../hooks/usePodcasts';
import { useGallery } from '../hooks/useGallery';
import { useBlog } from '../hooks/useBlog';

// Context oluştur
const ContentContext = createContext();

// Action Types
const CONTENT_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_ACTIVE_CONTENT: 'SET_ACTIVE_CONTENT',
  REFRESH_CONTENT: 'REFRESH_CONTENT',
  SET_INITIALIZED: 'SET_INITIALIZED'
};

// Initial State
const initialState = {
  activeContent: null, // Hangi content type aktif
  loading: {
    faqs: false,
    links: false,
    videos: false,
    catalog: false,
    podcasts: false,
    gallery: false,
    blog: false
  },
  error: {
    faqs: null,
    links: null,
    videos: null,
    catalog: null,
    podcasts: null,
    gallery: null,
    blog: null
  },
  initialized: {
    faqs: false,
    links: false,
    videos: false,
    catalog: false,
    podcasts: false,
    gallery: false,
    blog: false
  },
  refreshTrigger: 0
};

// Reducer
function contentReducer(state, action) {
  switch (action.type) {
    case CONTENT_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.contentType]: action.loading
        }
      };
    
    case CONTENT_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: {
          ...state.error,
          [action.contentType]: action.error
        }
      };
    
    case CONTENT_ACTIONS.SET_ACTIVE_CONTENT:
      return {
        ...state,
        activeContent: action.contentType
      };
    
    case CONTENT_ACTIONS.REFRESH_CONTENT:
      return {
        ...state,
        refreshTrigger: state.refreshTrigger + 1
      };
    
    case CONTENT_ACTIONS.SET_INITIALIZED:
      return {
        ...state,
        initialized: {
          ...state.initialized,
          [action.contentType]: action.initialized
        }
      };
    
    default:
      return state;
  }
}

// Error Boundary Component
class ContentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ContentContext Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="content-error-boundary">
          <h2>İçerik yüklenirken bir hata oluştu</h2>
          <p>Sayfayı yenilemeyi deneyin.</p>
          <button onClick={() => window.location.reload()}>
            Sayfayı Yenile
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Content Provider Component
export function ContentProvider({ children }) {
  const { user } = useAuth();
  const [state, dispatch] = useReducer(contentReducer, initialState);
  
  // Initialize all hooks
  const faqsHook = useFaqs();
  const linksHook = useLinks();
  const videosHook = useVideos();
  const catalogHook = useCatalog();
  const podcastsHook = usePodcasts();
  const galleryHook = useGallery();
  const blogHook = useBlog();

  // Content hooks map - memoized
  const contentHooks = useMemo(() => ({
    faqs: faqsHook,
    links: linksHook,
    videos: videosHook,
    catalog: catalogHook,
    podcasts: podcastsHook,
    gallery: galleryHook,
    blog: blogHook
  }), [faqsHook, linksHook, videosHook, catalogHook, podcastsHook, galleryHook, blogHook]);

  // Update loading states - optimized
  useEffect(() => {
    Object.entries(contentHooks).forEach(([type, hook]) => {
      if (hook.loading !== state.loading[type]) {
        dispatch({
          type: CONTENT_ACTIONS.SET_LOADING,
          contentType: type,
          loading: hook.loading
        });
      }
      
      if (hook.error !== state.error[type]) {
        dispatch({
          type: CONTENT_ACTIONS.SET_ERROR,
          contentType: type,
          error: hook.error
        });
      }
    });
  }, [
    faqsHook.loading, faqsHook.error,
    linksHook.loading, linksHook.error,
    videosHook.loading, videosHook.error,
    catalogHook.loading, catalogHook.error,
    podcastsHook.loading, podcastsHook.error,
    galleryHook.loading, galleryHook.error,
    blogHook.loading, blogHook.error,
    state.loading, state.error
  ]);

  // Actions - memoized with useCallback
  const setActiveContent = useCallback((contentType) => {
    dispatch({
      type: CONTENT_ACTIONS.SET_ACTIVE_CONTENT,
      contentType
    });
  }, []);
  
  const refreshContent = useCallback(() => {
    dispatch({
      type: CONTENT_ACTIONS.REFRESH_CONTENT
    });
  }, []);
  
  const getContentHook = useCallback((contentType) => {
    return contentHooks[contentType];
  }, [contentHooks]);
  
  // Get all data - memoized
  const getAllData = useMemo(() => ({
    faqs: faqsHook.faqs || [],
    links: linksHook.links || [],
    videos: videosHook.videos || [],
    catalog: catalogHook.catalogs || [],
    podcasts: podcastsHook.podcasts || [],
    gallery: galleryHook.galleries || [],
    blog: {
      posts: blogHook.posts || [],
      categories: blogHook.categories || []
    }
  }), [
    faqsHook.faqs,
    linksHook.links,
    videosHook.videos,
    catalogHook.catalogs,
    podcastsHook.podcasts,
    galleryHook.galleries,
    blogHook.posts,
    blogHook.categories
  ]);
  
  // Get total counts - memoized
  const getTotalCounts = useMemo(() => ({
    faqs: faqsHook.faqs?.length || 0,
    links: linksHook.links?.length || 0,
    videos: videosHook.videos?.length || 0,
    catalog: catalogHook.catalogs?.length || 0,
    podcasts: podcastsHook.podcasts?.length || 0,
    gallery: galleryHook.galleries?.length || 0,
    blog: blogHook.posts?.length || 0
  }), [
    faqsHook.faqs?.length,
    linksHook.links?.length,
    videosHook.videos?.length,
    catalogHook.catalogs?.length,
    podcastsHook.podcasts?.length,
    galleryHook.galleries?.length,
    blogHook.posts?.length
  ]);
  
  // Get loading status - memoized
  const isLoading = useCallback((contentType) => {
    if (contentType) {
      return state.loading[contentType];
    }
    return Object.values(state.loading).some(loading => loading);
  }, [state.loading]);
  
  // Get error status - memoized
  const hasError = useCallback((contentType) => {
    if (contentType) {
      return !!state.error[contentType];
    }
    return Object.values(state.error).some(error => !!error);
  }, [state.error]);

  // Context value - memoized
  const value = useMemo(() => ({
    // State
    ...state,
    
    // Actions
    setActiveContent,
    refreshContent,
    getContentHook,
    getAllData,
    getTotalCounts,
    isLoading,
    hasError,
    
    // Content hooks (direct access)
    hooks: contentHooks,
    
    // User info
    user
  }), [
    state,
    setActiveContent,
    refreshContent,
    getContentHook,
    getAllData,
    getTotalCounts,
    isLoading,
    hasError,
    contentHooks,
    user
  ]);

  return (
    <ContentErrorBoundary>
      <ContentContext.Provider value={value}>
        {children}
      </ContentContext.Provider>
    </ContentErrorBoundary>
  );
}

// Custom hook to use content context
export function useContent() {
  const context = useContext(ContentContext);
  
  if (!context) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  
  return context;
}

// Specific content hooks with context integration - memoized
export function useContentFaqs() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.faqs,
    isLoading: isLoading('faqs'),
    hasError: hasError('faqs')
  }), [hooks.faqs, isLoading, hasError]);
}

export function useContentLinks() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.links,
    isLoading: isLoading('links'),
    hasError: hasError('links')
  }), [hooks.links, isLoading, hasError]);
}

export function useContentVideos() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.videos,
    isLoading: isLoading('videos'),
    hasError: hasError('videos')
  }), [hooks.videos, isLoading, hasError]);
}

export function useContentCatalog() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.catalog,
    isLoading: isLoading('catalog'),
    hasError: hasError('catalog')
  }), [hooks.catalog, isLoading, hasError]);
}

export function useContentPodcasts() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.podcasts,
    isLoading: isLoading('podcasts'),
    hasError: hasError('podcasts')
  }), [hooks.podcasts, isLoading, hasError]);
}

export function useContentGallery() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.gallery,
    isLoading: isLoading('gallery'),
    hasError: hasError('gallery')
  }), [hooks.gallery, isLoading, hasError]);
}

export function useContentBlog() {
  const { hooks, isLoading, hasError } = useContent();
  return useMemo(() => ({
    ...hooks.blog,
    isLoading: isLoading('blog'),
    hasError: hasError('blog')
  }), [hooks.blog, isLoading, hasError]);
} 