import { memo } from 'react';
import { FiSettings, FiHelpCircle, FiPlus, FiTrash2 } from 'react-icons/fi';

const FaqTab = memo(({ contentData, activeTab, handleContentChange, handleTabToggle, addContentItem, removeContentItem, updateContentItem }) => {
  const currentTab = contentData[activeTab];
  const tabColor = '#8b5cf6';

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: `${tabColor}20`, color: tabColor }}>
            <FiHelpCircle size={24} />
          </div>
          <div className="content-title">
            <h2>{currentTab.title}</h2>
            <p>{currentTab.description}</p>
          </div>
        </div>
        <div className="content-toggle">
          <button className={`toggle-switch ${currentTab.enabled ? 'active' : ''}`} onClick={() => handleTabToggle(activeTab)}>
            <div className="toggle-slider"></div>
            <span className="toggle-label">{currentTab.enabled ? 'Aktif' : 'Pasif'}</span>
          </button>
        </div>
      </div>

      <div className="content-body">
        <div className="content-settings-section">
          <h3><FiSettings size={20} /> Genel Ayarlar</h3>
          <div className="settings-grid">
            <div className="form-group">
              <label>Sekme Başlığı</label>
              <input type="text" value={currentTab.title} onChange={(e) => handleContentChange(activeTab, 'title', e.target.value)} placeholder="Sekme başlığını girin" />
            </div>
            <div className="form-group">
              <label>Açıklama</label>
              <textarea value={currentTab.description} onChange={(e) => handleContentChange(activeTab, 'description', e.target.value)} placeholder="Sekme açıklamasını girin" rows={3} />
            </div>
          </div>
        </div>

        {currentTab.enabled && (
          <div className="content-items-section">
            <div className="section-header">
              <h3><FiHelpCircle size={20} /> İçerikler</h3>
              <button className="add-content-btn" onClick={() => addContentItem(activeTab)}><FiPlus />Soru Ekle</button>
            </div>
            <div className="content-items-grid">
              {currentTab.items.map(item => (
                <div key={item.id} className="content-item-card">
                  <div className="item-header">
                    <div className="item-number">#{currentTab.items.indexOf(item) + 1}</div>
                    <button className="delete-item-btn" onClick={() => removeContentItem(activeTab, item.id)} title="Sil"><FiTrash2 /></button>
                  </div>
                  <div className="item-form">
                    <div className="form-group">
                      <label>Soru</label>
                      <input type="text" placeholder="Soru" value={item.title} onChange={(e) => updateContentItem(activeTab, item.id, 'title', e.target.value)} />
                    </div>
                    <div className="form-group">
                      <label>Cevap</label>
                      <textarea placeholder="Cevap" value={item.content} onChange={(e) => updateContentItem(activeTab, item.id, 'content', e.target.value)} rows={4} />
                    </div>
                  </div>
                </div>
              ))}
              {currentTab.items.length === 0 && (
                <div className="empty-state">
                  <div className="empty-icon"><FiHelpCircle size={48} /></div>
                  <h4>Henüz soru yok</h4>
                  <p>Bu sekme için ilk sorunuzu ekleyin</p>
                  <button className="add-first-btn" onClick={() => addContentItem(activeTab)}><FiPlus />İlk Soruyu Ekle</button>
                </div>
              )}
            </div>
          </div>
        )}

        {!currentTab.enabled && (
          <div className="disabled-state">
            <div className="disabled-icon"><FiHelpCircle size={48} /></div>
            <h4>SSS sekmesi pasif</h4>
            <p>Bu sekmeyi aktif hale getirmek için yukarıdaki toggle'ı kullanın</p>
            <button className="enable-btn" onClick={() => handleTabToggle(activeTab)}>Sekmeyi Aktif Et</button>
          </div>
        )}
      </div>
    </div>
  );
});

FaqTab.displayName = 'FaqTab';
export default FaqTab; 