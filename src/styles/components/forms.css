/* Form Components */

/* Form Groups */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.875rem;
}

.form-group label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Input Styles */
.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input:disabled,
.form-group textarea:disabled,
.form-group select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Error States */
.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Field Error Messages */
.field-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  color: var(--danger);
  font-size: 0.75rem;
  font-weight: 500;
}

.field-error svg {
  flex-shrink: 0;
  width: 14px;
  height: 14px;
}

/* Character Count */
.char-count {
  display: block;
  margin-top: var(--spacing-xs);
  color: var(--text-muted);
  font-size: 0.75rem;
  text-align: right;
}

/* Blog Specific Form Elements */
.blog-title-input {
  width: 100%;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
  transition: var(--transition);
}

.blog-title-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.blog-title-input.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.blog-excerpt-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: var(--transition);
}

.blog-excerpt-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.blog-excerpt-textarea.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Quill Editor Container */
.quill-container {
  border-radius: var(--radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.quill-container.error {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ql-editor {
  min-height: 200px;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.ql-toolbar {
  background: var(--bg-tertiary);
  border: none;
  border-bottom: 1px solid var(--border-color);
}

.ql-container {
  border: none;
  background: var(--bg-secondary);
}

/* Form Row Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing);
  }
}

/* Status Select */
.status-select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.status-select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Upload Indicators */
.upload-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  padding: 0.5rem 1rem;
  background: rgba(79, 70, 229, 0.1);
  border: 1px solid rgba(79, 70, 229, 0.2);
  border-radius: var(--radius);
  color: var(--accent-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

.upload-indicator .spin {
  animation: spin 1s linear infinite;
} 