import React, { createContext, useState, useEffect, useContext, useMemo, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { toast } from 'react-toastify';

// Context oluşturuyoruz
const SidebarContext = createContext();

// Custom hook for easy context access
export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

// Varsayılan sekme verileri
const defaultTabs = ['links', 'blog', 'gallery', 'video', 'faq', 'contact', 'corporate', 'catalog', 'podcast'];

// Varsayılan içerik verilerini oluştur
const createDefaultContentData = () => {
  const defaultContentData = {};
  defaultTabs.forEach(tabId => {
    defaultContentData[tabId] = {
      id: tabId,
      name: tabId.charAt(0).toUpperCase() + tabId.slice(1),
      description: '',
      enabled: true,
      tab_order: defaultTabs.indexOf(tabId)
    };
  });
  return defaultContentData;
};

export const SidebarProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [contentData, setContentData] = useState(createDefaultContentData());
  const [tabOrder, setTabOrder] = useState(defaultTabs);
  const [loading, setLoading] = useState(true);
  const [draggedTab, setDraggedTab] = useState(null);
  const [dropTarget, setDropTarget] = useState(null);
  const [dropPosition, setDropPosition] = useState(null);
  
  // Kullanıcı bilgilerini al ve sayfa yüklenirken veri yükle
  useEffect(() => {
    const getUser = async () => {
      try {
        // Önce yükleme durumunu başlat
        setLoading(true);
        console.log('SidebarContext: Getting user and loading data');
        
        // Kullanıcı bilgilerini al
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          console.log('SidebarContext: User found, ID:', user.id);
          setUser(user);

          try {
            // İçerik verilerini yükle
            console.log('SidebarContext: Starting loadContentData...');
            await loadContentData(user);
            console.log('SidebarContext: loadContentData completed');
          } catch (loadError) {
            console.error('SidebarContext: Error loading content data:', loadError);
            // Hata durumunda varsayılan değerler kullan
            setContentData(createDefaultContentData());
            setTabOrder([...defaultTabs]);
            toast.error('Veriler yüklenirken hata oluştu, varsayılan ayarlar kullanılıyor', {
              position: "top-right",
              autoClose: 3000
            });
          }
        } else {
          console.log('SidebarContext: No user found');
          // Kullanıcı yoksa varsayılan değerler kullan
          setContentData(createDefaultContentData());
          setTabOrder([...defaultTabs]);
        }
      } catch (error) {
        console.error('Error getting user:', error);
        // Hata durumunda varsayılan değerler kullan
        setContentData(createDefaultContentData());
        setTabOrder([...defaultTabs]);
      } finally {
        // Her durumda yükleme durumunu sonlandır
        console.log('SidebarContext: Setting loading to false in finally block');
        setLoading(false);
      }
    };
    
    getUser();
  }, []);
  
  // İçerik verilerini yükle ve localStorage'dan önbelleğe al
  const loadContentData = async (currentUser) => {
    if (!currentUser || !currentUser.id) {
      console.error('Invalid user object provided to loadContentData');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      console.log('Loading content data for user:', currentUser.id);

      // Cache kontrolü... (Mevcut kod doğru, dokunmuyoruz)
      try {
        const cachedData = localStorage.getItem(`sidebar_data_${currentUser.id}`);
        const cachedTimestamp = localStorage.getItem(`sidebar_timestamp_${currentUser.id}`);
        const now = Date.now();
        if (cachedData && cachedTimestamp && (now - parseInt(cachedTimestamp)) < 30 * 60 * 1000) {
          const parsedData = JSON.parse(cachedData);
          if (parsedData && parsedData.contentData) {
            console.log('Using cached sidebar data');
            setContentData(parsedData.contentData);
            setTabOrder(parsedData.tabOrder || []);
            setLoading(false);
            return;
          }
        }
      } catch (cacheError) {
        console.error('Error reading from cache:', cacheError);
      }

      console.log('Fetching sidebar data from database');
      const { data, error } = await supabase.from('content_navigation').select('*').eq('user_id', currentUser.id);

      if (error) throw error;

      const newContentData = createDefaultContentData();
      let dbTabOrder = [];

      if (data && data.length > 0) {
        data.forEach(item => {
          if (item && item.tab_id && newContentData[item.tab_id]) {
            // --- SORUNUN KÖKÜNÜ ÇÖZEN KISIM ---
            // 'enabled' değerini her zaman doğru bir boolean'a çevir
            const value = item.enabled;
            let isEnabled;
            if (value === undefined || value === null) {
              isEnabled = true; // Varsayılan
            } else if (typeof value === 'boolean') {
              isEnabled = value; // Zaten boolean
            } else if (typeof value === 'string') {
              isEnabled = value.toLowerCase() === 'true' || value === '1';
            } else if (typeof value === 'number') {
              isEnabled = value !== 0;
            } else {
              isEnabled = false; // Güvenli varsayılan
            }
            // --- ÇÖZÜM SONU ---

            newContentData[item.tab_id].enabled = isEnabled;
            newContentData[item.tab_id].description = item.description || '';
            newContentData[item.tab_id].tab_order = item.tab_order;
          }
        });
        dbTabOrder = data.sort((a, b) => a.tab_order - b.tab_order).map(t => t.tab_id);
      }

      const finalTabOrder = [...new Set([...dbTabOrder, ...defaultTabs])];

      setContentData(newContentData);
      setTabOrder(finalTabOrder);

      localStorage.setItem(`sidebar_data_${currentUser.id}`, JSON.stringify({ contentData: newContentData, tabOrder: finalTabOrder }));
      localStorage.setItem(`sidebar_timestamp_${currentUser.id}`, Date.now().toString());

    } catch (error) {
      console.error('SidebarContext: Error in loadContentData:', error);
      toast.error('Veriler yüklenirken bir hata oluştu.');
      // Hata durumunda bile varsayılan state'ler zaten ayarlı
    } finally {
      console.log('SidebarContext: loadContentData finally block - setting loading to false');
      setLoading(false);
    }
  };
  
  // Tab toggle işlevi - memoize edilmiş
  // Stale state sorununu önlemek için useCallback kaldırıldı.
  // Bu, fonksiyonun her zaman en güncel state'i yakalamasını sağlar.
  const handleTabToggle = (tabId) => {
    toggleFunction(tabId);
  };
  
  const toggleFunction = (tabId) => {
    if (draggedTab) return;
    
    try {
      // tabId kontrolü - geçersiz tabId ile çağrılırsa işlem yapma
      if (!tabId) {
        console.error('Invalid tabId');
        return;
      }
      
      // contentData her zaman varsayılan değerlerle doldurulmuş olmalı
      // Ancak yine de kontrol edelim
      if (!contentData || Object.keys(contentData).length === 0) {
        console.warn('ContentData is empty, using default values');
        setContentData(createDefaultContentData());
        setTabOrder([...defaultTabs]);
        
        toast.info('Veriler hazırlanıyor...', {
          position: "top-right",
          autoClose: 1000
        });
        
        // Kısa bir gecikme ile tekrar deneyelim
        setTimeout(() => toggleFunction(tabId), 100);
        return;
      }
      
      // Belirtilen tabId mevcut değilse, varsayılan değerleri ekleyelim
      if (!contentData[tabId]) {
        console.warn(`Tab ID ${tabId} not found in contentData, adding default values`);
        
        // contentData'ya eksik tab'i ekle
        setContentData(prev => ({
          ...prev,
          [tabId]: {
            id: tabId,
            name: tabId.charAt(0).toUpperCase() + tabId.slice(1),
            description: '',
            enabled: true,
            tab_order: defaultTabs.indexOf(tabId)
          }
        }));
        
        // Kısa bir gecikme ile tekrar deneyelim
        setTimeout(() => toggleFunction(tabId), 100);
        return;
      }
      
      // loadContentData sayesinde 'enabled' değerinin her zaman boolean olduğunu varsayabiliriz.
      // Bu yüzden karmaşık kontrollere gerek kalmadı.
      const currentEnabledState = contentData[tabId].enabled;
      const newEnabledState = !currentEnabledState;

      console.log(`Toggling ${tabId}. Current: ${currentEnabledState}, New: ${newEnabledState}`);
      
      const tabName = contentData[tabId].name || tabId.charAt(0).toUpperCase() + tabId.slice(1);
      
      if (currentEnabledState === newEnabledState) return;
      
      // UI'yi hemen güncelle
      setContentData(prev => {
        if (!prev || !prev[tabId]) return prev;
        
        const newData = {
          ...prev,
          [tabId]: {
            ...prev[tabId],
            enabled: newEnabledState
          }
        };
        
        // localStorage'ı güncelle
        try {
          if (user?.id) {
            const cacheData = {
              contentData: newData,
              tabOrder: tabOrder
            };
            localStorage.setItem(`sidebar_data_${user.id}`, JSON.stringify(cacheData));
            localStorage.setItem(`sidebar_timestamp_${user.id}`, Date.now().toString());
          }
        } catch (e) {
          console.error('LocalStorage error:', e);
        }
        
        return newData;
      });
      
      // Bildirim göster
      toast.success(`${tabName} ${newEnabledState ? 'aktif' : 'pasif'} yapıldı`, {
        position: "top-right",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true
      });
      
      // Veritabanını arka planda güncelle
      if (!user?.id) return;
      
      console.log(`Updating database for tab ${tabId}, setting enabled to:`, newEnabledState);
      
      // Doğrudan güncelleme yap - debounce kaldırıldı
      supabase
        .from('content_navigation')
        .upsert({
          user_id: user.id,
          tab_id: tabId,
          description: contentData[tabId]?.description || '',
          // Kesinlikle boolean olarak gönder
          enabled: newEnabledState === true,
          tab_order: tabOrder.indexOf(tabId) >= 0 ? tabOrder.indexOf(tabId) : 999
        }, {
          onConflict: 'user_id,tab_id'
        })
        .then(({ data, error }) => {
          if (error) {
            console.error('Error updating database:', error);
            toast.error(`${tabName} durumu güncellenirken hata oluştu`, {
              position: "top-right",
              autoClose: 3000,
            });
            
            // Hata durumunda state'i geri al
            setContentData(prev => {
              if (!prev || !prev[tabId]) return prev;
              
              const rollbackData = {
                ...prev,
                [tabId]: {
                  ...prev[tabId],
                  enabled: currentEnabledState
                }
              };
              
              // localStorage'ı da geri al
              try {
                if (user?.id) {
                  localStorage.setItem(`sidebar_data_${user.id}`, JSON.stringify({
                    contentData: rollbackData,
                    tabOrder
                  }));
                }
              } catch (e) {
                console.error('LocalStorage rollback error:', e);
              }
              
              return rollbackData;
            });
          } else {
            console.log(`Database updated successfully for tab ${tabId}, enabled:`, newEnabledState);
          }
        });
    } catch (error) {
      console.error('Error toggling tab:', error);
      toast.error('İşlem sırasında bir hata oluştu', {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };
  
  // Drag and drop işlevleri
  const handleDragStart = useCallback((e, tabId) => {
    setDraggedTab(tabId);
    e.dataTransfer.effectAllowed = 'move';
    
    const element = e.currentTarget;
    e.dataTransfer.setData('text/plain', tabId);
    
    try {
      const clone = element.cloneNode(true);
      clone.style.width = `${element.offsetWidth}px`;
      clone.style.opacity = '0.6';
      clone.style.position = 'absolute';
      clone.style.top = '-1000px';
      document.body.appendChild(clone);
      
      e.dataTransfer.setDragImage(clone, 20, 20);
      
      setTimeout(() => {
        document.body.removeChild(clone);
      }, 0);
    } catch (err) {
      const dragImage = new Image();
      dragImage.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=';
      e.dataTransfer.setDragImage(dragImage, 0, 0);
    }
  }, []);
  
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    if (!draggedTab) return;
    
    const target = e.currentTarget;
    const targetId = target.dataset.tabId;
    
    if (targetId === draggedTab) {
      setDropTarget(null);
      return;
    }
    
    const rect = target.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const position = y < rect.height / 2 ? 'before' : 'after';
    
    setDropTarget(targetId);
    setDropPosition(position);
  }, [draggedTab]);
  
  const handleDragLeave = useCallback(() => {
    setDropTarget(null);
    setDropPosition(null);
  }, []);
  
  const handleDrop = useCallback((e, targetId) => {
    e.preventDefault();
    
    if (!draggedTab || !dropTarget || draggedTab === targetId) {
      setDraggedTab(null);
      setDropTarget(null);
      setDropPosition(null);
      return;
    }
    
    // Yeni sekme sırasını oluştur
    const newTabOrder = [...tabOrder];
    const draggedIndex = newTabOrder.indexOf(draggedTab);
    let targetIndex = newTabOrder.indexOf(targetId);
    
    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedTab(null);
      setDropTarget(null);
      setDropPosition(null);
      return;
    }
    
    // Sürüklenen öğeyi geçici olarak kaldır
    newTabOrder.splice(draggedIndex, 1);
    
    // Hedef pozisyona göre yeni indeksi belirle
    if (dropPosition === 'after') {
      targetIndex = newTabOrder.indexOf(targetId) + 1;
    } else {
      targetIndex = newTabOrder.indexOf(targetId);
    }
    
    // Sürüklenen öğeyi yeni konumuna ekle
    newTabOrder.splice(targetIndex, 0, draggedTab);
    
    // State'i güncelle
    setTabOrder(newTabOrder);
    
    // localStorage'ı güncelle
    const cachedData = localStorage.getItem(`sidebar_data_${user.id}`);
    if (cachedData && user) {
      const parsedData = JSON.parse(cachedData);
      parsedData.tabOrder = newTabOrder;
      localStorage.setItem(`sidebar_data_${user.id}`, JSON.stringify(parsedData));
      localStorage.setItem(`sidebar_timestamp_${user.id}`, Date.now().toString());
    }
    
    // Veritabanını güncelle
    if (user) {
      const updatePromises = newTabOrder.map((tabId, index) => {
        return supabase
          .from('content_navigation')
          .upsert({
            user_id: user.id,
            tab_id: tabId,
            tab_order: index,
            enabled: contentData[tabId]?.enabled || true,
            description: contentData[tabId]?.description || ''
          }, {
            onConflict: 'user_id,tab_id'
          });
      });
      
      Promise.all(updatePromises).catch(error => {
        console.error('Error updating tab order:', error);
      });
    }
    
    // Temizle
    setDraggedTab(null);
    setDropTarget(null);
    setDropPosition(null);
  }, [draggedTab, dropTarget, dropPosition, tabOrder, contentData, user]);
  
  const handleDragEnd = useCallback(() => {
    setDraggedTab(null);
    setDropTarget(null);
    setDropPosition(null);
  }, []);
  
  // Context value
  const value = useMemo(() => ({
    user,
    contentData,
    tabOrder,
    loading,
    draggedTab,
    dropTarget,
    dropPosition,
    setDraggedTab,
    setDropTarget,
    setDropPosition,
    handleTabToggle,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    refreshData: () => user && loadContentData(user)
  }), [
    user, contentData, tabOrder, loading, draggedTab, dropTarget, dropPosition,
    handleTabToggle, handleDragStart, handleDragOver, handleDragLeave, handleDrop, handleDragEnd
  ]);
  
  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  );
};

export default SidebarContext;
