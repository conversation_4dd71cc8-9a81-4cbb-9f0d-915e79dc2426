import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { FiSave, FiArrowLeft, FiUser, FiMail, FiClock, FiCheckCircle, FiXCircle } from 'react-icons/fi';

const EditUser = () => {
    const { userId } = useParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [user, setUser] = useState(null);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    const [formData, setFormData] = useState({
        full_name: '',
        username: '',
        profession: '',
        role: 'user',
        membership_status: 'active',
        membership_expires_at: ''
    });

    useEffect(() => {
        const fetchUser = async () => {
            try {
                setLoading(true);
                setError(null);

                const { data, error } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', userId)
                    .single();

                if (error) throw error;

                if (data) {
                    setUser(data);
                    setFormData({
                        full_name: data.full_name || '',
                        username: data.username || '',
                        profession: data.profession || '',
                        role: data.role || 'user',
                        membership_status: data.membership_status || 'active',
                        membership_expires_at: data.membership_expires_at ? 
                            new Date(data.membership_expires_at).toISOString().split('T')[0] : ''
                    });
                } else {
                    setError('Kullanıcı bulunamadı');
                }
            } catch (error) {
                console.error('Kullanıcı çekme hatası:', error);
                setError(`Kullanıcı bilgileri yüklenirken hata oluştu: ${error.message}`);
            } finally {
                setLoading(false);
            }
        };

        if (userId) {
            fetchUser();
        }
    }, [userId]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);
        setError(null);
        setSuccess(false);

        try {
            const updateData = {
                full_name: formData.full_name,
                username: formData.username,
                profession: formData.profession,
                role: formData.role,
                membership_status: formData.membership_status,
                membership_expires_at: formData.membership_expires_at ? 
                    new Date(formData.membership_expires_at).toISOString() : null,
                updated_at: new Date().toISOString()
            };

            const { error } = await supabase
                .from('profiles')
                .update(updateData)
                .eq('id', userId);

            if (error) throw error;

            setSuccess(true);
            setTimeout(() => {
                navigate('/admin/users');
            }, 2000);

        } catch (error) {
            console.error('Kullanıcı güncelleme hatası:', error);
            setError(`Kullanıcı güncellenirken hata oluştu: ${error.message}`);
        } finally {
            setSaving(false);
        }
    };

    const getMembershipStatusIcon = (status) => {
        switch (status) {
            case 'active':
                return <FiCheckCircle style={{ color: '#22c55e' }} />;
            case 'expired':
                return <FiXCircle style={{ color: '#ef4444' }} />;
            case 'suspended':
                return <FiClock style={{ color: '#f59e0b' }} />;
            default:
                return <FiCheckCircle style={{ color: '#22c55e' }} />;
        }
    };

    const getExpiryWarning = () => {
        if (!formData.membership_expires_at) return null;
        
        const now = new Date();
        const expiry = new Date(formData.membership_expires_at);
        const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
        
        if (daysUntilExpiry < 0) {
            return { text: `${Math.abs(daysUntilExpiry)} gün önce süresi bitmiş`, color: '#ef4444' };
        } else if (daysUntilExpiry <= 30) {
            return { text: `${daysUntilExpiry} gün kaldı`, color: '#f59e0b' };
        } else {
            return { text: `${daysUntilExpiry} gün kaldı`, color: '#22c55e' };
        }
    };

    if (loading) {
        return (
            <div className="admin-loading">
                <div className="loading-spinner"></div>
                <p>Kullanıcı bilgileri yükleniyor...</p>
            </div>
        );
    }

    if (error && !user) {
        return (
            <div className="edit-user-container">
                <div className="error-state">
                    <h2>Hata</h2>
                    <p>{error}</p>
                    <button onClick={() => navigate('/admin/users')} className="btn-secondary">
                        <FiArrowLeft />
                        Kullanıcı Listesine Dön
                    </button>
                </div>
            </div>
        );
    }

    const expiryWarning = getExpiryWarning();

    return (
        <div className="edit-user-container">
            <div className="edit-user-header">
                <button onClick={() => navigate('/admin/users')} className="btn-back">
                    <FiArrowLeft />
                    Geri
                </button>
                <div>
                    <h2>Kullanıcı Düzenle</h2>
                    <p>Kullanıcı bilgilerini ve üyelik durumunu güncelleyin</p>
                </div>
            </div>

            {error && (
                <div className="alert alert-error">
                    <FiXCircle />
                    <span>{error}</span>
                </div>
            )}

            {success && (
                <div className="alert alert-success">
                    <FiCheckCircle />
                    <span>Kullanıcı başarıyla güncellendi! Ana sayfaya yönlendiriliyorsunuz...</span>
                </div>
            )}

            <div className="edit-user-form-container">
                <form onSubmit={handleSubmit} className="edit-user-form">
                    <div className="form-section">
                        <h3>
                            <FiUser />
                            Temel Bilgiler
                        </h3>
                        
                        <div className="form-group">
                            <label htmlFor="full_name">Ad Soyad</label>
                            <input
                                type="text"
                                id="full_name"
                                name="full_name"
                                value={formData.full_name}
                                onChange={handleInputChange}
                                placeholder="Kullanıcının adı ve soyadı"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="username">Kullanıcı Adı</label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                value={formData.username}
                                onChange={handleInputChange}
                                placeholder="Kullanıcı adı"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="profession">Meslek / Unvan</label>
                            <input
                                type="text"
                                id="profession"
                                name="profession"
                                value={formData.profession}
                                onChange={handleInputChange}
                                placeholder="Örn: Yazılım Geliştirici, Doktor, Öğretmen"
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="role">Rol</label>
                            <select
                                id="role"
                                name="role"
                                value={formData.role}
                                onChange={handleInputChange}
                                required
                            >
                                <option value="user">Kullanıcı</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>
                            <FiClock />
                            Üyelik Bilgileri
                        </h3>

                        <div className="form-group">
                            <label htmlFor="membership_status">
                                Üyelik Durumu
                                {getMembershipStatusIcon(formData.membership_status)}
                            </label>
                            <select
                                id="membership_status"
                                name="membership_status"
                                value={formData.membership_status}
                                onChange={handleInputChange}
                                required
                            >
                                <option value="active">Aktif</option>
                                <option value="expired">Süresi Bitmiş</option>
                                <option value="suspended">Askıya Alınmış</option>
                            </select>
                        </div>

                        <div className="form-group">
                            <label htmlFor="membership_expires_at">Üyelik Bitiş Tarihi</label>
                            <input
                                type="date"
                                id="membership_expires_at"
                                name="membership_expires_at"
                                value={formData.membership_expires_at}
                                onChange={handleInputChange}
                                min={new Date().toISOString().split('T')[0]}
                            />
                            {expiryWarning && (
                                <div className="form-hint" style={{ color: expiryWarning.color }}>
                                    {expiryWarning.text}
                                </div>
                            )}
                            <div className="form-hint">
                                Boş bırakırsanız süresiz üyelik olur
                            </div>
                        </div>
                    </div>

                    <div className="form-actions">
                        <button 
                            type="button" 
                            onClick={() => navigate('/admin/users')}
                            className="btn-secondary"
                            disabled={saving}
                        >
                            İptal
                        </button>
                        <button 
                            type="submit" 
                            className="btn-primary"
                            disabled={saving}
                        >
                            {saving ? (
                                <>
                                    <div className="loading-spinner-small"></div>
                                    Kaydediliyor...
                                </>
                            ) : (
                                <>
                                    <FiSave />
                                    Kaydet
                                </>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditUser; 