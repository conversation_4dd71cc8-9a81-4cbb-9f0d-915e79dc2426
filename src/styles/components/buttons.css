/* Button Components */

/* Primary Buttons */
.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Secondary Buttons */
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--text-muted);
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Blog Editor Buttons */
.blog-publish-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.blog-publish-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, #059669, #047857);
}

.blog-publish-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.blog-publish-btn svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.btn-draft {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.btn-draft:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--text-muted);
}

.btn-draft svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

/* Icon Buttons */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  transition: var(--transition);
  cursor: pointer;
}

.btn-icon:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--text-muted);
}

.btn-icon.edit {
  color: var(--accent-primary);
}

.btn-icon.edit:hover {
  background: rgba(79, 70, 229, 0.1);
  border-color: var(--accent-primary);
}

/* Danger Buttons */
.btn-delete, .btn-danger {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  background: var(--danger);
  color: white;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.btn-delete:hover, .btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Upload Buttons */
.btn-upload {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.625rem 1.25rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.btn-upload:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--text-muted);
}

/* Loading States */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
} 