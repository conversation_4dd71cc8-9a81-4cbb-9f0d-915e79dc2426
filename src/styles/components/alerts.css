/* Alert Component Styles */

.alert-component {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1000;
  max-width: 400px;
  pointer-events: none;
}

.alert {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--spacing);
  margin-bottom: var(--spacing);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  animation: slideInRight 0.3s ease-out;
  pointer-events: auto;
  border-left: 4px solid var(--accent-primary);
}

.alert.success {
  border-left-color: var(--success);
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.alert.error {
  border-left-color: var(--danger);
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert.warning {
  border-left-color: var(--warning);
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
}

.alert.info {
  border-left-color: var(--info);
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.alert-icon {
  flex-shrink: 0;
  font-size: 1.125rem;
}

.alert.success .alert-icon {
  color: var(--success);
}

.alert.error .alert-icon {
  color: var(--danger);
}

.alert.warning .alert-icon {
  color: var(--warning);
}

.alert.info .alert-icon {
  color: var(--info);
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.alert-message {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.8125rem;
  line-height: 1.4;
}

.alert-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-close:hover {
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.05);
}

.alert-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: currentColor;
  opacity: 0.3;
  animation: alertProgress 4s linear;
}

.alert.success .alert-progress {
  background: var(--success);
}

.alert.error .alert-progress {
  background: var(--danger);
}

.alert.warning .alert-progress {
  background: var(--warning);
}

.alert.info .alert-progress {
  background: var(--info);
}

/* Modern Alert Styles */
.modern-alert {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing);
  border-radius: var(--radius);
  border: 1px solid;
  margin-bottom: var(--spacing);
  animation: slideInDown 0.3s ease-out;
}

.modern-alert .alert-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.modern-alert .alert-icon {
  flex-shrink: 0;
}

.modern-alert .alert-text {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

.modern-alert .alert-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-alert .alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.alert-success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes alertProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.alert.exit {
  animation: slideOutRight 0.3s ease-in forwards;
}

/* Multiple Alerts Stacking */
.alert:not(:last-child) {
  margin-bottom: var(--spacing);
}

/* Responsive Design */
@media (max-width: 768px) {
  .alert-component {
    top: var(--spacing);
    right: var(--spacing);
    left: var(--spacing);
    max-width: none;
  }

  .alert {
    margin-bottom: var(--spacing-sm);
  }

  .modern-alert {
    margin-bottom: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .alert-component {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
  }

  .alert {
    padding: var(--spacing-sm);
  }

  .modern-alert {
    padding: var(--spacing-sm);
  }

  .alert-title {
    font-size: 0.8125rem;
  }

  .alert-message,
  .alert-text {
    font-size: 0.75rem;
  }
} 