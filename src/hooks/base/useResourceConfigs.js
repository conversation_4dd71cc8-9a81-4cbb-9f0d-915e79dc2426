/**
 * Resource configurations for different content types
 * Her content type için spesifik konfigürasyon ayarları
 */

// Links (Portfolio) Configuration
export const linksConfig = {
  enableOrder: true,
  orderBy: 'display_order',
  orderDirection: 'asc',
  
  messages: {
    fetchError: 'Bağlantılar yüklenirken bir hata oluştu.',
    createSuccess: 'Bağlantı başarıyla oluşturuldu.',
    createError: 'Bağlantı oluşturulurken bir hata oluştu.',
    updateSuccess: 'Bağlantı başarıyla güncellendi.',
    updateError: 'Bağlantı güncellenirken bir hata oluştu.',
    deleteSuccess: 'Bağlantı başarıyla silindi.',
    deleteError: 'Bağlantı silinirken bir hata oluştu.',
  },
  
  storage: {
    bucket: 'portfolio',
    path: 'link_icons'
  }
};

// FAQ Configuration
export const faqConfig = {
  enableOrder: false,
  orderBy: 'created_at',
  orderDirection: 'desc',
  
  messages: {
    fetchError: 'SSS yüklenirken bir hata oluştu.',
    createSuccess: 'SSS başarıyla oluşturuldu.',
    createError: 'SSS oluşturulurken bir hata oluştu.',
    updateSuccess: 'SSS başarıyla güncellendi.',
    updateError: 'SSS güncellenirken bir hata oluştu.',
    deleteSuccess: 'SSS başarıyla silindi.',
    deleteError: 'SSS silinirken bir hata oluştu.',
  }
};

// Blog Configuration
export const blogConfig = {
  tableName: 'blog_posts',
  enableOrder: true,
  enableUserFilter: true,
  storage: {
    bucket: 'blog-images',
    path: 'images',
    featuredImageField: 'featured_image'
  },
  fields: {
    displayOrder: 'display_order'
  },
  relations: [
    {
      table: 'blog_post_categories',
      field: 'blog_post_categories',
      foreignKey: 'post_id',
      nested: {
        table: 'blog_categories',
        field: 'blog_categories',
        foreignKey: 'category_id'
      }
    }
  ],
  beforeCreate: async (data) => {
    // Slug oluştur
    if (data.title && !data.slug) {
      data.slug = data.title
        .toLowerCase()
        .replace(/[çğıöşü]/g, char => {
          const map = { 'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ö': 'o', 'ş': 's', 'ü': 'u' };
          return map[char] || char;
        })
        .replace(/[^a-z0-9\s-]/g, '')
        .trim()
        .replace(/\s+/g, '-');
    }
    return data;
  },
  customMethods: {
    // Blog özel metodları için yer tutucu
    uploadImage: true,
    createCategory: true,
    updateCategory: true,
    deleteCategory: true,
    linkCategoriesToPost: true
  }
};

// Video Configuration
export const videoConfig = {
  tableName: 'video_items',
  enableOrder: true,
  enableUserFilter: true,
  storage: {
    bucket: 'video-thumbnails',
    path: 'thumbnails',
    thumbnailField: 'thumbnail_url'
  },
  fields: {
    displayOrder: 'display_order'
  },
  multipleFileFields: ['thumbnail_url']
};

// Gallery Configuration
export const galleryConfig = {
  tableName: 'gallery_albums',
  enableOrder: true,
  enableUserFilter: true,
  fields: {
    displayOrder: 'album_order'
  },
  relations: [
    {
      table: 'gallery_images',
      field: 'gallery_images',
      foreignKey: 'album_id',
      count: true
    }
  ],
  storage: {
    bucket: 'gallery-images',
    path: 'images'
  },
  customMethods: {
    // Gallery özel metodları için yer tutucu
    uploadImage: true,
    deleteImage: true,
    updateImageOrder: true
  }
};

// Catalog Configuration
export const catalogConfig = {
  tableName: 'catalog_items',
  enableOrder: true,
  enableUserFilter: true,
  storage: {
    bucket: 'catalog-pdfs',
    path: 'files',
    pdfField: 'pdf_url',
    coverField: 'cover_image_url'
  },
  fields: {
    displayOrder: 'display_order'
  },
  multipleFileFields: ['pdf_url', 'cover_image_url'],
  beforeCreate: async (data, files) => {
    // PDF dosyası gerekli
    if (!files.pdf) {
      throw new Error('PDF dosyası gerekli');
    }
    return data;
  }
};

// Podcast Configuration
export const podcastConfig = {
  tableName: 'podcast_items',
  enableOrder: true,
  enableUserFilter: true,
  storage: {
    bucket: 'podcast-audio',
    path: 'files',
    audioField: 'audio_url',
    coverField: 'cover_image_url'
  },
  fields: {
    displayOrder: 'display_order'
  },
  multipleFileFields: ['audio_url', 'cover_image_url'],
  beforeCreate: async (data, files) => {
    // Audio dosyası gerekli
    if (!files.audio) {
      throw new Error('Audio dosyası gerekli');
    }
    return data;
  }
}; 