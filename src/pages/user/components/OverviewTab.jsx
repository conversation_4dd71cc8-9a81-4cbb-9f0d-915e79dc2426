import React from 'react';
import { 
  FiGrid, FiFileText, FiImage, FiVideo, 
  FiHelpCircle, FiPhone, FiUsers, FiBook, 
  FiHeadphones, FiTrendingUp, FiEye, FiEdit,
  FiPlus, FiArrowRight
} from 'react-icons/fi';

const OverviewTab = ({ contentData, tabOrder, navigate }) => {
  // İstatistikleri hesapla
  const getStats = () => {
    const stats = {
      totalTabs: tabOrder.length,
      activeTabs: tabOrder.filter(tabId => contentData[tabId]?.enabled).length,
      totalContent: 0,
      recentlyUpdated: 0
    };

    // Her sekmedeki içerik sayısını hesapla
    tabOrder.forEach(tabId => {
      const tab = contentData[tabId];
      if (tab?.items) {
        stats.totalContent += tab.items.length;
      }
    });

    return stats;
  };

  const stats = getStats();

  // <PERSON> güncellenmiş içerikleri bul
  const getRecentContent = () => {
    const recentItems = [];
    
    tabOrder.forEach(tabId => {
      const tab = contentData[tabId];
      if (tab?.items && tab.items.length > 0) {
        tab.items.slice(0, 3).forEach(item => {
          recentItems.push({
            id: item.id,
            title: item.title || item.question || item.name || 'Başlıksız',
            tab: tabId,
            tabName: getTabName(tabId),
            icon: getTabIcon(tabId),
            updatedAt: item.updatedAt || item.createdAt || new Date().toISOString()
          });
        });
      }
    });

    return recentItems
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, 6);
  };

  const getTabName = (tabId) => {
    const tabNames = {
      links: 'Bağlantılar',
      blog: 'Blog',
      gallery: 'Galeri',
      multimedia: 'Video',
      faq: 'SSS',
      contact: 'İletişim',
      corporate: 'Kurumsal',
      catalog: 'Katalog',
      podcast: 'Podcast'
    };
    return tabNames[tabId] || tabId;
  };

  const getTabIcon = (tabId) => {
    const icons = {
      links: FiGrid,
      blog: FiFileText,
      gallery: FiImage,
      multimedia: FiVideo,
      faq: FiHelpCircle,
      contact: FiPhone,
      corporate: FiUsers,
      catalog: FiBook,
      podcast: FiHeadphones
    };
    return icons[tabId] || FiGrid;
  };

  const recentContent = getRecentContent();

  const quickActions = [
    {
      title: 'Blog Yazısı Ekle',
      description: 'Yeni bir blog yazısı oluştur',
      icon: FiFileText,
      action: () => navigate('/dashboard/blogs/add'),
      color: '#3b82f6'
    },
    {
      title: 'Bağlantı Ekle',
      description: 'Yeni bir bağlantı ekle',
      icon: FiGrid,
      action: () => navigate('/dashboard/links/add'),
      color: '#8b5cf6'
    },
    {
      title: 'Fotoğraf Yükle',
      description: 'Galeriye fotoğraf ekle',
      icon: FiImage,
      action: () => {
        // Gallery add sayfasına git
      },
      color: '#ec4899'
    },
    {
      title: 'Video Ekle',
      description: 'Video içeriği ekle',
      icon: FiVideo,
      action: () => {
        // Multimedia add sayfasına git
      },
      color: '#10b981'
    }
  ];

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <FiTrendingUp size={24} color="white" />
          </div>
          <div className="content-title">
            <h2>Genel Bakış</h2>
            <p>İçerik yönetimi istatistikleriniz ve hızlı erişim</p>
          </div>
        </div>
      </div>

      <div className="content-body">
        <div className="overview-content">
          {/* İstatistikler */}
          <div className="overview-header">
            <div className="overview-title">
              <h2>İçerik Özeti</h2>
              <p>Profil sayfanızdaki içeriklerin genel durumu</p>
            </div>
          </div>

          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon" style={{ backgroundColor: '#3b82f6' }}>
                <FiGrid size={24} color="white" />
              </div>
              <div className="stat-content">
                <h3>{stats.totalTabs}</h3>
                <p>Toplam Sekme</p>
              </div>
              <div className="stat-desc">Mevcut sekme sayısı</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon" style={{ backgroundColor: '#10b981' }}>
                <FiEye size={24} color="white" />
              </div>
              <div className="stat-content">
                <h3>{stats.activeTabs}</h3>
                <p>Aktif Sekme</p>
              </div>
              <div className="stat-desc">Yayında olan sekmeler</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon" style={{ backgroundColor: '#f59e0b' }}>
                <FiEdit size={24} color="white" />
              </div>
              <div className="stat-content">
                <h3>{stats.totalContent}</h3>
                <p>Toplam İçerik</p>
              </div>
              <div className="stat-desc">Tüm sekmeler toplam</div>
            </div>

            <div className="stat-card">
              <div className="stat-icon" style={{ backgroundColor: '#8b5cf6' }}>
                <FiTrendingUp size={24} color="white" />
              </div>
              <div className="stat-content">
                <h3>%{Math.round((stats.activeTabs / stats.totalTabs) * 100)}</h3>
                <p>Aktiflik Oranı</p>
              </div>
              <div className="stat-desc">Sekmelerinizin aktiflik durumu</div>
            </div>
          </div>

          {/* Son İçerikler */}
          <div className="recent-content-section">
            <div className="section-header">
              <h3>Son İçerikler</h3>
              <p>En son eklediğiniz veya güncellediğiniz içerikler</p>
            </div>

            {recentContent.length > 0 ? (
              <div className="recent-items-grid">
                {recentContent.map((item, index) => {
                  const IconComponent = item.icon;
                  return (
                    <div key={index} className="recent-item-card">
                      <div className="recent-item-header">
                        <div className="recent-item-icon">
                          <IconComponent size={20} />
                        </div>
                        <div className="recent-item-info">
                          <h4>{item.title}</h4>
                          <span className="recent-item-tab">{item.tabName}</span>
                        </div>
                      </div>
                      <div className="recent-item-content">
                        <p>Son güncellenme: {new Date(item.updatedAt).toLocaleDateString('tr-TR')}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="empty-state">
                <div className="empty-icon">
                  <FiEdit size={48} />
                </div>
                <h4>Henüz içerik yok</h4>
                <p>İlk içeriğinizi oluşturmaya başlayın</p>
              </div>
            )}
          </div>

          {/* Hızlı Aksiyonlar */}
          <div className="quick-actions-section">
            <div className="section-header">
              <h3>Hızlı Aksiyonlar</h3>
              <p>Sık kullanılan işlemlere hızlı erişim</p>
            </div>

            <div className="quick-actions-grid">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <div 
                    key={index} 
                    className="quick-action-card"
                    onClick={action.action}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="quick-action-icon" style={{ backgroundColor: action.color }}>
                      <IconComponent size={24} color="white" />
                    </div>
                    <div className="quick-action-content">
                      <h3>{action.title}</h3>
                      <p>{action.description}</p>
                    </div>
                    <div className="action-arrow">
                      <FiArrowRight size={16} />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab; 