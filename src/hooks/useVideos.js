import { useBaseResource } from './base/useBaseResource';
import { videoConfig } from './base/useResourceConfigs';

export function useVideos() {
  const {
    items: videos,
    loading,
    error,
    createItem,
    updateItem,
    deleteItem,
    getItemById,
    updateItemOrder
  } = useBaseResource(videoConfig);

  // Thumbnail dosyası ile video oluştur
  const createVideo = async (videoData, thumbnailFile) => {
    const files = thumbnailFile ? { thumbnail: thumbnailFile } : {};
    return await createItem(videoData, files);
  };

  // Thumbnail dosyası ile video güncelle
  const updateVideo = async (id, videoData, thumbnailFile) => {
    const files = thumbnailFile ? { thumbnail: thumbnailFile } : {};
    return await updateItem(id, videoData, files);
  };

  return {
    videos,
    loading,
    error,
    getVideoById: getItemById,
    createVideo,
    updateVideo,
    deleteVideo: deleteItem,
    updateVideoOrder: updateItemOrder,
  };
} 