import { useState, useEffect } from 'react';
import { FiPackage, FiPlus, FiEdit3, FiTrash2, FiStar, FiCheck, FiX, FiSave, FiDollarSign, FiCalendar, FiUsers } from 'react-icons/fi';
import { supabase } from '../../supabaseClient';

const Packages = () => {
    const [packages, setPackages] = useState([]);
    const [loading, setLoading] = useState(true);
    const [editingPackage, setEditingPackage] = useState(null);
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        price: '',
        duration_months: 1,
        currency: 'TRY',
        features: '',
        is_popular: false,
        is_active: true
    });

    useEffect(() => {
        fetchPackages();
    }, []);

    const fetchPackages = async () => {
        try {
            setLoading(true);
            const { data, error } = await supabase
                .from('service_packages')
                .select('*')
                .order('duration_months', { ascending: true })
                .order('price', { ascending: true });

            if (error) throw error;
            setPackages(data || []);
        } catch (error) {
            console.error('Paketler yüklenirken hata:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const packageData = {
                ...formData,
                price: parseFloat(formData.price),
                duration_months: parseInt(formData.duration_months),
                features: formData.features.split('\n').filter(f => f.trim())
            };

            let error;
            if (editingPackage) {
                ({ error } = await supabase
                    .from('service_packages')
                    .update(packageData)
                    .eq('id', editingPackage.id));
            } else {
                ({ error } = await supabase
                    .from('service_packages')
                    .insert([packageData]));
            }

            if (error) throw error;

            setShowForm(false);
            setEditingPackage(null);
            resetForm();
            fetchPackages();
        } catch (error) {
            console.error('Paket kaydedilirken hata:', error);
            alert('Paket kaydedilirken hata oluştu: ' + error.message);
        }
    };

    const handleEdit = (pkg) => {
        setEditingPackage(pkg);
        setFormData({
            name: pkg.name,
            description: pkg.description || '',
            price: pkg.price.toString(),
            duration_months: pkg.duration_months,
            currency: pkg.currency,
            features: Array.isArray(pkg.features) ? pkg.features.join('\n') : '',
            is_popular: pkg.is_popular,
            is_active: pkg.is_active
        });
        setShowForm(true);
    };

    const handleDelete = async (id) => {
        if (!confirm('Bu paketi silmek istediğinizden emin misiniz?')) return;

        try {
            const { error } = await supabase
                .from('service_packages')
                .delete()
                .eq('id', id);

            if (error) throw error;
            fetchPackages();
        } catch (error) {
            console.error('Paket silinirken hata:', error);
            alert('Paket silinirken hata oluştu: ' + error.message);
        }
    };

    const toggleActive = async (id, currentStatus) => {
        try {
            const { error } = await supabase
                .from('service_packages')
                .update({ is_active: !currentStatus })
                .eq('id', id);

            if (error) throw error;
            fetchPackages();
        } catch (error) {
            console.error('Paket durumu güncellenirken hata:', error);
        }
    };

    const resetForm = () => {
        setFormData({
            name: '',
            description: '',
            price: '',
            duration_months: 1,
            currency: 'TRY',
            features: '',
            is_popular: false,
            is_active: true
        });
    };

    const formatPrice = (price, currency) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: currency || 'TRY'
        }).format(price);
    };

    const getDurationText = (months) => {
        return months === 1 ? 'Aylık' : months === 12 ? 'Yıllık' : `${months} Ay`;
    };

    if (loading) {
        return (
            <div className="users-management">
                <div className="users-header">
                    <h2>Hizmet Paketleri</h2>
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                        <div className="loading-spinner"></div>
                        <p>Paketler yükleniyor...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="users-management">
            <div className="users-header">
                <div>
                    <h2>Hizmet Paketleri</h2>
                    <p>Aylık ve yıllık hizmet paketlerini yönetin</p>
                </div>
                <button 
                    className="btn-primary"
                    onClick={() => {
                        setEditingPackage(null);
                        resetForm();
                        setShowForm(true);
                    }}
                >
                    <FiPlus size={16} />
                    Yeni Paket
                </button>
            </div>

            {/* Package Form Modal */}
            {showForm && (
                <div className="modal-overlay">
                    <div className="modal-content" style={{ maxWidth: '600px' }}>
                        <div className="modal-header">
                            <h3>{editingPackage ? 'Paket Düzenle' : 'Yeni Paket Ekle'}</h3>
                            <button 
                                className="modal-close"
                                onClick={() => {
                                    setShowForm(false);
                                    setEditingPackage(null);
                                    resetForm();
                                }}
                            >
                                <FiX />
                            </button>
                        </div>
                        
                        <form onSubmit={handleSubmit} className="modal-body">
                            <div className="form-grid">
                                <div className="form-group">
                                    <label>Paket Adı</label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                                        required
                                    />
                                </div>
                                
                                <div className="form-group">
                                    <label>Fiyat</label>
                                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={formData.price}
                                            onChange={(e) => setFormData({...formData, price: e.target.value})}
                                            required
                                            style={{ flex: 1 }}
                                        />
                                        <select
                                            value={formData.currency}
                                            onChange={(e) => setFormData({...formData, currency: e.target.value})}
                                            style={{ width: '80px' }}
                                        >
                                            <option value="TRY">TRY</option>
                                            <option value="USD">USD</option>
                                            <option value="EUR">EUR</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="form-group">
                                <label>Süre</label>
                                <select
                                    value={formData.duration_months}
                                    onChange={(e) => setFormData({...formData, duration_months: parseInt(e.target.value)})}
                                >
                                    <option value={1}>1 Ay (Aylık)</option>
                                    <option value={3}>3 Ay</option>
                                    <option value={6}>6 Ay</option>
                                    <option value={12}>12 Ay (Yıllık)</option>
                                </select>
                            </div>
                            
                            <div className="form-group">
                                <label>Açıklama</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                    rows={3}
                                />
                            </div>
                            
                            <div className="form-group">
                                <label>Özellikler (Her satıra bir özellik)</label>
                                <textarea
                                    value={formData.features}
                                    onChange={(e) => setFormData({...formData, features: e.target.value})}
                                    rows={6}
                                    placeholder="Kişisel profil sayfası&#10;Gelişmiş analitik&#10;E-posta desteği"
                                />
                            </div>
                            
                            <div className="checkbox-group">
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={formData.is_popular}
                                        onChange={(e) => setFormData({...formData, is_popular: e.target.checked})}
                                    />
                                    Popüler paket olarak işaretle
                                </label>
                                
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={formData.is_active}
                                        onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                                    />
                                    Aktif paket
                                </label>
                            </div>
                            
                            <div className="modal-actions">
                                <button type="submit" className="btn-primary">
                                    <FiSave size={16} />
                                    {editingPackage ? 'Güncelle' : 'Kaydet'}
                                </button>
                                <button 
                                    type="button"
                                    className="btn-secondary"
                                    onClick={() => {
                                        setShowForm(false);
                                        setEditingPackage(null);
                                        resetForm();
                                    }}
                                >
                                    İptal
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Packages Grid */}
            <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                gap: '1.5rem',
                marginTop: '2rem'
            }}>
                {packages.map((pkg) => (
                    <div key={pkg.id} className="user-card" style={{ 
                        opacity: pkg.is_active ? 1 : 0.6,
                        border: pkg.is_popular ? '2px solid var(--accent-primary)' : undefined
                    }}>
                        {pkg.is_popular && (
                            <div style={{
                                position: 'absolute',
                                top: '-10px',
                                right: '1rem',
                                background: 'var(--accent-primary)',
                                color: 'white',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '12px',
                                fontSize: '0.75rem',
                                fontWeight: '600',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.25rem'
                            }}>
                                <FiStar size={12} />
                                Popüler
                            </div>
                        )}
                        
                        <div className="user-card-header">
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                                <div style={{
                                    padding: '0.75rem',
                                    borderRadius: '8px',
                                    backgroundColor: 'var(--accent-primary)',
                                    color: 'white'
                                }}>
                                    <FiPackage size={24} />
                                </div>
                                <div>
                                    <h4 style={{ margin: 0, fontSize: '1.125rem' }}>{pkg.name}</h4>
                                    <p style={{ margin: '0.25rem 0 0', color: 'var(--text-muted)', fontSize: '0.875rem' }}>
                                        {getDurationText(pkg.duration_months)}
                                    </p>
                                </div>
                            </div>
                            
                            <div style={{ textAlign: 'right' }}>
                                <div style={{ 
                                    fontSize: '1.5rem', 
                                    fontWeight: '700', 
                                    color: 'var(--accent-primary)' 
                                }}>
                                    {formatPrice(pkg.price, pkg.currency)}
                                </div>
                                <div style={{ fontSize: '0.75rem', color: 'var(--text-muted)' }}>
                                    {pkg.duration_months === 1 ? '/ay' : pkg.duration_months === 12 ? '/yıl' : `/${pkg.duration_months} ay`}
                                </div>
                            </div>
                        </div>
                        
                        <div className="user-card-content">
                            {pkg.description && (
                                <p style={{ 
                                    color: 'var(--text-muted)', 
                                    fontSize: '0.875rem',
                                    marginBottom: '1rem'
                                }}>
                                    {pkg.description}
                                </p>
                            )}
                            
                            {pkg.features && Array.isArray(pkg.features) && (
                                <div>
                                    <h5 style={{ 
                                        fontSize: '0.875rem', 
                                        fontWeight: '600', 
                                        marginBottom: '0.5rem',
                                        color: 'var(--text-primary)'
                                    }}>
                                        Özellikler:
                                    </h5>
                                    <ul style={{ 
                                        listStyle: 'none', 
                                        padding: 0, 
                                        margin: 0,
                                        fontSize: '0.875rem'
                                    }}>
                                        {pkg.features.map((feature, index) => (
                                            <li key={index} style={{ 
                                                display: 'flex', 
                                                alignItems: 'center', 
                                                gap: '0.5rem',
                                                marginBottom: '0.25rem',
                                                color: 'var(--text-muted)'
                                            }}>
                                                <FiCheck size={14} style={{ color: 'var(--success)' }} />
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                        
                        <div className="user-card-actions">
                            <button
                                className="btn-view"
                                onClick={() => handleEdit(pkg)}
                                title="Düzenle"
                            >
                                <FiEdit3 size={14} />
                                Düzenle
                            </button>
                            
                            <button
                                className={pkg.is_active ? "btn-secondary" : "btn-primary"}
                                onClick={() => toggleActive(pkg.id, pkg.is_active)}
                                title={pkg.is_active ? "Pasif Yap" : "Aktif Yap"}
                            >
                                {pkg.is_active ? <FiX size={14} /> : <FiCheck size={14} />}
                                {pkg.is_active ? 'Pasif Yap' : 'Aktif Yap'}
                            </button>
                            
                            <button
                                className="btn-delete"
                                onClick={() => handleDelete(pkg.id)}
                                title="Sil"
                            >
                                <FiTrash2 size={14} />
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {packages.length === 0 && (
                <div className="user-card" style={{ textAlign: 'center', padding: '3rem 2rem' }}>
                    <FiPackage size={48} style={{ color: 'var(--text-muted)', marginBottom: '1rem' }} />
                    <h3 style={{ color: 'var(--text-muted)', marginBottom: '0.5rem' }}>
                        Henüz paket bulunmuyor
                    </h3>
                    <p style={{ color: 'var(--text-muted)', marginBottom: '1.5rem' }}>
                        İlk hizmet paketinizi oluşturmak için "Yeni Paket" butonuna tıklayın.
                    </p>
                    <button 
                        className="btn-primary"
                        onClick={() => {
                            setEditingPackage(null);
                            resetForm();
                            setShowForm(true);
                        }}
                    >
                        <FiPlus size={16} />
                        İlk Paketi Oluştur
                    </button>
                </div>
            )}
        </div>
    );
};

export default Packages; 