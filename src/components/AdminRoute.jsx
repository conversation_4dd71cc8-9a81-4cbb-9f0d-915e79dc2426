import { useEffect, useState } from 'react';
import { supabase } from '../supabaseClient';
import { Navigate, Outlet } from 'react-router-dom';

const AdminRoute = () => {
  const [isAdmin, setIsAdmin] = useState(null); // null: loading, false: not admin, true: is admin
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdmin = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();
      
      if (error || !profile) {
        setIsAdmin(false);
      } else {
        setIsAdmin(profile.role === 'admin');
      }
      setLoading(false);
    };

    checkAdmin();
  }, []);

  if (loading) {
    return <div>Yet<PERSON> kontrol ediliyor...</div>;
  }
  
  // Eğer kullanıcı admin ise, nested route'ları (Outlet) göster.
  // Değilse, ana sayfaya yönlendir.
  return isAdmin ? <Outlet /> : <Navigate to="/" />;
};

export default AdminRoute; 