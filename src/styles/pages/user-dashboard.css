/* Modern User Dashboard Styles */

.user-dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
  color: var(--text-muted);
  background: var(--bg-primary);
}

.user-dashboard-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.user-dashboard-loading p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 400;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Dashboard */
.user-dashboard {
  min-height: 100vh;
  background: var(--bg-primary);
}

/* Modern Header */
.user-dashboard-header {
  background: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.dashboard-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #667eea;
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.02em;
}

.logo-icon {
  flex-shrink: 0;
  font-size: 1.75rem;
}

.user-welcome h1 {
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.user-welcome p {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 18px;
  height: 18px;
  background: #ef4444;
  color: white;
  border-radius: 9px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0 4px;
  border: 2px solid var(--bg-primary);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.logout-btn svg {
  flex-shrink: 0;
}

/* Main Content */
.dashboard-main {
  padding: 2rem 0;
}

/* Stats Section */
.stats-section {
  margin-bottom: 3rem;
}

.stats-section h2 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.4s ease;
  border-radius: 0 10px 10px 0;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.08), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(102, 126, 234, 0.7);
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.98) 0%, 
    rgba(31, 41, 55, 0.95) 50%,
    rgba(55, 65, 81, 0.9) 100%);
}

.stat-card:hover::before {
  width: 8px;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.stat-card:hover::after {
  opacity: 1;
  right: -30%;
}

.stat-icon {
  flex-shrink: 0;
  width: 70px;
  height: 70px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stat-icon.views {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-icon.monthly {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 
    0 8px 25px rgba(16, 185, 129, 0.4),
    0 4px 10px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-icon.score {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 
    0 8px 25px rgba(245, 158, 11, 0.4),
    0 4px 10px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-icon.leads {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  box-shadow: 
    0 8px 25px rgba(139, 92, 246, 0.4),
    0 4px 10px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

.stat-card:hover .stat-icon.views {
  box-shadow: 
    0 15px 40px rgba(102, 126, 234, 0.6),
    0 8px 20px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-card:hover .stat-icon.monthly {
  box-shadow: 
    0 15px 40px rgba(16, 185, 129, 0.6),
    0 8px 20px rgba(16, 185, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-card:hover .stat-icon.score {
  box-shadow: 
    0 15px 40px rgba(245, 158, 11, 0.6),
    0 8px 20px rgba(245, 158, 11, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-card:hover .stat-icon.leads {
  box-shadow: 
    0 15px 40px rgba(139, 92, 246, 0.6),
    0 8px 20px rgba(139, 92, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-content h3 {
  margin: 0 0 0.375rem 0;
  color: #ffffff;
  font-size: 2rem;
  font-weight: 800;
  letter-spacing: -0.03em;
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.stat-content p {
  margin: 0 0 0.75rem 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-content h3 {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.stat-card:hover .stat-content p {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.4rem 0.8rem;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  letter-spacing: 0.02em;
}

.stat-change.positive {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.15) 100%);
  color: #34d399;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.stat-change.negative {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.15) 100%);
  color: #f87171;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
}

.stat-change.neutral {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.2) 0%, rgba(75, 85, 99, 0.15) 100%);
  color: #d1d5db;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.2);
}

.stat-card:hover .stat-change {
  transform: translateY(-1px) scale(1.05);
}

.stat-card:hover .stat-change.positive {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.35);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(5, 150, 105, 0.25) 100%);
}

.stat-card:hover .stat-change.negative {
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.35);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 38, 0.25) 100%);
}

.stat-card:hover .stat-change.neutral {
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.35);
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.3) 0%, rgba(75, 85, 99, 0.25) 100%);
}

/* Quick Actions Section */
.quick-actions-section {
  margin-bottom: 3rem;
}

.quick-actions-section h2 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: linear-gradient(135deg, 
    rgba(31, 41, 55, 0.8) 0%, 
    rgba(55, 65, 81, 0.6) 100%);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  padding: 1.75rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-align: left;
  width: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 6px 25px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.6) 50%, 
    transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.action-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 
    0 15px 50px rgba(0, 0, 0, 0.25),
    0 8px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, 
    rgba(31, 41, 55, 0.95) 0%, 
    rgba(55, 65, 81, 0.8) 100%);
}

.action-card:hover::before {
  transform: translateX(0);
}

.action-icon {
  flex-shrink: 0;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.2) 0%, 
    rgba(118, 75, 162, 0.15) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b8cf5;
  font-size: 1.375rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 4px 15px rgba(102, 126, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
}

.action-card:hover .action-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.15) rotate(-5deg);
  box-shadow: 
    0 10px 30px rgba(102, 126, 234, 0.4),
    0 5px 15px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-card:hover .action-icon::before {
  transform: rotate(45deg) translateX(100%);
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-content h3 {
  margin: 0 0 0.375rem 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.action-content p {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
}

.action-card:hover .action-content h3 {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.action-card:hover .action-content p {
  color: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

/* Activity Section */
.activity-section h2 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.activity-list {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.activity-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.125rem;
  margin-top: 0.125rem;
}

.activity-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.activity-content h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.3;
}

.activity-content p {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
  white-space: nowrap;
  margin-top: 0.125rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-container {
    padding: 0 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-left {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-logo {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    border-radius: 16px;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    border-radius: 14px;
  }

  .stat-card:hover .stat-icon {
    transform: scale(1.1);
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .action-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
    border-radius: 14px;
  }

  .action-card:hover .action-icon {
    transform: scale(1.1) rotate(-3deg);
  }

  .activity-item {
    padding: 1rem;
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }

  .activity-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .activity-content {
    flex: 1;
    text-align: left;
  }

  .activity-time {
    font-size: 0.7rem;
    margin-top: 0.25rem;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0 0.75rem;
  }

  .user-dashboard-header {
    padding: 1rem 0;
  }

  .dashboard-main {
    padding: 1rem 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1.25rem;
    gap: 1rem;
    border-radius: 14px;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .quick-actions-grid {
    gap: 0.75rem;
  }

  .action-card {
    padding: 1.25rem;
    gap: 1rem;
    border-radius: 14px;
  }

  .logout-btn span {
    display: none;
  }

  .notification-btn {
    width: 40px;
    height: 40px;
  }

  .header-right {
    gap: 0.75rem;
  }

  .user-welcome h1 {
    font-size: 1.5rem;
  }

  .dashboard-logo {
    font-size: 1.25rem;
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .stats-section,
  .quick-actions-section {
    margin-bottom: 2rem;
  }

  .stats-section h2,
  .quick-actions-section h2,
  .activity-section h2 {
    font-size: 1.25rem;
  }
} 