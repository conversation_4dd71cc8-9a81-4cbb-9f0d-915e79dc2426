import { useState, useEffect } from 'react';
import { Link, Outlet, useLocation, useNavigate, Navigate } from 'react-router-dom';
import { FiHome, FiUsers, FiLogOut, FiMenu, FiX, FiSettings, FiPackage } from 'react-icons/fi';
import { supabase } from '../../supabaseClient';
import { useAuth } from '../../hooks/useAuth';

const AdminLayout = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const { user, loading } = useAuth();

    // Close sidebar when clicking outside or on nav items
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (isSidebarOpen && !event.target.closest('.admin-sidebar') && !event.target.closest('.sidebar-toggle')) {
                setIsSidebarOpen(false);
            }
        };

        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [isSidebarOpen]);

    // Close sidebar when route changes
    useEffect(() => {
        setIsSidebarOpen(false);
    }, [location.pathname]);

    // Loading durumunda spinner göster
    if (loading) {
        return (
            <div className="admin-loading">
                <div className="loading-spinner"></div>
                <p>Yükleniyor...</p>
            </div>
        );
    }

    // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
    if (!user) {
        return <Navigate to="/login" replace />;
    }

    const handleLogout = async () => {
        if (!confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
            return;
        }
        
        try {
            setIsLoggingOut(true);
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            
            // State'i temizle ve yönlendir
            setIsSidebarOpen(false);
            navigate('/login', { replace: true });
        } catch (error) {
            console.error('Çıkış hatası:', error.message);
            alert('Çıkış yapılırken bir hata oluştu. Lütfen tekrar deneyin.');
        } finally {
            setIsLoggingOut(false);
        }
    };

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const closeSidebar = () => {
        setIsSidebarOpen(false);
    };

    const getPageTitle = () => {
        const path = location.pathname;
        if (path === '/admin') return 'Dashboard';
        if (path === '/admin/users') return 'Kullanıcı Yönetimi';
        if (path.startsWith('/admin/users/')) return 'Kullanıcı Düzenle';
        if (path === '/admin/packages') return 'Hizmet Paketleri';
        if (path === '/admin/settings') return 'Sistem Ayarları';
        return 'Admin Panel';
    };

    const navigation = [
        {
            name: 'Dashboard',
            href: '/admin',
            icon: FiHome,
            current: location.pathname === '/admin'
        },
        {
            name: 'Kullanıcılar',
            href: '/admin/users',
            icon: FiUsers,
            current: location.pathname === '/admin/users'
        },
        {
            name: 'Paketler',
            href: '/admin/packages',
            icon: FiPackage,
            current: location.pathname === '/admin/packages'
        },
        {
            name: 'Ayarlar',
            href: '/admin/settings',
            icon: FiSettings,
            current: location.pathname === '/admin/settings'
        }
    ];

    return (
        <div className="admin-layout">
            {/* Sidebar Overlay for Mobile */}
            {isSidebarOpen && <div className="sidebar-overlay" onClick={closeSidebar}></div>}
            
            {/* Sidebar */}
            <aside className={`admin-sidebar ${isSidebarOpen ? 'sidebar-open' : ''}`}>
                <div className="sidebar-header">
                    <h2>Zylo Admin</h2>
                    <button className="sidebar-close" onClick={closeSidebar}>
                        <FiX />
                    </button>
                </div>
                
                <nav className="sidebar-nav">
                    {navigation.map((item) => {
                        const Icon = item.icon;
                        return (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`nav-item ${item.current ? 'active' : ''}`}
                                onClick={() => setIsSidebarOpen(false)}
                            >
                                <Icon size={20} />
                                <span>{item.name}</span>
                            </Link>
                        );
                    })}
                </nav>
                
                <div className="sidebar-footer">
                    <div className="user-info">
                        <p>Hoş geldin,</p>
                        <p className="user-email">{user?.email}</p>
                    </div>
                    <button 
                        className="logout-btn" 
                        onClick={handleLogout}
                        disabled={isLoggingOut}
                    >
                        <FiLogOut />
                        {isLoggingOut ? 'Çıkış Yapılıyor...' : 'Çıkış Yap'}
                    </button>
                </div>
            </aside>
            
            {/* Main Content */}
            <main className="admin-main">
                <header className="admin-header">
                    <button className="sidebar-toggle" onClick={toggleSidebar}>
                        <FiMenu />
                    </button>
                    <h1>{getPageTitle()}</h1>
                </header>
                
                <div className="admin-content">
                    <Outlet />
                </div>
            </main>
        </div>
    );
};

export default AdminLayout; 