/* User Profile Styles */

.user-profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: var(--spacing);
  color: var(--text-muted);
}

.user-profile-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.user-profile-loading p {
  margin: 0;
  font-size: 0.875rem;
}

.user-profile {
  min-height: 100vh;
  background: var(--bg-primary);
}

.user-profile-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
}

.profile-main {
  padding: var(--spacing-xl) 0;
}

.profile-message {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing);
  border-radius: var(--radius);
  border-left: 4px solid var(--accent-primary);
}

.profile-message.success {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success);
  color: var(--success);
}

.profile-message.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--danger);
  color: var(--danger);
}

.profile-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.profile-card-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.profile-avatar-large {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--border-color);
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--text-muted);
}

.avatar-edit-icon {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: var(--accent-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid var(--bg-secondary);
}

.avatar-edit-icon:hover {
  background: var(--accent-secondary);
  transform: scale(1.1);
}

.avatar-edit-icon:active {
  transform: scale(0.95);
}

.profile-basic-info h2 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
}

.profile-basic-info p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.profile-email {
  color: var(--text-muted);
  font-size: 0.8125rem;
}

.profile-username {
  color: var(--accent-primary);
  font-weight: 600;
}

.profile-slogan {
  font-style: italic;
  color: var(--text-secondary);
  margin: var(--spacing-sm) 0 !important;
}

.profile-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
}

.tab-btn.active {
  color: var(--accent-primary);
  border-bottom-color: var(--accent-primary);
  background: rgba(79, 70, 229, 0.05);
}

.profile-content {
  padding: 1rem;
}

.personal-info-section {
  max-width: 800px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* Sosyal medya için 3 sütunlu grid artık */
.social-media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .social-media-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .social-media-grid {
    grid-template-columns: 1fr;
  }
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.875rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

.form-group small {
  display: block;
  margin-top: var(--spacing-xs);
  color: var(--text-muted);
  font-size: 0.75rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}


.social-section h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.form-actions {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.save-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 2rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.portfolio-section {
  margin-top: var(--spacing-2xl);
}

.portfolio-header h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.portfolio-header p {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.add-link-section {
  background: var(--bg-tertiary);
  border-radius: var(--radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.add-link-section h4 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.add-link-form {
  display: flex;
  gap: var(--spacing);
  align-items: end;
}

.add-link-form .form-group {
  flex: 1;
  margin-bottom: 0;
}

.add-link-btn {
  padding: 0.75rem 1.5rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.add-link-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.portfolio-links-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.portfolio-link-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  transition: var(--transition);
}

.portfolio-link-item:hover {
  border-color: var(--accent-primary);
  transform: translateX(3px);
}

.link-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 0.9375rem;
  font-weight: 600;
}

.link-info a {
  color: var(--accent-primary);
  text-decoration: none;
  font-size: 0.8125rem;
  transition: var(--transition);
}

.link-info a:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

.delete-link-btn {
  padding: 0.5rem;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.delete-link-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: var(--danger);
}

.empty-portfolio {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-muted);
  background: var(--bg-tertiary);
  border-radius: var(--radius);
  border: 2px dashed var(--border-color);
}

.empty-portfolio svg {
  margin-bottom: var(--spacing);
  opacity: 0.5;
}

.empty-portfolio h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
  font-weight: 600;
}

.empty-portfolio p {
  margin: 0;
  font-size: 0.875rem;
}

/* Username Input with Checking */
.username-input-wrapper {
  position: relative;
  width: 100%;
}

.username-input-wrapper input {
  padding-right: 3rem;
}

.username-checking {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--accent-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.username-available {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--success);
}

.username-unavailable {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--danger);
}

.form-group small.success {
  color: var(--success);
}

.form-group small.error {
  color: var(--danger);
}

.social-media-section {
  margin-top: var(--spacing-xl);
}

.social-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.form-group label svg {
  flex-shrink: 0;
}

.avatar-edit-btn {
  position: relative;
  overflow: hidden;
}

.avatar-edit-btn:hover {
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-profile .dashboard-container {
    padding: 0 var(--spacing);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing);
    text-align: center;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .profile-card-header {
    padding: var(--spacing);
  }

  .profile-content {
    padding: var(--spacing);
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .add-link-form {
    flex-direction: column;
    align-items: stretch;
  }

  .add-link-btn {
    width: 100%;
  }

  .portfolio-link-item {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .profile-tabs {
    overflow-x: auto;
  }

  .tab-btn {
    white-space: nowrap;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .user-profile .dashboard-container {
    padding: 0 var(--spacing-sm);
  }

  .profile-card {
    margin: var(--spacing-sm);
  }

  .profile-card-header {
    padding: var(--spacing);
  }

  .profile-content {
    padding: var(--spacing);
  }

  .avatar-placeholder-large {
    font-size: 2rem;
  }

  .add-link-section {
    padding: var(--spacing);
  }
}

/* Avatar Image */
.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* QR Code Styles */
.profile-qr-section {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.qr-code-container {
  background: white;
  padding: var(--spacing-sm);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}

.qr-code {
  display: block;
}

.qr-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.qr-info h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.profile-link {
  font-size: 0.875rem;
  color: var(--accent-primary);
  font-weight: 500;
  margin: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-link:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

.qr-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.qr-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.qr-action-btn:hover {
  background: var(--bg-primary);
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Media Queries for QR Code */
@media (max-width: 992px) {
  .profile-qr-section {
    margin-left: 0;
    margin-top: var(--spacing);
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .user-profile .dashboard-container {
    padding: var(--spacing);
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing);
  }
  
  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-qr-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-card-header {
    padding: var(--spacing);
  }
  
  .profile-content {
    padding: var(--spacing);
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }

  /* Sosyal medya için mobile responsive */
  .social-media-section .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .add-link-form {
    flex-direction: column;
  }
  
  .add-link-btn {
    width: 100%;
  }
  
  .portfolio-link-item {
    flex-direction: column;
    gap: var(--spacing);
  }
  
  .profile-tabs {
    overflow-x: auto;
  }
  
  .tab-btn {
    padding: var(--spacing) var(--spacing-md);
    white-space: nowrap;
  }
}

@media (max-width: 480px) {
  .user-profile .dashboard-container {
    padding: var(--spacing-sm);
  }
  
  .profile-card {
    border-radius: var(--radius);
  }
  
  .profile-card-header {
    padding: var(--spacing-sm);
  }
  
  .profile-content {
    padding: var(--spacing-sm);
  }
  
  .avatar-placeholder-large {
    width: 80px;
    height: 80px;
  }
  
  .add-link-section {
    padding: var(--spacing-sm);
  }

  /* Küçük ekranlarda sosyal medya tek sütun */
  .social-media-section .form-grid {
    grid-template-columns: 1fr;
  }
}

/* E-posta Güncelleme Stilleri */
.email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.email-input-wrapper input {
  flex: 1;
  padding-right: 45px;
}

.email-update-btn {
  position: absolute;
  right: 8px;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.email-update-btn:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-1px);
}

/* Şifre Değiştirme Bölümü */
.password-change-section {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.password-change-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #f8fafc;
  font-size: 1.1rem;
  font-weight: 600;
}

.password-change-section h3 svg {
  color: #f59e0b;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-wrapper input {
  flex: 1;
  padding-right: 45px;
}

.password-toggle-btn {
  position: absolute;
  right: 8px;
  background: transparent;
  border: none;
  color: #94a3b8;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.password-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.password-update-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  min-width: 180px;
  justify-content: center;
}

.password-update-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.password-update-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.password-update-btn .btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Kullanıcı Bilgileri 3 Sütun Grid */
.user-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .user-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .user-info-grid {
    grid-template-columns: 1fr;
  }
}

/* Şifre Değiştir 3 Sütun Grid */
.password-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .password-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .password-grid {
    grid-template-columns: 1fr;
  }
}

/* Profil Bilgileri 3 Sütun Grid */
.profile-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .profile-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .profile-info-grid {
    grid-template-columns: 1fr;
  }
}

/* İletişim 3 Sütun Grid */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
}

.customization-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.customization-group {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.customization-group h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.customization-group h3 svg {
  color: var(--accent-primary);
}

/* Color Options */
.color-options {
  margin-top: var(--spacing);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--spacing);
}

.color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing);
  border: 2px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.02);
}

.color-option:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

.color-option input[type="radio"] {
  display: none;
}

.color-option input[type="radio"]:checked + .color-preview {
  transform: scale(1.2);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.color-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.color-option span {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

.color-option input[type="radio"]:checked ~ span {
  color: var(--text-primary);
  font-weight: 600;
}

/* Style Options */
.style-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing);
  margin-top: var(--spacing);
}

.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing);
  border: 2px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.02);
}

.style-option:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

.style-option input[type="radio"] {
  display: none;
}

.style-option input[type="radio"]:checked {
  & + .style-preview {
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
  }
  
  & ~ span {
    color: var(--text-primary);
    font-weight: 600;
  }
}

.style-preview {
  width: 60px;
  height: 40px;
  border-radius: var(--radius-sm);
  transition: var(--transition);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.gradient-preview {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.solid-preview {
  background: #374151;
}

.pattern-preview {
  background: 
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.3) 1px, transparent 1px),
    #1f2937;
  background-size: 20px 20px;
}

.card-modern {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border-radius: 16px;
}

.card-classic {
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.card-minimal {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.style-option span {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* Visibility Options */
.visibility-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
  margin-top: var(--spacing);
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing);
  border: 1px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.02);
}

.checkbox-option:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  position: relative;
  transition: var(--transition);
  flex-shrink: 0;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.checkbox-option input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.checkbox-option span:not(.checkmark) {
  color: var(--text-secondary);
  font-size: 0.9375rem;
}

.checkbox-option input[type="checkbox"]:checked ~ span:not(.checkmark) {
  color: var(--text-primary);
}

/* Privacy Options */
.privacy-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
  margin-top: var(--spacing);
}

.privacy-option {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.02);
}

.privacy-option:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

.privacy-option input[type="radio"] {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  appearance: none;
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
  position: relative;
}

.privacy-option input[type="radio"]:checked {
  border-color: var(--accent-primary);
  background: var(--accent-primary);
}

.privacy-option input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.privacy-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.privacy-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9375rem;
}

.privacy-desc {
  color: var(--text-muted);
  font-size: 0.8125rem;
  line-height: 1.4;
}

.privacy-option input[type="radio"]:checked ~ .privacy-info .privacy-title {
  color: var(--accent-primary);
}

/* Responsive Design for Customization */
@media (max-width: 768px) {
  .color-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .style-options {
    grid-template-columns: 1fr;
  }
  
  .customization-group {
    padding: var(--spacing);
  }
  
  .customization-group h3 {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .color-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .color-preview {
    width: 32px;
    height: 32px;
  }
  
  .style-preview {
    width: 50px;
    height: 32px;
  }
  
  .customization-content {
    gap: var(--spacing-lg);
  }
}