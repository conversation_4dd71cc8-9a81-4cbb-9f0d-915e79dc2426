import { useState, useEffect } from 'react';
import { supabase } from '../../supabaseClient';
import { 
    FiSave, FiSearch, FiEye, FiPhone, FiSettings, FiImage, 
    FiCheckCircle, FiXCircle, FiLoader, FiUpload, FiTrash2 
} from 'react-icons/fi';

const Settings = () => {
    const [settings, setSettings] = useState({});
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [activeTab, setActiveTab] = useState('seo');
    const [success, setSuccess] = useState('');
    const [error, setError] = useState('');

    const tabs = [
        { id: 'seo', name: 'SEO Ayarları', icon: FiSearch },
        { id: 'branding', name: 'Marka & Logo', icon: FiImage },
        { id: 'contact', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: FiPhone },
        { id: 'general', name: '<PERSON><PERSON>', icon: FiSettings }
    ];

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            setError('');

            const { data, error } = await supabase
                .from('site_settings')
                .select('*')
                .order('category, display_order');

            if (error) throw error;

            // Convert array to object for easier access
            const settingsObj = {};
            data?.forEach(setting => {
                settingsObj[setting.setting_key] = setting.setting_value || '';
            });

            setSettings(settingsObj);
        } catch (error) {
            console.error('Settings fetch error:', error);
            setError(`Ayarlar yüklenirken hata oluştu: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (key, value) => {
        setSettings(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSave = async () => {
        try {
            setSaving(true);
            setError('');
            setSuccess('');

            // Get all current settings to compare
            const { data: currentSettings } = await supabase
                .from('site_settings')
                .select('setting_key, setting_value');

            const updates = [];
            const currentSettingsMap = {};
            
            currentSettings?.forEach(setting => {
                currentSettingsMap[setting.setting_key] = setting.setting_value;
            });

            // Find changed settings
            Object.keys(settings).forEach(key => {
                if (settings[key] !== (currentSettingsMap[key] || '')) {
                    updates.push({
                        setting_key: key,
                        setting_value: settings[key]
                    });
                }
            });

            if (updates.length > 0) {
                // Update changed settings
                for (const update of updates) {
                    const { error } = await supabase
                        .from('site_settings')
                        .update({ 
                            setting_value: update.setting_value,
                            updated_at: new Date().toISOString()
                        })
                        .eq('setting_key', update.setting_key);

                    if (error) throw error;
                }

                setSuccess('Ayarlar başarıyla kaydedildi!');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setSuccess('Değişiklik yapılmadı.');
                setTimeout(() => setSuccess(''), 2000);
            }

        } catch (error) {
            console.error('Settings save error:', error);
            setError(`Ayarlar kaydedilirken hata oluştu: ${error.message}`);
        } finally {
            setSaving(false);
        }
    };

    const renderFormField = (setting) => {
        const value = settings[setting.setting_key] || '';

        switch (setting.setting_type) {
            case 'textarea':
                return (
                    <textarea
                        value={value}
                        onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                        placeholder={setting.description}
                        rows={3}
                        className="form-textarea"
                        required={setting.is_required}
                    />
                );
            
            case 'email':
                return (
                    <input
                        type="email"
                        value={value}
                        onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                        placeholder={setting.description}
                        className="form-input"
                        required={setting.is_required}
                    />
                );
            
            case 'number':
                return (
                    <input
                        type="number"
                        value={value}
                        onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                        placeholder={setting.description}
                        className="form-input"
                        required={setting.is_required}
                        min="0"
                    />
                );
            
            case 'boolean':
                return (
                    <select
                        value={value}
                        onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                        className="form-select"
                        required={setting.is_required}
                    >
                        <option value="true">Evet</option>
                        <option value="false">Hayır</option>
                    </select>
                );
            
            case 'file':
                return (
                    <div className="file-input-group">
                        <input
                            type="url"
                            value={value}
                            onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                            placeholder="Dosya URL'si girin"
                            className="form-input"
                        />
                        <button type="button" className="btn-upload">
                            <FiUpload size={16} />
                            Yükle
                        </button>
                    </div>
                );
            
            default: // text
                return (
                    <input
                        type="text"
                        value={value}
                        onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                        placeholder={setting.description}
                        className="form-input"
                        required={setting.is_required}
                    />
                );
        }
    };

    const settingsConfig = {
        seo: [
            { setting_key: 'site_title', display_name: 'Site Başlığı', setting_type: 'text', description: 'Web sitesinin ana başlığı (SEO için önemli)', is_required: true },
            { setting_key: 'site_description', display_name: 'Site Açıklaması', setting_type: 'textarea', description: 'Meta description alanı (SEO için kritik)', is_required: true },
            { setting_key: 'site_keywords', display_name: 'Site Anahtar Kelimeleri', setting_type: 'textarea', description: 'Meta keywords (virgülle ayırın)', is_required: false },
            { setting_key: 'google_analytics_id', display_name: 'Google Analytics ID', setting_type: 'text', description: 'GA4 Tracking ID (örn: G-XXXXXXXXXX)', is_required: false },
            { setting_key: 'google_search_console', display_name: 'Google Search Console Meta', setting_type: 'textarea', description: 'Google Search Console doğrulama meta tagı', is_required: false }
        ],
        branding: [
            { setting_key: 'site_logo', display_name: 'Site Logosu', setting_type: 'file', description: 'Ana logo dosyası (PNG/JPG/SVG)', is_required: false },
            { setting_key: 'site_favicon', display_name: 'Favicon', setting_type: 'file', description: 'Site ikonu (ICO/PNG 32x32)', is_required: false },
            { setting_key: 'site_logo_dark', display_name: 'Koyu Tema Logo', setting_type: 'file', description: 'Koyu tema için logo', is_required: false },
            { setting_key: 'brand_color_primary', display_name: 'Ana Marka Rengi', setting_type: 'text', description: 'Hex renk kodu (#RRGGBB)', is_required: false },
            { setting_key: 'brand_color_secondary', display_name: 'İkincil Marka Rengi', setting_type: 'text', description: 'Hex renk kodu (#RRGGBB)', is_required: false }
        ],
        contact: [
            { setting_key: 'contact_email', display_name: 'İletişim E-postası', setting_type: 'email', description: 'Genel iletişim e-posta adresi', is_required: true },
            { setting_key: 'contact_phone', display_name: 'İletişim Telefonu', setting_type: 'text', description: 'Genel iletişim telefon numarası', is_required: false },
            { setting_key: 'contact_address', display_name: 'Adres', setting_type: 'textarea', description: 'Fiziksel adres bilgisi', is_required: false },
            { setting_key: 'support_email', display_name: 'Destek E-postası', setting_type: 'email', description: 'Teknik destek e-posta adresi', is_required: false },
            { setting_key: 'business_hours', display_name: 'Çalışma Saatleri', setting_type: 'text', description: 'İş saatleri bilgisi', is_required: false }
        ],
        general: [
            { setting_key: 'site_status', display_name: 'Site Durumu', setting_type: 'text', description: 'Site durumu (active/maintenance)', is_required: true },
            { setting_key: 'maintenance_message', display_name: 'Bakım Mesajı', setting_type: 'textarea', description: 'Bakım modunda gösterilecek mesaj', is_required: false },
            { setting_key: 'user_registration_enabled', display_name: 'Kullanıcı Kaydına İzin Ver', setting_type: 'boolean', description: 'Yeni kullanıcı kaydına izin ver/verme', is_required: true },
            { setting_key: 'default_membership_duration', display_name: 'Varsayılan Üyelik Süresi (Gün)', setting_type: 'number', description: 'Yeni kullanıcılar için varsayılan üyelik süresi', is_required: true }
        ]
    };

    if (loading) {
        return (
            <div className="admin-loading">
                <div className="loading-spinner"></div>
                <p>Ayarlar yükleniyor...</p>
            </div>
        );
    }

    return (
        <div className="settings-container">
            <div className="settings-header">
                <h2>Sistem Ayarları</h2>
                <p>Web sitesi yapılandırmasını ve temel ayarları yönetin</p>
            </div>

            {/* Alerts */}
            {error && (
                <div className="alert alert-error">
                    <FiXCircle />
                    <span>{error}</span>
                </div>
            )}

            {success && (
                <div className="alert alert-success">
                    <FiCheckCircle />
                    <span>{success}</span>
                </div>
            )}

            {/* Settings Tabs */}
            <div className="settings-tabs">
                {tabs.map(tab => {
                    const Icon = tab.icon;
                    return (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                        >
                            <Icon size={20} />
                            <span>{tab.name}</span>
                        </button>
                    );
                })}
            </div>

            {/* Settings Form */}
            <div className="settings-content">
                <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
                    <div className="settings-form">
                        {settingsConfig[activeTab]?.map(setting => (
                            <div key={setting.setting_key} className="form-group">
                                <label htmlFor={setting.setting_key}>
                                    {setting.display_name}
                                    {setting.is_required && <span className="required">*</span>}
                                </label>
                                {renderFormField(setting)}
                                {setting.description && (
                                    <div className="form-hint">{setting.description}</div>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="form-actions">
                        <button 
                            type="submit" 
                            className="btn-primary"
                            disabled={saving}
                        >
                            {saving ? (
                                <>
                                    <FiLoader className="animate-spin" />
                                    Kaydediliyor...
                                </>
                            ) : (
                                <>
                                    <FiSave />
                                    Ayarları Kaydet
                                </>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Settings; 