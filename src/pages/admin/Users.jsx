import { useEffect, useState } from 'react';
import { supabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';
import { FiSearch, FiUsers, FiUserCheck, FiShield, FiEdit3, FiExternalLink, FiChevronDown, FiChevronUp, FiFilter, FiClock, FiXCircle, FiCheckCircle } from 'react-icons/fi';

const Users = () => {
    const [users, setUsers] = useState([]);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedRole, setSelectedRole] = useState('all');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [error, setError] = useState(null);
    const [sortField, setSortField] = useState('created_at');
    const [sortDirection, setSortDirection] = useState('desc');

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                setLoading(true);
                setError(null);
                
                console.log('Kullanıcılar çekiliyor...');
                
                // Profilleri çekelim
                const { data: profiles, error: profilesError } = await supabase
                    .from('profiles')
                    .select('*');
                
                if (profilesError) throw profilesError;
                
                console.log('Profil bilgileri:', profiles);
                
                if (profiles && profiles.length > 0) {
                    // Kullanıcı verilerini düzenle
                    const formattedUsers = profiles.map(profile => ({
                        id: profile.id,
                        full_name: profile.full_name || '',
                        username: profile.username || '',
                        email: '', // Auth API olmadan e-posta bilgisini alamayız
                        role: profile.role || 'user',
                        avatar_url: profile.avatar_url,
                        updated_at: profile.updated_at,
                        created_at: profile.updated_at, // Kayıt tarihi olarak güncelleme tarihini kullanıyoruz
                        last_sign_in_at: null, // Giriş bilgisi alamıyoruz
                        membership_expires_at: profile.membership_expires_at,
                        membership_status: profile.membership_status || 'active'
                    }));
                    
                    setUsers(formattedUsers);
                    setFilteredUsers(formattedUsers);
                    console.log('Kullanıcılar yüklendi:', formattedUsers);
                } else {
                    console.log('Veri bulunamadı veya boş dizi döndü');
                    // Örnek kullanıcı ekleyelim
                    const dummyUsers = [
                        {
                            id: '1',
                            full_name: 'Cansu Demir',
                            username: 'cansudemir',
                            email: '<EMAIL>',
                            role: 'admin',
                            avatar_url: null,
                            updated_at: new Date().toISOString(),
                            created_at: new Date().toISOString(),
                            last_sign_in_at: new Date().toISOString(),
                            membership_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 yıl sonra
                            membership_status: 'active'
                        },
                        {
                            id: '2',
                            full_name: 'Test Kullanıcı',
                            username: 'testuser',
                            email: '<EMAIL>',
                            role: 'user',
                            avatar_url: null,
                            updated_at: new Date().toISOString(),
                            created_at: new Date().toISOString(),
                            last_sign_in_at: null,
                            membership_expires_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 gün önce süresi bitmiş
                            membership_status: 'expired'
                        }
                    ];
                    setUsers(dummyUsers);
                    setFilteredUsers(dummyUsers);
                    console.log('Örnek kullanıcılar eklendi:', dummyUsers);
                }
            } catch (error) {
                console.error("Kullanıcıları çekerken hata:", error);
                setError(`Kullanıcılar yüklenirken hata oluştu: ${error.message}`);
                
                // Hata durumunda örnek kullanıcılar gösterelim
                const dummyUsers = [
                    {
                        id: '1',
                        full_name: 'Cansu Demir',
                        username: 'cansudemir',
                        email: '<EMAIL>',
                        role: 'admin',
                        avatar_url: null,
                        updated_at: new Date().toISOString(),
                        created_at: new Date().toISOString(),
                        last_sign_in_at: new Date().toISOString(),
                        membership_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                        membership_status: 'active'
                    },
                    {
                        id: '2',
                        full_name: 'Test Kullanıcı',
                        username: 'testuser',
                        email: '<EMAIL>',
                        role: 'user',
                        avatar_url: null,
                        updated_at: new Date().toISOString(),
                        created_at: new Date().toISOString(),
                        last_sign_in_at: null,
                        membership_expires_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                        membership_status: 'expired'
                    }
                ];
                setUsers(dummyUsers);
                setFilteredUsers(dummyUsers);
            } finally {
                setLoading(false);
            }
        };

        fetchUsers();
    }, []);

    useEffect(() => {
        let filtered = users;

        // Arama filtresi
        if (searchTerm) {
            filtered = filtered.filter(user => 
                user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.email?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Rol filtresi
        if (selectedRole !== 'all') {
            filtered = filtered.filter(user => user.role === selectedRole);
        }

        // Üyelik durumu filtresi
        if (selectedStatus !== 'all') {
            filtered = filtered.filter(user => user.membership_status === selectedStatus);
        }

        // Sıralama
        filtered = [...filtered].sort((a, b) => {
            const fieldA = a[sortField] || '';
            const fieldB = b[sortField] || '';
            
            if (sortDirection === 'asc') {
                return fieldA < fieldB ? -1 : fieldA > fieldB ? 1 : 0;
            } else {
                return fieldA > fieldB ? -1 : fieldA < fieldB ? 1 : 0;
            }
        });

        setFilteredUsers(filtered);
    }, [searchTerm, selectedRole, selectedStatus, users, sortField, sortDirection]);

    const getRoleStats = () => {
        const adminCount = users.filter(u => u.role === 'admin').length;
        const userCount = users.filter(u => u.role === 'user').length;
        const activeCount = users.filter(u => u.membership_status === 'active').length;
        const expiredCount = users.filter(u => u.membership_status === 'expired').length;
        return { total: users.length, adminCount, userCount, activeCount, expiredCount };
    };

    const getRoleBadgeClass = (role) => {
        return role === 'admin' ? 'role-badge admin' : 'role-badge user';
    };

    const getMembershipStatusBadgeClass = (status) => {
        switch (status) {
            case 'active':
                return 'status-badge active';
            case 'expired':
                return 'status-badge expired';
            case 'suspended':
                return 'status-badge suspended';
            default:
                return 'status-badge active';
        }
    };

    const getMembershipStatusText = (status) => {
        switch (status) {
            case 'active':
                return 'Aktif';
            case 'expired':
                return 'Süresi Bitmiş';
            case 'suspended':
                return 'Askıya Alınmış';
            default:
                return 'Aktif';
        }
    };

    const getMembershipStatusIcon = (status) => {
        switch (status) {
            case 'active':
                return <FiCheckCircle />;
            case 'expired':
                return <FiXCircle />;
            case 'suspended':
                return <FiClock />;
            default:
                return <FiCheckCircle />;
        }
    };

    // Tarih formatını düzenleyen yardımcı fonksiyon
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        try {
            return new Date(dateString).toLocaleDateString('tr-TR');
        } catch (e) {
            return '-';
        }
    };

    // Üyelik bitiş tarihi rengini belirleyen fonksiyon
    const getExpiryDateStyle = (expiryDate) => {
        if (!expiryDate) return {};
        
        const now = new Date();
        const expiry = new Date(expiryDate);
        const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
        
        if (daysUntilExpiry < 0) {
            return { color: 'var(--danger)' }; // Süresi bitmiş - kırmızı
        } else if (daysUntilExpiry <= 30) {
            return { color: 'var(--warning)' }; // 30 gün kala - sarı
        } else {
            return { color: 'var(--success)' }; // Normal - yeşil
        }
    };

    // Sıralama için sütun başlığına tıklama işleyicisi
    const handleSort = (field) => {
        if (sortField === field) {
            // Aynı sütuna tekrar tıklandığında sıralama yönünü değiştir
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            // Farklı sütuna tıklandığında varsayılan olarak azalan sıralama
            setSortField(field);
            setSortDirection('desc');
        }
    };

    // Sıralama ikonunu gösteren yardımcı fonksiyon
    const renderSortIcon = (field) => {
        if (sortField !== field) return null;
        return sortDirection === 'asc' ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />;
    };

    if (loading) {
        return (
            <div className="admin-loading">
                <div className="loading-spinner"></div>
                <p>Kullanıcılar yükleniyor...</p>
            </div>
        );
    }

    const stats = getRoleStats();

    return (
        <div className="users-management">
            <div className="users-header">
                <h2>Kullanıcı Yönetimi</h2>
                <p>Sisteme kayıtlı tüm kullanıcıları ve üyelik durumlarını görüntüleyin</p>
            </div>

            {error && (
                <div style={{ 
                    padding: '1rem', 
                    marginBottom: '1.5rem', 
                    background: 'rgba(239, 68, 68, 0.1)', 
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '8px',
                    color: 'var(--danger)'
                }}>
                    <p>{error}</p>
                    <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
                        Not: Şu an örnek kullanıcılar gösteriliyor.
                    </p>
                </div>
            )}

            {/* İstatistik Kartları */}
            <div className="stats-grid">
                <div className="stat-card">
                    <div className="stat-icon">
                        <FiUsers />
                    </div>
                    <div className="stat-content">
                        <h3>{stats.total}</h3>
                        <p>Toplam Kullanıcı</p>
                    </div>
                </div>
                <div className="stat-card">
                    <div className="stat-icon user">
                        <FiCheckCircle />
                    </div>
                    <div className="stat-content">
                        <h3>{stats.activeCount}</h3>
                        <p>Aktif Üyelik</p>
                    </div>
                </div>
                <div className="stat-card">
                    <div className="stat-icon admin">
                        <FiXCircle />
                    </div>
                    <div className="stat-content">
                        <h3>{stats.expiredCount}</h3>
                        <p>Süresi Bitmiş</p>
                    </div>
                </div>
                <div className="stat-card">
                    <div className="stat-icon admin">
                        <FiShield />
                    </div>
                    <div className="stat-content">
                        <h3>{stats.adminCount}</h3>
                        <p>Admin</p>
                    </div>
                </div>
            </div>

            {/* Arama ve Filtreler */}
            <div className="users-controls">
                <div className="search-box">
                    <FiSearch className="search-icon" />
                    <input
                        type="text"
                        placeholder="Kullanıcı ara (ad, kullanıcı adı)..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <select 
                    value={selectedRole} 
                    onChange={(e) => setSelectedRole(e.target.value)}
                    className="role-filter"
                >
                    <option value="all">Tüm Roller</option>
                    <option value="admin">Admin</option>
                    <option value="user">Kullanıcı</option>
                </select>
                <select 
                    value={selectedStatus} 
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="role-filter"
                >
                    <option value="all">Tüm Durumlar</option>
                    <option value="active">Aktif</option>
                    <option value="expired">Süresi Bitmiş</option>
                    <option value="suspended">Askıya Alınmış</option>
                </select>
            </div>

            {/* Modern Kullanıcı Tablosu */}
            <div className="users-table-container">
                <table className="users-table">
                    <thead>
                        <tr>
                            <th>Kullanıcı</th>
                            <th className="sortable" onClick={() => handleSort('username')}>
                                <span>Kullanıcı Adı</span>
                                {renderSortIcon('username')}
                            </th>
                            <th className="sortable" onClick={() => handleSort('role')}>
                                <span>Rol</span>
                                {renderSortIcon('role')}
                            </th>
                            <th className="sortable" onClick={() => handleSort('membership_status')}>
                                <span>Üyelik Durumu</span>
                                {renderSortIcon('membership_status')}
                            </th>
                            <th className="sortable" onClick={() => handleSort('membership_expires_at')}>
                                <span>Üyelik Bitiş</span>
                                {renderSortIcon('membership_expires_at')}
                            </th>
                            <th className="sortable" onClick={() => handleSort('updated_at')}>
                                <span>Güncellenme</span>
                                {renderSortIcon('updated_at')}
                            </th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredUsers.length > 0 ? (
                            filteredUsers.map(user => (
                                <tr key={user.id}>
                                    <td className="user-cell">
                                        <div className="user-avatar-small">
                                            {user.avatar_url ? (
                                                <img src={user.avatar_url} alt={user.full_name} />
                                            ) : (
                                                <div className="avatar-placeholder-small">
                                                    {user.full_name ? user.full_name.charAt(0).toUpperCase() : 'U'}
                                                </div>
                                            )}
                                        </div>
                                        <div className="user-info">
                                            <div className="user-name">{user.full_name || 'İsim Belirtilmemiş'}</div>
                                        </div>
                                    </td>
                                    <td>@{user.username || '-'}</td>
                                    <td>
                                        <span className={getRoleBadgeClass(user.role)}>
                                            {user.role === 'admin' ? 'Admin' : 'Kullanıcı'}
                                        </span>
                                    </td>
                                    <td>
                                        <span className={getMembershipStatusBadgeClass(user.membership_status)}>
                                            {getMembershipStatusIcon(user.membership_status)}
                                            {getMembershipStatusText(user.membership_status)}
                                        </span>
                                    </td>
                                    <td>
                                        <span style={getExpiryDateStyle(user.membership_expires_at)}>
                                            {formatDate(user.membership_expires_at)}
                                        </span>
                                    </td>
                                    <td>{formatDate(user.updated_at)}</td>
                                    <td className="actions-cell">
                                        <Link to={`/admin/users/${user.id}`} className="btn-icon edit">
                                            <FiEdit3 size={16} />
                                        </Link>
                                        {user.username && (
                                            <Link 
                                                to={`/${user.username}`} 
                                                target="_blank"
                                                className="btn-icon view"
                                            >
                                                <FiExternalLink size={16} />
                                            </Link>
                                        )}
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan="7" className="no-results">
                                    <p>Arama kriterlerinize uygun kullanıcı bulunamadı.</p>
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default Users; 