-- public.profiles tablosunu oluşturuyoruz.
-- <PERSON><PERSON> tablo, auth.users tablosundaki her kullanı<PERSON><PERSON> için ek verileri tutar.
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user' NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Profiles tablosuna erişim i<PERSON> g<PERSON> k<PERSON>.
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public profiles are viewable by everyone." ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile." ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile." ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- <PERSON><PERSON> bir kullanıcı Supabase'e kaydolduğunda,
-- o<PERSON><PERSON><PERSON> olarak profiles tablosunda bir satır oluşturan fonksiyon.
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username)
  VALUES (new.id, new.raw_user_meta_data ->> 'username');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Bu fonksiyonu, auth.users tablosuna yeni kayıt eklendiğinde
-- tetiklenecek (trigger) şekilde ayarlıyoruz.
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Sonradan eklenen tüm sütunlar
ALTER TABLE public.profiles
ADD COLUMN title TEXT,
ADD COLUMN company TEXT,
ADD COLUMN whatsapp_number TEXT,
ADD COLUMN slogan TEXT,
ADD COLUMN instagram TEXT,
ADD COLUMN facebook TEXT,
ADD COLUMN linkedin TEXT,
ADD COLUMN x_twitter TEXT,
ADD COLUMN youtube TEXT,
ADD COLUMN tiktok TEXT,
ADD COLUMN bluesky TEXT,
ADD COLUMN telegram TEXT,
ADD COLUMN wechat TEXT,
ADD COLUMN mastodon TEXT,
ADD COLUMN snapchat TEXT,
ADD COLUMN spotify TEXT,
ADD COLUMN reddit TEXT,
ADD COLUMN pinterest TEXT; 