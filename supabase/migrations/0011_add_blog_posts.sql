-- Blog posts tablosu
CREATE TABLE public.blog_posts (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    title text NOT NULL,
    slug text UNIQUE NOT NULL,
    excerpt text,
    content text NOT NULL,
    featured_image text,
    status text DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    tags text[],
    meta_title text,
    meta_description text,
    published_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Blog kategorileri tablosu
CREATE TABLE public.blog_categories (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    color text DEFAULT '#3b82f6',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id, slug)
);

-- Blog post kategorileri bağlantı tablosu
CREATE TABLE public.blog_post_categories (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id uuid REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    category_id uuid REFERENCES public.blog_categories(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(post_id, category_id)
);

-- Blog medya dosyaları tablosu
CREATE TABLE public.blog_media (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    post_id uuid REFERENCES public.blog_posts(id) ON DELETE CASCADE,
    file_name text NOT NULL,
    file_path text NOT NULL,
    file_size bigint,
    mime_type text,
    alt_text text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- RLS politikaları
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_post_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_media ENABLE ROW LEVEL SECURITY;

-- Blog posts politikaları
CREATE POLICY "Users can view own blog posts" ON public.blog_posts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own blog posts" ON public.blog_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own blog posts" ON public.blog_posts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own blog posts" ON public.blog_posts
    FOR DELETE USING (auth.uid() = user_id);

-- Blog kategorileri politikaları
CREATE POLICY "Users can view own blog categories" ON public.blog_categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own blog categories" ON public.blog_categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own blog categories" ON public.blog_categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own blog categories" ON public.blog_categories
    FOR DELETE USING (auth.uid() = user_id);

-- Blog post kategorileri politikaları
CREATE POLICY "Users can manage blog post categories" ON public.blog_post_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.blog_posts bp 
            WHERE bp.id = post_id AND bp.user_id = auth.uid()
        )
    );

-- Blog medya politikaları
CREATE POLICY "Users can view own blog media" ON public.blog_media
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own blog media" ON public.blog_media
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own blog media" ON public.blog_media
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own blog media" ON public.blog_media
    FOR DELETE USING (auth.uid() = user_id);

-- Indexes
CREATE INDEX idx_blog_posts_user_id ON public.blog_posts(user_id);
CREATE INDEX idx_blog_posts_status ON public.blog_posts(status);
CREATE INDEX idx_blog_posts_published_at ON public.blog_posts(published_at);
CREATE INDEX idx_blog_posts_slug ON public.blog_posts(slug);
CREATE INDEX idx_blog_categories_user_id ON public.blog_categories(user_id);
CREATE INDEX idx_blog_categories_slug ON public.blog_categories(user_id, slug);
CREATE INDEX idx_blog_media_user_id ON public.blog_media(user_id);
CREATE INDEX idx_blog_media_post_id ON public.blog_media(post_id);

-- Updated at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Updated at triggers
CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON public.blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_categories_updated_at BEFORE UPDATE ON public.blog_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 