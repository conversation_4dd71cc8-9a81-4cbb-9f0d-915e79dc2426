{"name": "zylome-clone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hello-pangea/dnd": "^18.0.1", "@supabase/supabase-js": "^2.0.0", "all": "^0.0.0", "framer-motion": "^12.18.1", "quill": "^2.0.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-qr-code": "^2.0.16", "react-quill": "^2.0.0", "react-router-dom": "^6.4.3", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.0.22", "@types/react-dom": "^18.0.7", "@vitejs/plugin-react": "^2.2.0", "supabase": "^2.26.9", "vite": "^3.2.0"}}