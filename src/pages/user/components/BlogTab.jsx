import { memo, useState, useEffect } from 'react';
import { 
  FiSettings, 
  FiFileText, 
  FiPlus, 
  FiTrash2,
  FiEdit3,
  FiEye,
  FiClock,
  FiTag,
  FiCalendar,
  FiBarChart
} from 'react-icons/fi';
import { useBlog } from '../../../hooks/useBlog';
import BlogEditor from './BlogEditor';

const BlogTab = memo(({ 
  contentData, 
  activeTab, 
  handleContentChange, 
  handleTabToggle
}) => {
  const currentTab = contentData[activeTab];
  const tabColor = '#3b82f6';
  
  const { 
    posts, 
    categories, 
    loading, 
    createPost, 
    updatePost, 
    deletePost 
  } = useBlog();
  
  const [editorOpen, setEditorOpen] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [filter, setFilter] = useState('all'); // all, draft, published, archived

  // Post filtreleme
  const filteredPosts = posts.filter(post => {
    if (filter === 'all') return true;
    return post.status === filter;
  });

  // Post durumu renkeri
  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return '#10b981';
      case 'draft': return '#f59e0b';
      case 'archived': return '#6b7280';
      default: return '#6b7280';
    }
  };

  // Post durumu metni
  const getStatusText = (status) => {
    switch (status) {
      case 'published': return 'Yayında';
      case 'draft': return 'Taslak';
      case 'archived': return 'Arşivlendi';
      default: return 'Bilinmiyor';
    }
  };

  // Yeni post oluştur
  const handleCreatePost = () => {
    setEditingPost(null);
    setEditorOpen(true);
  };

  // Post düzenle
  const handleEditPost = (post) => {
    setEditingPost(post);
    setEditorOpen(true);
  };

  // Post kaydet
  const handleSavePost = async (postData) => {
    try {
      if (editingPost) {
        await updatePost(editingPost.id, postData);
      } else {
        await createPost(postData);
      }
      setEditorOpen(false);
      setEditingPost(null);
      // Success toast veya notification eklenebilir
    } catch (error) {
      console.error('Post kaydetme hatası:', error);
      throw error;
    }
  };

  // Post sil
  const handleDeletePost = async (postId) => {
    if (!confirm('Bu yazıyı silmek istediğinizden emin misiniz?')) return;
    
    try {
      await deletePost(postId);
    } catch (error) {
      console.error('Post silme hatası:', error);
      alert('Post silinirken hata oluştu');
    }
  };

  // İçerik önizlemesi
  const getContentPreview = (content) => {
    // HTML taglarını kaldır ve ilk 150 karakteri al
    const text = content.replace(/<[^>]*>/g, '').trim();
    return text.length > 150 ? text.substring(0, 150) + '...' : text;
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="content-main-area">
      <div className="content-header-info">
        <div className="content-icon" style={{ backgroundColor: `${tabColor}20`, color: tabColor }}>
          <FiFileText size={24} />
        </div>
        <div className="content-title">
          <h2>{currentTab.title}</h2>
          <p>{currentTab.description}</p>
          <div className="content-status">
            <span className={`status-indicator ${currentTab.enabled ? 'active' : 'inactive'}`}>
              {currentTab.enabled ? 'Aktif' : 'Pasif'}
            </span>
            <span className="status-indicator">
              {posts.length} Yazı
            </span>
            <span className="status-indicator draft">
              {posts.filter(p => p.status === 'draft').length} Taslak
            </span>
          </div>
        </div>
        <div className="content-actions">
          <button className="action-btn" title="Ayarlar">
            <FiSettings />
          </button>
          <button className="action-btn" title="İstatistikler">
            <FiBarChart />
          </button>
        </div>
        <div className="content-toggle">
          <button 
            className={`toggle-switch ${currentTab.enabled ? 'active' : ''}`}
            onClick={() => handleTabToggle(activeTab)}
          >
            <div className="toggle-slider"></div>
            <span className="toggle-label">
              {currentTab.enabled ? 'Aktif' : 'Pasif'}
            </span>
          </button>
        </div>
      </div>

      <div className="content-body">
        <div className="content-settings-section">
          <h3><FiSettings size={20} /> Genel Ayarlar</h3>
          <div className="settings-grid">
            <div className="form-group">
              <label>Sekme Başlığı</label>
              <input
                type="text"
                value={currentTab.title}
                onChange={(e) => handleContentChange(activeTab, 'title', e.target.value)}
                placeholder="Sekme başlığını girin"
              />
            </div>

            <div className="form-group">
              <label>Açıklama</label>
              <textarea
                value={currentTab.description}
                onChange={(e) => handleContentChange(activeTab, 'description', e.target.value)}
                placeholder="Sekme açıklamasını girin"
                rows={3}
              />
            </div>
          </div>
        </div>

        {currentTab.enabled && (
          <div className="content-items-section">
            <div className="section-header">
              <h3><FiFileText size={20} /> Blog Yazıları</h3>
              <div className="section-actions">
                <select 
                  value={filter} 
                  onChange={(e) => setFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">Tümü ({posts.length})</option>
                  <option value="published">Yayında ({posts.filter(p => p.status === 'published').length})</option>
                  <option value="draft">Taslaklar ({posts.filter(p => p.status === 'draft').length})</option>
                  <option value="archived">Arşivlenenler ({posts.filter(p => p.status === 'archived').length})</option>
                </select>
                <button className="add-content-btn" onClick={handleCreatePost}>
                  <FiPlus />
                  Yeni Yazı
                </button>
              </div>
            </div>

            {loading ? (
              <div className="loading-state">
                <div className="loading-spinner"></div>
                <p>Blog yazıları yükleniyor...</p>
              </div>
            ) : (
              <div className="blog-posts-grid">
                {filteredPosts.map(post => (
                  <div key={post.id} className="blog-post-card">
                    <div className="post-header">
                      <div className="post-status" style={{ backgroundColor: getStatusColor(post.status) }}>
                        {getStatusText(post.status)}
                      </div>
                      <div className="post-actions">
                        <button 
                          className="edit-post-btn" 
                          onClick={() => handleEditPost(post)}
                          title="Düzenle"
                        >
                          <FiEdit3 />
                        </button>
                        <button 
                          className="delete-post-btn" 
                          onClick={() => handleDeletePost(post.id)}
                          title="Sil"
                        >
                          <FiTrash2 />
                        </button>
                      </div>
                    </div>

                    {post.featured_image && (
                      <div className="post-featured-image">
                        <img src={post.featured_image} alt={post.title} />
                      </div>
                    )}

                    <div className="post-content">
                      <h4 className="post-title">{post.title}</h4>
                      
                      {post.excerpt && (
                        <p className="post-excerpt">{post.excerpt}</p>
                      )}
                      
                      <p className="post-content-preview">
                        {getContentPreview(post.content)}
                      </p>

                      <div className="post-meta">
                        <div className="post-meta-item">
                          <FiCalendar size={14} />
                          <span>{formatDate(post.created_at)}</span>
                        </div>
                        
                        {post.tags && post.tags.length > 0 && (
                          <div className="post-meta-item">
                            <FiTag size={14} />
                            <div className="post-tags">
                              {post.tags.slice(0, 3).map(tag => (
                                <span key={tag} className="post-tag">
                                  {tag}
                                </span>
                              ))}
                              {post.tags.length > 3 && (
                                <span className="post-tag-more">
                                  +{post.tags.length - 3}
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                        {post.blog_post_categories && post.blog_post_categories.length > 0 && (
                          <div className="post-meta-item">
                            <div className="post-categories">
                              {post.blog_post_categories.map(pc => (
                                <span 
                                  key={pc.blog_categories.id} 
                                  className="post-category"
                                  style={{ backgroundColor: pc.blog_categories.color + '20', color: pc.blog_categories.color }}
                                >
                                  {pc.blog_categories.name}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {filteredPosts.length === 0 && (
                  <div className="empty-state">
                    <div className="empty-icon">
                      <FiFileText size={48} />
                    </div>
                    <h4>
                      {filter === 'all' 
                        ? 'Henüz blog yazısı yok' 
                        : `${getStatusText(filter)} yazı bulunamadı`
                      }
                    </h4>
                    <p>
                      {filter === 'all' 
                        ? 'İlk blog yazınızı oluşturun' 
                        : 'Filtre seçiminizi değiştirmeyi deneyin'
                      }
                    </p>
                    {filter === 'all' && (
                      <button className="add-first-btn" onClick={handleCreatePost}>
                        <FiPlus />
                        İlk Yazıyı Oluştur
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {!currentTab.enabled && (
          <div className="disabled-state">
            <div className="disabled-icon">
              <FiFileText size={48} />
            </div>
            <h4>Blog sekmesi pasif</h4>
            <p>Bu sekmeyi aktif hale getirmek için yukarıdaki toggle'ı kullanın</p>
            <button className="enable-btn" onClick={() => handleTabToggle(activeTab)}>
              Sekmeyi Aktif Et
            </button>
          </div>
        )}
      </div>

      {/* Blog Editor Modal */}
      <BlogEditor
        post={editingPost}
        isOpen={editorOpen}
        onClose={() => {
          setEditorOpen(false);
          setEditingPost(null);
        }}
        onSave={handleSavePost}
      />
    </div>
  );
});

BlogTab.displayName = 'BlogTab';

export default BlogTab; 