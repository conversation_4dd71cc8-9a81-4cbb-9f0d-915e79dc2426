import React, { memo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSidebar } from '../../../contexts/SidebarContext';
import SidebarNavigation from './SidebarNavigation';
import { FiHome } from 'react-icons/fi';
import './Sidebar.css';

// TABS sabiti - tüm sekme tanımları
import { TABS } from '../../../constants/tabs';

const Sidebar = memo(() => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    contentData, 
    tabOrder, 
    draggedTab,
    dropTarget,
    dropPosition,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    handleTabToggle,
    loading
  } = useSidebar();
  
  // Aktif sekmeyi belirle
  const activeTab = React.useMemo(() => {
    const path = location.pathname;
    
    // Blog özel durumu
    if (path.includes('/blogs')) {
      return 'blog';
    }
    
    // SSS özel durumu
    if (path.includes('/faqs')) {
      return 'faq';
    }
    
    // Diğer sekmeler için path kontrolü
    for (const tab of TABS) {
      if (path.includes(`/${tab.id}`)) {
        return tab.id;
      }
    }
    
    return null;
  }, [location.pathname]);
  
  const handleHomeClick = () => {
    navigate('/dashboard');
  };
  
  if (loading) {
    return (
      <aside className="sidebar">
        <div className="sidebar-header">
          <button className="sidebar-home-button" onClick={handleHomeClick}>
            <FiHome size={20} />
            <span>İçerik Anasayfa</span>
          </button>
        </div>
        <div className="sidebar-loading">
          <div className="loading-spinner"></div>
          <p>Yükleniyor...</p>
        </div>
      </aside>
    );
  }
  
  return (
    <aside className="sidebar">
      <div className="sidebar-header">
        <button className="sidebar-home-button" onClick={handleHomeClick}>
          <FiHome size={20} />
          <span>İçerik Anasayfa</span>
        </button>
      </div>
      
      <SidebarNavigation 
        TABS={TABS}
        tabOrder={tabOrder}
        contentData={contentData}
        draggedTab={draggedTab}
        dropTarget={dropTarget}
        dropPosition={dropPosition}
        activeTab={activeTab}
        location={location}
        navigate={navigate}
        handleDragStart={handleDragStart}
        handleDragOver={handleDragOver}
        handleDragLeave={handleDragLeave}
        handleDrop={handleDrop}
        handleDragEnd={handleDragEnd}
        handleTabToggle={handleTabToggle}
      />
    </aside>
  );
});

Sidebar.displayName = 'Sidebar';

export default Sidebar;
