/* Notification Components */

/* Toast Notifications */
.notification-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 9999;
  max-width: 400px;
  min-width: 300px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  animation: slideInToast 0.3s ease-out;
}

@keyframes slideInToast {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing);
  padding: var(--spacing);
}

.notification-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.notification-message {
  flex: 1;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
}

.notification-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 4px;
  transition: var(--transition);
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* Toast Types */
.notification-toast.success {
  border-left: 4px solid var(--success);
}

.notification-toast.success .notification-icon {
  color: var(--success);
}

.notification-toast.error {
  border-left: 4px solid var(--danger);
}

.notification-toast.error .notification-icon {
  color: var(--danger);
}

.notification-toast.warning {
  border-left: 4px solid var(--warning);
}

.notification-toast.warning .notification-icon {
  color: var(--warning);
}

.notification-toast.info {
  border-left: 4px solid var(--accent-primary);
}

.notification-toast.info .notification-icon {
  color: var(--accent-primary);
}

/* Modern Alerts */
.modern-alert {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  max-width: 500px;
  min-width: 320px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  animation: slideInToast 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing);
  padding: var(--spacing-lg);
}

.alert-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  animation: bounceIn 0.6s ease-out 0.2s both;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.alert-text {
  flex: 1;
  color: var(--text-primary);
  font-size: 0.9375rem;
  font-weight: 500;
  line-height: 1.5;
}

.alert-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 6px;
  transition: var(--transition);
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  transform: scale(1.1);
}

.alert-close:active {
  transform: scale(0.95);
}

/* Alert Types */
.alert-success {
  border-left: 4px solid var(--success);
}

.alert-error {
  border-left: 4px solid var(--danger);
}

.alert-warning {
  border-left: 4px solid var(--warning);
}

.alert-info {
  border-left: 4px solid var(--accent-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
    min-width: auto;
    transform: none;
  }
  
  .modern-alert {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    max-width: none;
    min-width: auto;
    transform: none;
  }
  
  .notification-content,
  .alert-content {
    padding: var(--spacing);
  }
  
  .alert-text {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .notification-toast,
  .modern-alert {
    margin: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    top: 0.5rem;
  }
  
  .alert-icon {
    width: 20px;
    height: 20px;
  }
  
  .alert-close {
    width: 24px;
    height: 24px;
  }
} 