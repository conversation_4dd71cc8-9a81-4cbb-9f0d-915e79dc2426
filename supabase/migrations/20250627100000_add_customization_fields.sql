-- Add customization fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS theme_color TEXT DEFAULT '#6366f1',
ADD COLUMN IF NOT EXISTS background_style TEXT DEFAULT 'gradient',
ADD COLUMN IF NOT EXISTS card_style TEXT DEFAULT 'modern',
ADD COLUMN IF NOT EXISTS button_style TEXT DEFAULT 'rounded',
ADD COLUMN IF NOT EXISTS font_style TEXT DEFAULT 'modern',
ADD COLUMN IF NOT EXISTS layout_style TEXT DEFAULT 'default',
ADD COLUMN IF NOT EXISTS show_social_icons BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS show_contact_info BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS show_qr_code BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS profile_visibility TEXT DEFAULT 'public';

-- Add comments for documentation
COMMENT ON COLUMN public.profiles.theme_color IS 'User selected theme color (hex code)';
COMMENT ON COLUMN public.profiles.background_style IS 'Profile background style: gradient, solid, pattern';
COMMENT ON COLUMN public.profiles.card_style IS 'Profile card style: modern, classic, minimal';
COMMENT ON COLUMN public.profiles.button_style IS 'Button style: rounded, square, pill';
COMMENT ON COLUMN public.profiles.font_style IS 'Font style: modern, classic, minimal';
COMMENT ON COLUMN public.profiles.layout_style IS 'Layout style: default, compact, spacious';
COMMENT ON COLUMN public.profiles.show_social_icons IS 'Whether to show social media icons on profile';
COMMENT ON COLUMN public.profiles.show_contact_info IS 'Whether to show contact information on profile';
COMMENT ON COLUMN public.profiles.show_qr_code IS 'Whether to show QR code on profile';
COMMENT ON COLUMN public.profiles.profile_visibility IS 'Profile visibility: public, private';

-- Create indexes for frequently used customization fields
CREATE INDEX IF NOT EXISTS idx_profiles_theme_color ON public.profiles(theme_color);
CREATE INDEX IF NOT EXISTS idx_profiles_background_style ON public.profiles(background_style);
CREATE INDEX IF NOT EXISTS idx_profiles_profile_visibility ON public.profiles(profile_visibility); 