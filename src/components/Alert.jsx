import React, { useState, useEffect } from 'react';
import { FiCheckCircle, FiXCircle, FiAlertTriangle, FiInfo, FiX } from 'react-icons/fi';
import '../styles/components/alerts.css';

const alertConfig = {
  success: { icon: FiCheckCircle, title: 'Başar<PERSON>l<PERSON>' },
  error: { icon: FiXCircle, title: '<PERSON><PERSON>' },
  warning: { icon: FiAlertTriangle, title: 'Uyarı' },
  info: { icon: FiInfo, title: '<PERSON><PERSON><PERSON>' },
};

const Alert = ({ type, text, onDismiss }) => {
  const [visible, setVisible] = useState(false);
  
  useEffect(() => {
    if (text) {
      setVisible(true);
      const timer = setTimeout(() => {
        handleDismiss();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [text, type]);

  const handleDismiss = () => {
    setVisible(false);
    // Allow animation to complete before calling onDismiss
    setTimeout(() => {
      onDismiss();
    }, 300);
  };
  
  if (!text || !type) return null;

  const config = alertConfig[type] || alertConfig.info;
  const Icon = config.icon;

  return (
    <div className={`alert ${type} ${visible ? '' : 'exit'}`}>
      <div className="alert-icon">
        <Icon />
      </div>
      <div className="alert-content">
        <p className="alert-title">{config.title}</p>
        <p className="alert-message">{text}</p>
      </div>
      <button onClick={handleDismiss} className="alert-close">
        <FiX />
      </button>
      <div className="alert-progress"></div>
    </div>
  );
};

const AlertContainer = ({ message, onDismiss }) => {
  if (!message || !message.text) return null;

  return (
    <div className="alert-component">
      <Alert type={message.type} text={message.text} onDismiss={onDismiss} />
    </div>
  );
};

export default AlertContainer; 