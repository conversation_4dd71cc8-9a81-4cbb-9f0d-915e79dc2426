import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import LoginModal from '../components/LoginModal';
import { 
  FiHome, 
  FiUsers, 
  FiTrendingUp, 
  FiShield, 
  FiStar, 
  FiArrowRight, 
  FiCheck,
  FiMenu,
  FiX,
  FiTarget,
  FiGlobe,
  FiBarChart,
  FiUserCheck,
  FiMapPin,
  FiCamera,
  FiMessageCircle,
  FiDollarSign,
  FiLogIn,
  FiGrid,
  FiFileText
} from 'react-icons/fi';

const Home = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const features = [
    {
      icon: <FiUserCheck className="w-8 h-8" />,
      title: "Profesyonel Profil",
      description: "Kişisel bilgiler<PERSON>zi, sosyal medya hesaplarınızı ve daha fazlasını ekleyin"
    },
    {
      icon: <FiGrid className="w-8 h-8" />,
      title: "Dijital Bağlantılar",
      description: "Bağlantılarınızı online sergileyip potansiyel müşterilere ulaşın"
    },
    {
      icon: <FiTrendingUp className="w-8 h-8" />,
      title: "Satış Analizi",
      description: "Detaylı raporlar ve analizlerle performansınızı takip edin"
    },
    {
      icon: <FiTarget className="w-8 h-8" />,
      title: "Hedef Odaklı",
      description: "Satış hedeflerinizi belirleyin ve ilerlemenizi görsel olarak takip edin"
    },
    {
      icon: <FiGlobe className="w-8 h-8" />,
      title: "Online Görünürlük",
      description: "Profesyonel profil sayfanızla dijital dünyada öne çıkın"
    },
    {
      icon: <FiBarChart className="w-8 h-8" />,
      title: "Raporlama",
      description: "Comprehensive satış ve performans raporları ile stratejinizi geliştirin"
    }
  ];

  const testimonials = [
    {
      name: "Ahmet Yılmaz",
      title: "Gayrimenkul Danışmanı",
      content: "Zylo sayesinde müşteri ağım %40 arttı. Dijital presence'im çok güçlendi.",
      rating: 5,
      image: null
    },
    {
      name: "Zeynep Kaya",
      title: "Emlak Ofisi Sahibi",
      content: "Ekibimin tüm performansını tek yerden takip edebilmek harika. Çok pratik bir çözüm.",
      rating: 5,
      image: null
    },
    {
      name: "Murat Demir",
      title: "Freelance Emlakçı",
      content: "Profesyonel profil sayfam sayesinde müşteriler beni daha kolay buluyor.",
      rating: 5,
      image: null
    },
    {
      name: "Ayşe Yılmaz",
      title: "Pazarlama Uzmanı",
      image: "https://randomuser.me/api/portraits/women/68.jpg",
      content: "Zylo sayesinde müşteri ağım %40 arttı. Dijital presence'im çok güçlendi.",
      rating: 5
    }
  ];

  const stats = [
    { number: "500+", label: "Aktif Emlakçı" },
    { number: "10K+", label: "Satış İşlemi" },
    { number: "₺2.5M+", label: "İşlem Hacmi" },
    { number: "98%", label: "Müşteri Memnuniyeti" }
  ];

  const handleLoginSuccess = async (user) => {
    console.log('Kullanıcı giriş yaptı:', user);
    
    try {
      // Kullanıcının rolünü kontrol et
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      // Admin ise admin paneline, kullanıcı ise dashboard'a yönlendir
      if (profile?.role === 'admin') {
        window.location.href = '/login'; // Admin login sayfasına
      } else {
        window.location.href = '/dashboard'; // Kullanıcı dashboard'ına
      }
    } catch (error) {
      console.error('Rol kontrol hatası:', error);
      // Hata durumunda kullanıcı dashboard'ına yönlendir
      window.location.href = '/dashboard';
    }
  };

  // Mobil menu açık olduğunda body scroll'u engelle
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  return (
    <div className="landing-page">
      {/* Header */}
      <header className="landing-header">
        <div className="container">
          <div className="nav-wrapper">
            <div className="logo">
              <FiHome className="logo-icon" />
              <span className="logo-text">Zylo</span>
            </div>
            
            <nav className="nav-menu">
              <a href="#features">Özellikler</a>
              <a href="#about">Hakkımızda</a>
              <a href="#testimonials">Referanslar</a>
              <a href="#pricing">Fiyatlar</a>
            </nav>
            
            <div className={`mobile-nav ${isMobileMenuOpen ? 'active' : ''}`}>
              <div className="mobile-menu-header">
                <div className="mobile-menu-logo">
                  <FiHome className="logo-icon" />
                  <span>Zylo</span>
                </div>
                <button 
                  className="mobile-menu-close"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <FiX />
                </button>
              </div>
              
              <div className="mobile-menu-content">
                <a href="#features" onClick={() => setIsMobileMenuOpen(false)}>
                  <FiHome />
                  <span>Özellikler</span>
                </a>
                <a href="#about" onClick={() => setIsMobileMenuOpen(false)}>
                  <FiUsers />
                  <span>Hakkımızda</span>
                </a>
                <a href="#testimonials" onClick={() => setIsMobileMenuOpen(false)}>
                  <FiStar />
                  <span>Referanslar</span>
                </a>
                <a href="#pricing" onClick={() => setIsMobileMenuOpen(false)}>
                  <FiDollarSign />
                  <span>Fiyatlar</span>
                </a>
                
                <div className="mobile-nav-actions">
                  <button 
                    className="mobile-btn-register"
                    onClick={() => {
                      setIsLoginModalOpen(true);
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    <FiLogIn />
                    <span>Giriş Yap / Kayıt Ol</span>
                  </button>
                </div>
              </div>
            </div>
            
            <div className="nav-actions">
              <button 
                className="btn-register"
                onClick={() => setIsLoginModalOpen(true)}
              >
                Giriş Yap / Kayıt Ol
              </button>
            </div>
            
            <button 
              className={`mobile-menu-toggle ${isMobileMenuOpen ? 'hidden' : ''}`}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <FiMenu />
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-background">
          <div className="hero-gradient"></div>
          <div className="hero-pattern"></div>
        </div>
        
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1>
                Emlak Satışlarınızı 
                <span className="highlight"> Dijitalleştirin</span>
              </h1>
              <p>
                Gayrimenkul profesyonelleri için tasarlanmış modern platform. 
                Bağlantılarınızı sergileyip, müşteri ilişkilerinizi yönetin ve 
                satışlarınızı artırın.
              </p>
              
              <div className="hero-actions">
                <button 
                  className="btn-primary-large"
                  onClick={() => setIsLoginModalOpen(true)}
                >
                  Ücretsiz Başla
                  <FiArrowRight />
                </button>
                <button className="btn-demo">
                  <FiUsers />
                  Demo İzle
                </button>
              </div>
              
              <div className="hero-stats">
                {stats.map((stat, index) => (
                  <div key={index} className="stat-item">
                    <span className="stat-number">{stat.number}</span>
                    <span className="stat-label">{stat.label}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="hero-visual">
              <div className="mockup-container">
                <div className="mockup-phone">
                  <div className="phone-screen">
                    <div className="screen-content">
                      <div className="profile-header">
                        <div className="profile-avatar"></div>
                        <div className="profile-info">
                          <div className="profile-name"></div>
                          <div className="profile-title"></div>
                        </div>
                      </div>
                      <div className="property-cards">
                        <div className="property-card"></div>
                        <div className="property-card"></div>
                        <div className="property-card"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features-section">
        <div className="container">
          <div className="section-header">
            <h2>Güçlü Özellikler</h2>
            <p>Emlak profesyonelleri için özel olarak tasarlanmış araçlar</p>
          </div>
          
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">
                  {feature.icon}
                </div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about-section">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2>Neden Zylo?</h2>
              <p>
                Gayrimenkul sektöründeki deneyimimizi teknoloji ile birleştirerek, 
                emlak profesyonellerinin ihtiyaçlarına özel bir platform geliştirdik.
              </p>
              
              <div className="about-features">
                <div className="about-feature">
                  <FiCheck />
                  <span>Kolay kullanım ve hızlı kurulum</span>
                </div>
                <div className="about-feature">
                  <FiCheck />
                  <span>Mobil uyumlu responsive tasarım</span>
                </div>
                <div className="about-feature">
                  <FiCheck />
                  <span>Güvenli ve güncel teknoloji</span>
                </div>
                <div className="about-feature">
                  <FiCheck />
                  <span>7/24 teknik destek</span>
                </div>
              </div>
            </div>
            
            <div className="about-visual">
              <div className="dashboard-mockup">
                <div className="mockup-header">
                  <div className="mockup-tabs">
                    <div className="tab active">Dashboard</div>
                    <div className="tab">Bağlantılar</div>
                    <div className="tab">Müşteriler</div>
                  </div>
                </div>
                <div className="mockup-content">
                  <div className="chart-placeholder"></div>
                  <div className="cards-placeholder">
                    <div className="card-placeholder"></div>
                    <div className="card-placeholder"></div>
                    <div className="card-placeholder"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="testimonials-section">
        <div className="container">
          <div className="section-header">
            <h2>Müşteri Yorumları</h2>
            <p>Zylo kullanan emlak profesyonellerinin deneyimleri</p>
          </div>
          
          <div className="testimonials-grid">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="testimonial-card">
                <div className="testimonial-content">
                  <div className="stars">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <FiStar key={i} className="star filled" />
                    ))}
                  </div>
                  <p>"{testimonial.content}"</p>
                </div>
                <div className="testimonial-author">
                  <div className="author-avatar">
                    {testimonial.image ? (
                      <img src={testimonial.image} alt={testimonial.name} />
                    ) : (
                      <FiUserCheck />
                    )}
                  </div>
                  <div className="author-info">
                    <h4>{testimonial.name}</h4>
                    <span>{testimonial.title}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Hemen Başlayın</h2>
            <p>
              Sadece 2 dakikada hesabınızı oluşturun ve 
              emlak kariyerinizi dijitalleştirin.
            </p>
            <div className="cta-actions">
              <button 
                className="btn-primary-large"
                onClick={() => setIsLoginModalOpen(true)}
              >
                Ücretsiz Hesap Oluştur
                <FiArrowRight />
              </button>
              <p className="cta-note">
                Kredi kartı gerektirmez • 14 gün ücretsiz deneme
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <div className="logo">
                <FiHome className="logo-icon" />
                <span className="logo-text">Zylo</span>
              </div>
              <p>Emlak profesyonelleri için modern dijital çözümler.</p>
            </div>
            
            <div className="footer-links">
              <div className="link-group">
                <h4>Platform</h4>
                <a href="#features">Özellikler</a>
                <a href="#pricing">Fiyatlar</a>
                <a href="#demo">Demo</a>
              </div>
              <div className="link-group">
                <h4>Destek</h4>
                <a href="#help">Yardım</a>
                <a href="#contact">İletişim</a>
                <a href="#docs">Dokümantasyon</a>
              </div>
              <div className="link-group">
                <h4>Şirket</h4>
                <a href="#about">Hakkımızda</a>
                <a href="#blog">Blog</a>
                <a href="#careers">Kariyer</a>
              </div>
            </div>
          </div>
          
          <div className="footer-bottom">
            <p>© 2025 Zylo. Tüm hakları saklıdır.</p>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      <LoginModal 
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onSuccess={handleLoginSuccess}
      />
    </div>
  );
};

export default Home; 