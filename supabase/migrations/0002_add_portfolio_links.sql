-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n harici ba<PERSON>ını (sosyal medya, web sitesi vb.) saklamak için yeni tablo.
CREATE TABLE public.portfolio_links (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  icon_url TEXT,
  display_order SMALLINT DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bu tablo için satır seviyesi gü<PERSON> (Row Level Security) aktif ediliyor.
ALTER TABLE public.portfolio_links ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON> bağlantı linklerini görüntüleyebilmesi için bir kural.
CREATE POLICY "Public portfolio links are viewable by everyone."
  ON public.portfolio_links FOR SELECT
  USING (true);

-- <PERSON><PERSON><PERSON><PERSON><PERSON>ların sadece kendi profillerine link ekleyebilmesi için bir kural.
CREATE POLICY "Users can insert their own portfolio links."
  ON public.portfolio_links FOR INSERT
  WITH CHECK (profile_id = (SELECT id FROM public.profiles WHERE auth.uid() = id));

-- Kullanıcıların sadece kendi linklerini güncelleyebilmesi için bir kural.
CREATE POLICY "Users can update their own portfolio links."
  ON public.portfolio_links FOR UPDATE
  USING (profile_id = (SELECT id FROM public.profiles WHERE auth.uid() = id));

-- Kullanıcıların sadece kendi linklerini silebilmesi için bir kural.
CREATE POLICY "Users can delete their own portfolio links."
  ON public.portfolio_links FOR DELETE
  USING (profile_id = (SELECT id FROM public.profiles WHERE auth.uid() = id)); 