import { useState } from 'react';
import { supabase } from '../supabaseClient';
import { useNavigate } from 'react-router-dom';
import { FiMail, FiLock, FiEye, FiEyeOff, FiArrowRight, FiShield } from 'react-icons/fi';

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');
      
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) throw error;
      
      // Başarılı giriş
      navigate('/admin');
    } catch (error) {
      setError(error.error_description || error.message || '<PERSON><PERSON><PERSON> yapılırken bir hata oluş<PERSON>');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="bg-gradient-animated"></div>
        <div className="bg-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          {/* Header */}
          <div className="login-header">
            <div className="login-logo">
              <FiShield size={40} />
            </div>
            <h1>Zylo Admin</h1>
            <p>Yönetim paneline hoş geldiniz</p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="login-error">
              <p>{error}</p>
            </div>
          )}

          {/* Login Form */}
          <form onSubmit={handleLogin} className="login-form">
            <div className="form-group">
              <label htmlFor="email">E-posta Adresi</label>
              <div className="input-wrapper">
                <FiMail className="input-icon" />
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="password">Şifre</label>
              <div className="input-wrapper">
                <FiLock className="input-icon" />
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  required
                  disabled={loading}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
            </div>

            <button 
              type="submit" 
              className="login-button"
              disabled={loading || !email || !password}
            >
              {loading ? (
                <>
                  <div className="spinner"></div>
                  Giriş Yapılıyor...
                </>
              ) : (
                <>
                  Giriş Yap
                  <FiArrowRight />
                </>
              )}
            </button>
          </form>

          {/* Footer */}
          <div className="login-footer">
            <p>© 2025 Zylo Admin Panel</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login; 