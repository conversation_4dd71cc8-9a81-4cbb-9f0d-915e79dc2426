-- Add membership expiry fields to profiles table
ALTER TABLE profiles 
ADD COLUMN membership_expires_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN membership_status TEXT DEFAULT 'active' CHECK (membership_status IN ('active', 'expired', 'suspended'));

-- Add index for better performance on expiry checks
CREATE INDEX profiles_membership_expires_at_idx ON profiles(membership_expires_at);
CREATE INDEX profiles_membership_status_idx ON profiles(membership_status);

-- Create a function to automatically set membership status based on expiry date
CREATE OR REPLACE FUNCTION update_membership_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If membership_expires_at is set and is in the past, mark as expired
  IF NEW.membership_expires_at IS NOT NULL AND NEW.membership_expires_at < NOW() THEN
    NEW.membership_status := 'expired';
  -- If membership_expires_at is in the future or null, and not suspended, mark as active
  ELSIF (NEW.membership_expires_at IS NULL OR NEW.membership_expires_at > NOW()) 
        AND NEW.membership_status != 'suspended' THEN
    NEW.membership_status := 'active';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update membership status
CREATE TRIGGER trigger_update_membership_status
  BEFORE INSERT OR UPDATE OF membership_expires_at, membership_status ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_membership_status();

-- Create a function to check and update expired memberships (can be called periodically)
CREATE OR REPLACE FUNCTION check_expired_memberships()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE profiles 
  SET membership_status = 'expired'
  WHERE membership_expires_at < NOW() 
    AND membership_status = 'active';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Set default membership expiry for existing users (1 year from now)
UPDATE profiles 
SET membership_expires_at = NOW() + INTERVAL '1 year'
WHERE membership_expires_at IS NULL;
