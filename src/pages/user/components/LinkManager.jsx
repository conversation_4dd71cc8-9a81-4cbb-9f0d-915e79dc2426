import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentLinks } from '../../../contexts/ContentContext';
import { useAuth } from '../../../hooks/useAuth';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  FiPlus, FiArrowLeft, FiSave, FiEdit, FiTrash2, 
  FiSearch, FiX, FiUpload, FiImage,
  FiLink, FiMenu, FiInfo
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const LinkManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const { user } = useAuth();
  const { 
    links, 
    loading, 
    getLinkById, 
    createLink, 
    updateLink, 
    deleteLink, 
    updateLinkOrder 
  } = useContentLinks();

  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [formData, setFormData] = useState({
    title: '',
    link: '',
    icon_url: ''
  });

  const [iconFile, setIconFile] = useState(null);
  const [iconPreview, setIconPreview] = useState('');

  useEffect(() => {
    if (mode === 'edit' && id) {
      const loadLink = async () => {
        try {
          const data = await getLinkById(id);
          if (data) {
            setFormData({
              title: data.title || '',
              link: data.link || '',
              icon_url: data.icon_url || ''
            });
            if (data.icon_url) {
              setIconPreview(data.icon_url);
            }
          }
        } catch (error) {
          navigate('/dashboard/links/list');
        }
      };
      loadLink();
    }
  }, [mode, id, getLinkById, navigate]);

  const handleIconFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      showToast.error('Lütfen geçerli bir resim dosyası seçin!');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      showToast.error('Dosya boyutu 5MB\'dan küçük olmalıdır!');
      return;
    }

    setIconFile(file);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setIconPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.link.trim()) {
      showToast.warning('Başlık ve link alanları boş bırakılamaz!');
      return;
    }

    setSaving(true);
    try {
      const linkData = {
        title: formData.title.trim(),
        link: formData.link.trim(),
        icon_url: formData.icon_url
      };

      if (mode === 'edit' && id) {
        await updateLink(id, linkData, iconFile);
      } else {
        await createLink(linkData, iconFile);
      }
      navigate('/dashboard/links/list');
    } catch (error) {
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteClick = async (linkId) => {
    if (!window.confirm('Bu bağlantıyı silmek istediğinizden emin misiniz?')) {
      return;
    }
    try {
      await deleteLink(linkId);
    } catch (error) {
    }
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(links);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    try {
      await updateLinkOrder(items);
    } catch (error) {
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const removeIcon = () => {
    setIconFile(null);
    setIconPreview('');
    setFormData(prev => ({ ...prev, icon_url: '' }));
  };

  const filteredLinks = links.filter(link =>
    link.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      {mode === 'add' || mode === 'edit' ? (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)', color: '#3b82f6' }}>
                <FiLink size={24} />
              </div>
              <div className="content-title">
                <h2>{mode === 'edit' ? 'Bağlantı Düzenle' : 'Yeni Bağlantı Ekle'}</h2>
                <p>{mode === 'edit' ? 'Mevcut bağlantıyı düzenleyin' : 'Yeni bir bağlantı ekleyin'}</p>
              </div>
            </div>
            <div className="content-actions">
              <button 
                className="action-button secondary"
                onClick={() => navigate('/dashboard/links/list')}
              >
                <FiArrowLeft size={18} />
                <span>Listeye Dön</span>
              </button>
            </div>
          </div>
          
          <div className="content-body">
            <form onSubmit={handleSubmit} className="link-form">
              <div className="form-main-content">
                <div className="form-group">
                  <label htmlFor="title">Başlık *</label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Örn: Trendyol Mağazam"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="link">URL *</label>
                  <input
                    type="url"
                    id="link"
                    name="link"
                    value={formData.link}
                    onChange={handleInputChange}
                    placeholder="https://..."
                    required
                  />
                </div>
              </div>
              <div className="form-sidebar">
                <div className="form-group">
                  <label>İkon (Opsiyonel)</label>
                  <div className="icon-uploader">
                    <div className="icon-preview">
                      {iconPreview ? (
                        <img src={iconPreview} alt="İkon" />
                      ) : (
                        <FiImage size={48} />
                      )}
                    </div>
                    <input
                      type="file"
                      id="icon"
                      accept="image/*"
                      onChange={handleIconFileChange}
                      style={{ display: 'none' }}
                    />
                    <div className="icon-actions">
                      <label htmlFor="icon" className="upload-btn">
                        <FiUpload size={16} /> {uploading ? 'Yükleniyor...' : 'Seç/Değiştir'}
                      </label>
                      {iconPreview && (
                        <button type="button" className="remove-btn" onClick={removeIcon}>
                          <FiTrash2 size={16} /> Kaldır
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="form-actions-full">
                <button 
                  type="submit" 
                  className="action-button primary"
                  disabled={saving || uploading}
                >
                  <FiSave size={18} />
                  <span>{saving ? 'Kaydediliyor...' : 'Bağlantıyı Kaydet'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      ) : (
        <div className="content-main-area list-page-container">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)', color: '#3b82f6' }}>
                <FiLink size={24} />
              </div>
              <div className="content-title">
                <h2>Bağlantı Listesi</h2>
              </div>
              <div className="header-info-box">
                <FiInfo size={16} />
                <span>Sıralamayı değiştirmek için sürükleyip bırakın.</span>
              </div>
            </div>
            
            <div className="content-actions">
              <div className="search-container">
                <div className="search-box">
                  <FiSearch size={20} />
                  <input
                    type="text"
                    placeholder="Bağlantı ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button className="clear-search" onClick={clearSearch}>
                      <FiX size={16} />
                    </button>
                  )}
                </div>
              </div>
              <button 
                className="action-button primary"
                onClick={() => navigate('/dashboard/links/add')}
              >
                <FiPlus size={18} />
                <span>Yeni Bağlantı Ekle</span>
              </button>
            </div>
          </div>
          
          
            {loading ? (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Bağlantı listesi yükleniyor...</p>
              </div>
            ) : (
              <div className="list-table-container">
                <div className="list-table-header">
                  <div className="header-cell drag-handle"></div>
                  <div className="header-cell icon">İkon</div>
                  <div className="header-cell title">Başlık</div>
                  <div className="header-cell link">URL</div>
                  <div className="header-cell actions">İşlemler</div>
                </div>
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="links-droppable">
                    {(provided) => (
                      <div 
                        className="list-table-body"
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {filteredLinks.length > 0 ? (
                          filteredLinks.map((link, index) => (
                            <Draggable key={link.id} draggableId={link.id.toString()} index={index}>
                              {(provided, snapshot) => (
                                <div 
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  className={`list-table-row ${snapshot.isDragging ? 'dragging' : ''}`}
                                >
                                  <div className="body-cell drag-handle" {...provided.dragHandleProps}>
                                    <FiMenu size={20} />
                                  </div>
                                  <div className="body-cell icon">
                                    <div className="icon-wrapper">
                                      {link.icon_url ? (
                                        <img src={link.icon_url} alt={link.title} />
                                      ) : (
                                        <FiLink size={24} />
                                      )}
                                    </div>
                                  </div>
                                  <div className="body-cell title">
                                    <span>{link.title}</span>
                                  </div>
                                  <div className="body-cell link">
                                    <a href={link.link} target="_blank" rel="noopener noreferrer">
                                      {link.link}
                                    </a>
                                  </div>
                                  <div className="body-cell actions">
                                    <button 
                                      className="action-btn edit"
                                      onClick={() => navigate(`/dashboard/links/edit/${link.id}`)}
                                      title="Düzenle"
                                    >
                                      <FiEdit size={16} />
                                    </button>
                                    <button 
                                      className="action-btn delete"
                                      onClick={() => handleDeleteClick(link.id)}
                                      title="Sil"
                                    >
                                      <FiTrash2 size={16} />
                                    </button>
                                  </div>
                                </div>
                              )}
                            </Draggable>
                          ))
                        ) : (
                          <div className="empty-state">
                            <div className="empty-icon">
                              <FiLink size={48} />
                            </div>
                            <h4>{searchTerm ? 'Arama sonucu bulunamadı' : 'Henüz bağlantı eklenmemiş'}</h4>
                            <p>{searchTerm 
                              ? 'Farklı bir anahtar kelimeyle tekrar deneyin' 
                              : 'İlk bağlantınızı ekleyerek başlayın'}
                            </p>
                            {!searchTerm && (
                              <button 
                                className="add-first-btn"
                                onClick={() => navigate('/dashboard/links/add')}
                              >
                                <FiPlus />
                                İlk Bağlantıyı Ekle
                              </button>
                            )}
                          </div>
                        )}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </div>
            )}
        </div>
      )}
    </>
  );
};

export default LinkManager; 