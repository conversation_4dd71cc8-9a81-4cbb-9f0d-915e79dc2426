/* CSS Reset & Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(150deg, var(--bg-primary) 0%, #0f172a 100%);
}

#root {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Global Button Reset */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Global Input Reset */
input, textarea, select {
  font-family: inherit;
  border: none;
  outline: none;
}

/* Global Link Reset */
a {
  text-decoration: none;
  color: inherit;
} 