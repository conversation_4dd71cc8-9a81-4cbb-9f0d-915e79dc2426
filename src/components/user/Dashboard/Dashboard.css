/* Dashboard Container */
.dashboard-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8fafc;
  min-height: calc(100vh - 80px);
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
  text-align: center;
}

.dashboard-header h1 {
  color: #1a202c;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
}

/* Loading & Error States */
.dashboard-loading,
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3182ce;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Overview Section */
.dashboard-overview {
  margin-bottom: 2rem;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.overview-card.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-icon {
  font-size: 3rem;
  opacity: 0.9;
}

.overview-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.overview-count {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

/* Stats Grid */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid #e2e8f0;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Color variations */
.stat-card.blue { border-left-color: #3182ce; }
.stat-card.green { border-left-color: #38a169; }
.stat-card.purple { border-left-color: #805ad5; }
.stat-card.orange { border-left-color: #dd6b20; }
.stat-card.red { border-left-color: #e53e3e; }
.stat-card.indigo { border-left-color: #5a67d8; }
.stat-card.pink { border-left-color: #d53f8c; }

.stat-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stat-icon {
  font-size: 1.5rem;
  color: #718096;
}

.stat-card.blue .stat-icon { color: #3182ce; }
.stat-card.green .stat-icon { color: #38a169; }
.stat-card.purple .stat-icon { color: #805ad5; }
.stat-card.orange .stat-icon { color: #dd6b20; }
.stat-card.red .stat-icon { color: #e53e3e; }
.stat-card.indigo .stat-icon { color: #5a67d8; }
.stat-card.pink .stat-icon { color: #d53f8c; }

.stat-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #4a5568;
}

.stat-count {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.stat-recent {
  border-top: 1px solid #e2e8f0;
  padding-top: 0.75rem;
  margin-top: 0.75rem;
}

.stat-recent p {
  margin: 0 0 0.25rem 0;
  font-size: 0.8rem;
  color: #718096;
  font-weight: 500;
}

.stat-recent span {
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 500;
}

/* Quick Actions */
.dashboard-quick-actions {
  margin-bottom: 3rem;
}

.dashboard-quick-actions h2 {
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quick-action-btn {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
}

.quick-action-btn:hover {
  border-color: #3182ce;
  background: #f7fafc;
  transform: translateY(-1px);
  color: #3182ce;
}

.quick-action-btn svg {
  font-size: 1.5rem;
}

/* Recent Content */
.dashboard-recent-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dashboard-recent-content h2 {
  color: #1a202c;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.recent-content-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recent-content-item {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.recent-content-item:hover {
  background-color: #f7fafc;
}

.recent-content-info {
  flex: 1;
}

.recent-content-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
}

.recent-content-info p {
  margin: 0;
  font-size: 0.8rem;
  color: #718096;
}

.recent-content-status .status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status.published {
  background-color: #c6f6d5;
  color: #276749;
}

.status.draft {
  background-color: #fed7d7;
  color: #c53030;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 2rem;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .recent-content-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
} 