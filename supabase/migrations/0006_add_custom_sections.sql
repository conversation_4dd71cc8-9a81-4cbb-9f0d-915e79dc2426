-- <PERSON><PERSON> alanlar tablosunu oluşturuyoruz
CREATE TABLE public.custom_sections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  section_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Custom sections tablosuna erişim için güvenlik kuralları
ALTER TABLE public.custom_sections ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own custom sections" ON public.custom_sections
  FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Users can insert their own custom sections" ON public.custom_sections
  FOR INSERT WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Users can update their own custom sections" ON public.custom_sections
  FOR UPDATE USING (profile_id = auth.uid());

CREATE POLICY "Users can delete their own custom sections" ON public.custom_sections
  FOR DELETE USING (profile_id = auth.uid());

-- Updated at trigger için fonksiyon
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Updated at trigger
CREATE TRIGGER update_custom_sections_updated_at 
    BEFORE UPDATE ON public.custom_sections 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 