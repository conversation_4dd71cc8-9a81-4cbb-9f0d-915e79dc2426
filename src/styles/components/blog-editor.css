/* Blog Editor Components */

/* Animasyonlar */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
}

@keyframes spinRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Modern Loading */
.modern-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modern-loading-content {
  text-align: center;
  animation: fadeIn 0.5s ease-in-out;
}

.loading-spinner-modern {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
}

.spinner-ring {
  position: absolute;
  border: 4px solid transparent;
  border-top: 4px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spinRing 1.2s linear infinite;
}

.spinner-ring:nth-child(1) {
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  animation-delay: -0.4s;
  border-top-color: rgba(255, 255, 255, 0.6);
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  animation-delay: -0.8s;
  border-top-color: rgba(255, 255, 255, 0.4);
}

.loading-text {
  color: white;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

/* Toast Notifications */
.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.Toastify__toast {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  min-height: 64px !important;
  padding: 16px !important;
}

.Toastify__toast--success {
  background: linear-gradient(135deg, #10b981, #34d399) !important;
  color: white !important;
}

.Toastify__toast--error {
  background: linear-gradient(135deg, #ef4444, #f87171) !important;
  color: white !important;
}

.Toastify__toast--info {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  color: white !important;
}

.Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.8) !important;
  height: 3px !important;
}

.Toastify__close-button {
  color: rgba(255, 255, 255, 0.8) !important;
  opacity: 0.8 !important;
}

.Toastify__close-button:hover {
  opacity: 1 !important;
}

.Toastify__toast-container {
  width: 380px !important;
}

@media (max-width: 640px) {
  .Toastify__toast-container {
    width: calc(100vw - 32px) !important;
    left: 16px !important;
    right: 16px !important;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.animate-slide-in {
  animation: slideIn 0.4s ease forwards;
}

.animate-shake {
  animation: shake 0.4s ease forwards;
}

/* Blog Editor Layout */
.blog-editor-container {
  width: 100%;
  max-width: none;
  margin: 0 auto;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  height: calc(100vh - 100px);
}

.blog-editor-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 100%;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

/* Modern Editor Header */
.modern-editor-header {
  background: linear-gradient(135deg, var(--bg-secondary), rgba(var(--bg-secondary-rgb), 0.9));
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.modern-editor-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.8;
}

.modern-editor-header .editor-header-left {
  gap: var(--spacing-md);
}

.modern-editor-header .editor-back-btn {
  width: 42px;
  height: 42px;
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.modern-editor-header .editor-back-btn:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-editor-header .editor-title {
  display: flex;
  flex-direction: column;
}

.modern-editor-header .title-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.modern-editor-header .editor-title h2 {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

.modern-editor-header .editor-subtitle {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 4px 0 0 0;
}

.modern-editor-header .editor-status {
  display: flex;
  align-items: center;
}

.modern-editor-header .status-badge {
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  border: 1px solid var(--border-color);
}

.modern-editor-header .status-badge.published {
  background: rgba(var(--success-rgb), 0.1);
  color: var(--success);
  border-color: rgba(var(--success-rgb), 0.2);
}

.modern-editor-header .status-badge.draft {
  background: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
  border-color: rgba(var(--warning-rgb), 0.2);
}

.modern-editor-header .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
}

.modern-editor-header .editor-actions {
  gap: var(--spacing-sm);
}

.modern-editor-header .editor-action-btn {
  padding: 6px 10px;
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modern-editor-header .editor-action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .modern-editor-header {
    padding: var(--spacing) var(--spacing-md);
  }
  
  .modern-editor-header .editor-title h2 {
    font-size: 1.1rem;
  }
  
  .modern-editor-header .title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .modern-editor-header .editor-subtitle {
    display: none;
  }
}

/* Editor Header */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing) var(--spacing-lg);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.editor-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.editor-back-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.editor-back-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(-2px);
}

.editor-title {
  display: flex;
  flex-direction: column;
}

.editor-title h2 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.editor-status {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 0;
  color: var(--text-secondary);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-badge.published .status-dot {
  background-color: var(--success);
}

.status-badge.draft .status-dot {
  background-color: var(--warning);
}

.editor-actions {
  display: flex;
  gap: var(--spacing-md);
}

.editor-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: transparent;
  border: none;
  border-radius: var(--radius);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.editor-action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.editor-action-btn svg {
  font-size: 1rem;
}

.action-label {
  display: none;
}

/* Media Queries for Header */
@media (min-width: 1024px) {
  .action-label {
    display: inline;
  }
  
  .editor-action-btn {
    padding: 6px 12px;
  }
}

.editor-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
}

.editor-main-panel .form-group {
  margin-bottom: var(--spacing-lg);
}

.title-group {
  flex-shrink: 0;
}

.editor-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed .editor-main-panel {
  max-width: 900px;
  margin: 0 auto;
}

/* Form Elements */
.blog-title-input {
  width: 100%;
  padding: var(--spacing-sm) 0;
  font-size: 2rem;
  font-weight: 600;
  border: none;
  background: transparent;
  color: var(--text-primary);
}

.blog-title-input:focus {
  outline: none;
}

.blog-title-input::placeholder {
  color: var(--text-muted);
  opacity: 0.5;
}

.blog-excerpt-textarea {
  width: 100%;
  padding: var(--spacing-sm);
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-tertiary);
  color: var(--text-muted);
  resize: vertical;
  min-height: 100px;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.blog-excerpt-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  background: var(--bg-primary);
}

.char-count {
  display: block;
  text-align: right;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 4px;
}

/* Quill Editor */
.quill-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden; /* Important for child flex to work */
}

.quill-wrapper.error {
  border-color: var(--danger);
}

.quill-wrapper .ql-toolbar {
  flex-shrink: 0;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color) !important;
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
  border: none;
}

/* Quill icon renkleri */
.quill-wrapper .ql-toolbar .ql-stroke {
  stroke: var(--text-primary) !important;
}

.quill-wrapper .ql-toolbar .ql-fill {
  fill: var(--text-primary) !important;
}

.quill-wrapper .ql-toolbar .ql-picker {
  color: var(--text-primary) !important;
}

.quill-wrapper .ql-toolbar button:hover .ql-stroke,
.quill-wrapper .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--accent-primary) !important;
}

.quill-wrapper .ql-toolbar button:hover .ql-fill,
.quill-wrapper .ql-toolbar button.ql-active .ql-fill {
  fill: var(--accent-primary) !important;
}

.quill-wrapper .ql-toolbar .ql-picker:hover,
.quill-wrapper .ql-toolbar .ql-picker.ql-expanded {
  color: var(--accent-primary) !important;
}

.quill-wrapper .ql-toolbar .ql-picker-options {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

.quill-wrapper .ql-container {
  flex: 1;
  font-size: 1.1rem;
  line-height: 1.6;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow-y: auto;
  border: none;
  border-bottom-left-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

/* Editor Sidebar */
.editor-sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  overflow-y: auto;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 600px;
  max-height: calc(100vh - 80px);
}

.sidebar-collapsed .editor-sidebar {
  width: 0;
  padding: 0;
  overflow: hidden;
  border-left: none;
}

.sidebar-content {
  flex: 1;
  padding-bottom: var(--spacing-lg);
}

.editor-sidebar h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-muted);
}

.editor-sidebar .form-group {
  margin-bottom: var(--spacing-lg);
}

.editor-sidebar label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.status-select {
  width: 100%;
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-select:focus {
  outline: none;
  border-color: var(--accent-primary);
  background: var(--bg-primary);
}

/* Featured Image */
.featured-image-preview {
  position: relative;
  border-radius: var(--radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
  width: 100%;
  aspect-ratio: 16/9;
}

.featured-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.remove-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.featured-image-preview:hover .remove-image-btn {
  opacity: 1;
}

.remove-image-btn:hover {
  background: var(--danger);
  transform: scale(1.1);
}

.featured-image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  height: 180px;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius);
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.featured-image-upload:hover {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  background: rgba(var(--accent-primary-rgb), 0.05);
}

.featured-image-upload input {
  display: none;
}

/* Sidebar Footer */
.sidebar-footer {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing);
  margin-top: auto;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* Buttons */
.btn-secondary {
  flex: 1;
  text-align: center;
  padding: 0.75rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

.btn-publish {
  flex: 1;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-publish:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-publish:disabled,
.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Field Error */
.field-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--danger);
  font-size: 0.875rem;
  margin-top: var(--spacing-sm);
}

/* Upload Indicator */
.upload-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--accent-primary);
  font-size: 0.875rem;
  margin-top: var(--spacing-sm);
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Notification Toast */
.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 400px;
  padding: var(--spacing);
  border-radius: var(--radius);
  background: var(--bg-secondary);
  box-shadow: var(--shadow-lg);
  z-index: 10000;
  overflow: hidden;
}

.notification-toast::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.notification-toast.success::before {
  background-color: var(--success);
}

.notification-toast.error::before {
  background-color: var(--danger);
}

.notification-toast.warning::before {
  background-color: var(--warning);
}

.notification-toast.info::before {
  background-color: var(--info);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.notification-toast.success .notification-icon {
  color: var(--success);
}

.notification-toast.error .notification-icon {
  color: var(--danger);
}

.notification-toast.warning .notification-icon {
  color: var(--warning);
}

.notification-toast.info .notification-icon {
  color: var(--info);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  transition: all 0.2s ease;
}

.notification-close:hover {
  color: var(--text-primary);
}

/* Content Preview */
.content-preview-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  max-width: 800px;
  margin: 0 auto;
}

.preview-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.preview-header h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing);
  color: var(--text-primary);
}

.preview-excerpt {
  font-size: 1.125rem;
  color: var(--text-secondary);
  font-style: italic;
  max-width: 600px;
  margin: 0 auto;
}

.preview-featured-image {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius);
  overflow: hidden;
}

.preview-featured-image img {
  width: 100%;
  height: auto;
  display: block;
}

.preview-content {
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 1.125rem;
}

.preview-content h1,
.preview-content h2,
.preview-content h3,
.preview-content h4,
.preview-content h5,
.preview-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  color: var(--text-primary);
}

.preview-content p {
  margin-bottom: 1em;
}

.preview-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-sm);
  margin: 1em 0;
}

.preview-content blockquote {
  border-left: 4px solid var(--accent-primary);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: var(--text-secondary);
}

.preview-content ul,
.preview-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

/* Responsive */
@media (max-width: 1024px) {
  .editor-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(0);
  }
  
  .sidebar-collapsed .editor-sidebar {
    transform: translateX(100%);
  }
  
  .editor-main-panel {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .editor-header {
    padding: var(--spacing-sm) var(--spacing);
  }
  
  .editor-header-left {
    gap: var(--spacing-sm);
  }
  
  .editor-title h2 {
    font-size: 0.9rem;
  }
  
  .editor-actions {
    gap: 4px;
  }
  
  .editor-action-btn {
    padding: 4px 8px;
  }
  
  .editor-back-btn {
    width: 32px;
    height: 32px;
  }
  
  .blog-title-input {
    font-size: 1.5rem;
  }
  
  .quill-wrapper .ql-container {
    font-size: 1rem;
  }
} 

/* Modern Content Header Styles */
.content-header-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-wrap: wrap;
  width: 100%;
}

/* Icon styling */
.content-header-info .content-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Icon hover effect */
.content-header-info:hover .content-icon {
  transform: scale(1.05);
}

/* Title styling */
.content-header-info .content-title {
  flex: 1;
}

.content-header-info .content-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.content-header-info:hover .content-title h2 {
  color: var(--accent-primary);
}

.content-header-info .content-title p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status indicators */
.content-header-info .content-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: 8px;
  flex-wrap: wrap;
}

.content-header-info .status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.content-header-info .status-indicator.active {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
}

.content-header-info .status-indicator.draft {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
}

.content-header-info .status-indicator.inactive {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
}

/* Action buttons */
.content-header-info .content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-left: auto;
}

.content-header-info .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.content-header-info .action-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--accent-primary);
}

/* Content toggle */
.content-header-info .content-toggle {
  margin-left: var(--spacing-md);
}

/* Add content button */
.content-header-info .add-content-btn {
  margin-left: var(--spacing-md);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.content-header-info .add-content-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Responsive styles */
@media (max-width: 768px) {
  .content-header-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing);
    padding: var(--spacing);
  }
  
  .content-header-info .content-icon {
    width: 40px;
    height: 40px;
  }
  
  .content-header-info .content-title h2 {
    font-size: 1.1rem;
  }
  
  .content-header-info .content-actions {
    align-self: flex-end;
    margin-top: var(--spacing-sm);
  }
  
  .content-header-info .content-toggle,
  .content-header-info .add-content-btn {
    margin-left: 0;
    margin-top: var(--spacing);
    align-self: flex-end;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .content-header-info {
    background: linear-gradient(to right, var(--bg-secondary), rgba(var(--bg-secondary-rgb), 0.8));
  }
  
  .content-header-info::before {
    opacity: 0.6;
  }
  
  .content-header-info:hover {
    background: linear-gradient(to right, var(--bg-secondary), rgba(var(--bg-secondary-rgb), 0.9));
  }
}

/* Blog Add Page Styles */
.blog-add-page {
  background-color: var(--bg-primary);
}

/* Modern Header */
.modern-header {
  background: linear-gradient(135deg, var(--bg-secondary), rgba(var(--bg-secondary-rgb), 0.9));
  border-radius: var(--radius-lg);
  padding: var(--spacing) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.9;
}

.modern-header .content-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(var(--accent-primary-rgb), 0.1), rgba(var(--accent-secondary-rgb), 0.1));
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(var(--accent-primary-rgb), 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
}

.modern-header .content-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 6px;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

.modern-header .content-title p {
  font-size: 0.95rem;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.modern-header .content-status {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: 10px;
}

.modern-header .status-indicator {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.modern-header .content-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.modern-header .action-btn {
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: rgba(var(--bg-tertiary-rgb), 0.5);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.modern-header .action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .modern-header {
    padding: var(--spacing-sm) var(--spacing);
  }
  
  .modern-header .content-icon {
    width: 42px;
    height: 42px;
  }
  
  .modern-header .content-title h2 {
    font-size: 1.2rem;
  }
} 