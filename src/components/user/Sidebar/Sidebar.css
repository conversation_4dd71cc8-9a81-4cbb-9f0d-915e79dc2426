/* Sidebar Styles */
.sidebar {
  width: 280px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.sidebar-home-button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: #f3f4f6;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
  color: #374151;
}

.sidebar-home-button:hover {
  background-color: #e5e7eb;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.nav-item-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  cursor: grab;
  transition: all 0.2s;
  user-select: none;
  touch-action: manipulation;
  position: relative;
  z-index: 1;
}

.sidebar-nav-item:hover,
.sidebar-nav-item:focus {
  background-color: #f9fafb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sidebar-nav-item.active {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.sidebar-nav-item.dragging {
  z-index: 100;
}

.sidebar-nav-item.drop-target {
  background-color: #f3f4f6;
}

.nav-item-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-item-drag-handle {
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.drag-dots {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2px;
  width: 10px;
  height: 14px;
}

.drag-dots span {
  width: 3px;
  height: 3px;
  background-color: #9ca3af;
  border-radius: 50%;
}

.nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: #f3f4f6;
  color: #6b7280;
}

.nav-item-content {
  display: flex;
  flex-direction: column;
}

.nav-item-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.nav-item-status {
  font-size: 12px;
  color: #6b7280;
}

.nav-item-right {
  display: flex;
  align-items: center;
}

.nav-item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-item-switch {
  width: 36px;
  height: 20px;
  background-color: #d1d5db;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
}

.nav-item-switch.active {
  background-color: #10b981;
}

.switch-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.2s;
}

.nav-item-switch.active .switch-slider {
  transform: translateX(16px);
}

.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #3b82f6;
  z-index: 10;
  border-radius: 2px;
}

.drop-indicator.drop-before {
  top: -2px;
}

.drop-indicator.drop-after {
  bottom: -2px;
}

.sidebar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled state */
.sidebar-nav-item.disabled {
  opacity: 0.7;
}

/* Focus state for accessibility */
.sidebar-nav-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
