-- <PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS gallery_albums (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    link TEXT,
    hit_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    album_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> resimleri tablosu
CREATE TABLE IF NOT EXISTS gallery_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    album_id UUID NOT NULL REFERENCES gallery_albums(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_title TEXT,
    description TEXT,
    image_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>ri storage bucket'ı oluştur
INSERT INTO storage.buckets (id, name, public) 
VALUES ('gallery-images', 'gallery-images', true)
ON CONFLICT (id) DO NOTHING;

-- RLS politikaları
ALTER TABLE gallery_albums ENABLE ROW LEVEL SECURITY;
ALTER TABLE gallery_images ENABLE ROW LEVEL SECURITY;

-- Galeri albümleri için RLS politikaları
CREATE POLICY "Users can view their own gallery albums" ON gallery_albums
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own gallery albums" ON gallery_albums
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own gallery albums" ON gallery_albums
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own gallery albums" ON gallery_albums
    FOR DELETE USING (auth.uid() = user_id);

-- Galeri resimleri için RLS politikaları
CREATE POLICY "Users can view their own gallery images" ON gallery_images
    FOR SELECT USING (
        auth.uid() = (
            SELECT user_id FROM gallery_albums WHERE id = gallery_images.album_id
        )
    );

CREATE POLICY "Users can insert their own gallery images" ON gallery_images
    FOR INSERT WITH CHECK (
        auth.uid() = (
            SELECT user_id FROM gallery_albums WHERE id = gallery_images.album_id
        )
    );

CREATE POLICY "Users can update their own gallery images" ON gallery_images
    FOR UPDATE USING (
        auth.uid() = (
            SELECT user_id FROM gallery_albums WHERE id = gallery_images.album_id
        )
    );

CREATE POLICY "Users can delete their own gallery images" ON gallery_images
    FOR DELETE USING (
        auth.uid() = (
            SELECT user_id FROM gallery_albums WHERE id = gallery_images.album_id
        )
    );

-- Storage politikaları
CREATE POLICY "Gallery images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'gallery-images');

CREATE POLICY "Users can upload gallery images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'gallery-images' AND 
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can update their own gallery images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'gallery-images' AND 
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can delete their own gallery images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'gallery-images' AND 
        auth.role() = 'authenticated'
    );

-- Performans için indeksler
CREATE INDEX IF NOT EXISTS idx_gallery_albums_user_id ON gallery_albums(user_id);
CREATE INDEX IF NOT EXISTS idx_gallery_albums_order ON gallery_albums(album_order);
CREATE INDEX IF NOT EXISTS idx_gallery_images_album_id ON gallery_images(album_id);
CREATE INDEX IF NOT EXISTS idx_gallery_images_order ON gallery_images(image_order);

-- Updated at trigger fonksiyonu
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Updated at trigger'ları
CREATE TRIGGER update_gallery_albums_updated_at BEFORE UPDATE ON gallery_albums 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gallery_images_updated_at BEFORE UPDATE ON gallery_images 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 