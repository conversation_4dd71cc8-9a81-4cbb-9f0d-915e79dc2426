import { useBaseResource } from './base/useBaseResource';
import { linksConfig } from './base/useResourceConfigs';
import { useCallback } from 'react';

/**
 * Links (Portfolio) management hook using the generic base resource
 */
export function useLinks() {
  const baseResource = useBaseResource('link_items', linksConfig);
  
  // Custom wrapper for icon file handling
  const createLink = useCallback(async (linkData, iconFile) => {
    const files = iconFile ? { icon_url: iconFile } : {};
    return baseResource.createItem(linkData, files);
  }, [baseResource.createItem]);

  const updateLink = useCallback(async (id, linkData, iconFile) => {
    const files = iconFile ? { icon_url: iconFile } : {};
    return baseResource.updateItem(id, linkData, files);
  }, [baseResource.updateItem]);
  
  return {
    // Data
    links: baseResource.items,
    loading: baseResource.loading,
    error: baseResource.error,
    initialized: baseResource.initialized,
    
    // Methods - Links specific names
    fetchLinks: baseResource.fetchItems,
    getLinkById: baseResource.getItemById,
    createLink,
    updateLink,
    deleteLink: baseResource.deleteItem,
    updateLinkOrder: baseResource.updateItemOrder,
    
    // File upload (direct access)
    uploadLinkIcon: baseResource.uploadFile,
    
    // Utilities
    refreshLinks: baseResource.refreshCache
  };
} 