import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentCatalog } from '../../../contexts/ContentContext';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  FiPlus, FiArrowLeft, FiSave, FiEdit, FiTrash2, 
  FiSearch, FiMenu, FiBookOpen, FiUpload, FiDownload, FiEye, FiEyeOff
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const CatalogManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const { 
    catalogs, loading, getCatalogById, createCatalog, 
    updateCatalog, deleteCatalog, updateCatalogOrder 
  } = useContentCatalog();
  
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [formData, setFormData] = useState({
    title: '',
    pdf_url: '',
    cover_image_url: '',
    is_active: true,
  });

  const [pdfFile, setPdfFile] = useState(null);
  const [coverFile, setCoverFile] = useState(null);
  const [coverPreview, setCoverPreview] = useState('');

  useEffect(() => {
    if (mode === 'edit' && id) {
      const loadCatalog = async () => {
        try {
          const data = await getCatalogById(id);
          if (data) {
            setFormData(data);
            if(data.cover_image_url) setCoverPreview(data.cover_image_url);
            if (data.file_url) setFormData(prev => ({ ...prev, pdf_url: data.file_url }));
          }
        } catch (error) {
          showToast.error('Katalog bilgileri yüklenirken bir hata oluştu.');
          navigate('/dashboard/catalogs/list');
        }
      };
      loadCatalog();
    } else if (mode === 'add') {
      setFormData({ title: '', pdf_url: '', cover_image_url: '', is_active: true });
      setCoverFile(null);
      setPdfFile(null);
      setCoverPreview('');
    }
  }, [mode, id, getCatalogById, navigate]);
  
  const handleCoverFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (file.size > 5 * 1024 * 1024) {
      showToast.error('Dosya boyutu 5MB\'dan küçük olmalıdır.');
      return;
    }
    setCoverFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setCoverPreview(reader.result);
    reader.readAsDataURL(file);
  };

  const handlePdfFileChange = (e) => {
    const file = e.target.files[0];
    if (!file || file.type !== 'application/pdf') {
      showToast.error('Lütfen geçerli bir PDF dosyası seçin.');
      return;
    }
    setPdfFile(file);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.title.trim()) {
      showToast.warning('Başlık alanı zorunludur.');
      return;
    }
    if (mode === 'add' && !pdfFile) {
      showToast.warning('Lütfen bir PDF dosyası seçin.');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'edit' && id) {
        await updateCatalog(id, formData, pdfFile, coverFile);
      } else {
        await createCatalog(formData, pdfFile, coverFile);
      }
      navigate('/dashboard/catalogs/list');
    } catch (error) {
      showToast.error('Katalog kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (catalogId) => {
    if (!window.confirm('Bu kataloğu silmek istediğinizden emin misiniz?')) return;
    try {
      await deleteCatalog(catalogId);
    } catch (error) {
      showToast.error('Katalog silinirken bir hata oluştu.');
    }
  };
  
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(catalogs);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    try {
      await updateCatalogOrder(items);
    } catch (error) {
      showToast.error('Sıralama güncellenirken bir hata oluştu.');
    }
  };
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const handleToggleStatus = async (catalog) => {
    try {
      await updateCatalog(catalog.id, { is_active: !catalog.is_active });
    } catch (error) {
      showToast.error('Durum güncellenirken bir hata oluştu.');
    }
  };

  const filteredCatalogs = catalogs.filter(c => c.title.toLowerCase().includes(searchTerm.toLowerCase()));

  // ADD / EDIT FORM
  if (mode === 'add' || mode === 'edit') {
    return (
      <div className="content-main-area">
        <div className="content-header">
           <div className="content-header-info">
            <div className="content-icon" style={{ backgroundColor: 'rgba(79, 70, 229, 0.1)', color: '#4f46e5' }}>
              <FiBookOpen size={24} />
            </div>
            <div className="content-title">
              <h2>{mode === 'edit' ? 'Katalog Düzenle' : 'Yeni Katalog Ekle'}</h2>
              <p>Katalog bilgilerinizi ve PDF dosyanızı buradan yönetin.</p>
            </div>
          </div>
          <div className="content-actions">
            <button className="action-button secondary" onClick={() => navigate('/dashboard/catalogs/list')}>
              <FiArrowLeft size={18} />
              <span>Listeye Dön</span>
            </button>
          </div>
        </div>
        
        <div className="content-body">
          <form className="content-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="catalog-title">Katalog Başlığı</label>
              <input type="text" id="catalog-title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Katalog başlığını girin" required />
            </div>
            
            <div className="form-group">
                <label>Kapak Resmi (Opsiyonel)</label>
                <div className="file-upload-container">
                    <input type="file" id="cover-file" accept="image/png, image/jpeg" onChange={handleCoverFileChange} />
                    <label htmlFor="cover-file" className="file-upload-label">
                        <FiUpload size={18} />
                        <span>{coverFile ? 'Resmi Değiştir' : 'Resim Seç'}</span>
                    </label>
                    {coverPreview && <div className="image-preview"><img src={coverPreview} alt="Kapak Resmi Önizleme" /></div>}
                </div>
                <small>En iyi görünüm için 300x400 piksel boyutunda bir resim yükleyin. (Max 5MB)</small>
            </div>

             <div className="form-group">
                <label>PDF Dosyası</label>
                <div className="file-upload-container">
                    <input type="file" id="pdf-file" accept="application/pdf" onChange={handlePdfFileChange} />
                    <label htmlFor="pdf-file" className="file-upload-label">
                        <FiDownload size={18} />
                        <span>{pdfFile ? pdfFile.name : 'PDF Seç'}</span>
                    </label>
                    {formData.pdf_url && !pdfFile && <a href={formData.pdf_url} target="_blank" rel="noreferrer" className="text-sm text-blue-500">Mevcut PDF'i Görüntüle</a>}
                </div>
            </div>
            
            <div className="form-group">
                <label>Durum</label>
                <div className="toggle-switch">
                    <input type="checkbox" id="is_active" name="is_active" checked={formData.is_active} onChange={handleInputChange} />
                    <label htmlFor="is_active"></label>
                    <span>{formData.is_active ? 'Yayında' : 'Taslak'}</span>
                </div>
            </div>

            <div className="form-actions">
              <button type="button" className="action-button secondary" onClick={() => navigate('/dashboard/catalogs/list')}>İptal</button>
              <button type="submit" className="action-button primary" disabled={saving}>
                <FiSave size={18} />
                <span>{saving ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  // LIST VIEW
  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: 'rgba(79, 70, 229, 0.1)', color: '#4f46e5' }}>
            <FiBookOpen size={24} />
          </div>
          <div className="content-title">
            <h2>Katalog Yönetimi</h2>
            <p>PDF formatındaki kataloglarınızı buradan yönetin.</p>
          </div>
        </div>
        <div className="content-actions">
          <button className="action-button primary" onClick={() => navigate('/dashboard/catalogs/add')}>
            <FiPlus size={18} />
            <span>Yeni Katalog Ekle</span>
          </button>
        </div>
      </div>
      
      <div className="content-body">
        <div className="content-toolbar">
          <div className="search-box">
            <FiSearch className="search-icon" />
            <input type="text" placeholder="Katalog ara..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            {searchTerm && <FiEyeOff className="clear-icon" onClick={() => setSearchTerm('')} />}
          </div>
        </div>

        {loading ? (
          <div className="loading-indicator"><p>Kataloglar yükleniyor...</p></div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="catalogs">
              {(provided) => (
                <div className="draggable-list" {...provided.droppableProps} ref={provided.innerRef}>
                  {filteredCatalogs.length > 0 ? (
                    filteredCatalogs.map((catalog, index) => (
                      <Draggable key={catalog.id} draggableId={catalog.id.toString()} index={index}>
                        {(provided, snapshot) => (
                          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps} className={`draggable-item ${snapshot.isDragging ? 'dragging' : ''}`}>
                            <div className="drag-handle"><FiMenu /></div>
                            <div className="item-thumbnail">
                              {catalog.cover_image_url ? <img src={catalog.cover_image_url} alt={catalog.title} /> : <div className="thumbnail-placeholder"><FiEye /></div>}
                            </div>
                            <div className="item-content">
                              <h4 className="item-title">{catalog.title}</h4>
                              <a href={catalog.pdf_url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-500 hover:underline">PDF'i Görüntüle</a>
                            </div>
                            <div className="item-status">
                              <span className={`status-badge ${catalog.is_active ? 'active' : 'inactive'}`}>{catalog.is_active ? 'Yayında' : 'Taslak'}</span>
                            </div>
                            <div className="item-actions">
                              <button className="action-button-icon" onClick={() => navigate(`/dashboard/catalogs/edit/${catalog.id}`)} title="Düzenle"><FiEdit /></button>
                              <button className="action-button-icon danger" onClick={() => handleDelete(catalog.id)} title="Sil"><FiTrash2 /></button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))
                  ) : (
                    <div className="empty-state">
                      <FiBookOpen size={48} />
                      <h3>Henüz katalog eklenmemiş.</h3>
                      <p>Yeni bir katalog ekleyerek başlayabilirsiniz.</p>
                      <button className="action-button primary" onClick={() => navigate('/dashboard/catalogs/add')}>
                        <FiPlus size={18} />
                        <span>İlk Kataloğu Ekle</span>
                      </button>
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </div>
    </div>
  );
};

export default CatalogManager; 