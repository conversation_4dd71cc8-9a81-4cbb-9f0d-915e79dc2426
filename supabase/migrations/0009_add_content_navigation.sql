-- Create content_navigation table for storing user content tab settings and order
CREATE TABLE IF NOT EXISTS content_navigation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    tab_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT false,
    tab_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, tab_id)
);

-- Add RLS policies
ALTER TABLE content_navigation ENABLE ROW LEVEL SECURITY;

-- Users can only access their own content navigation settings
CREATE POLICY "Users can view own content navigation" ON content_navigation
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own content navigation" ON content_navigation
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own content navigation" ON content_navigation
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own content navigation" ON content_navigation
    FOR DELETE USING (auth.uid() = user_id);

-- Admins can access all content navigation settings
CREATE POLICY "Admins can view all content navigation" ON content_navigation
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Create indexes
CREATE INDEX idx_content_navigation_user_id ON content_navigation(user_id);
CREATE INDEX idx_content_navigation_tab_order ON content_navigation(user_id, tab_order);

-- Insert default navigation structure for existing users
INSERT INTO content_navigation (user_id, tab_id, title, description, enabled, tab_order)
SELECT 
    p.id as user_id,
    tab_data.tab_id,
    tab_data.title,
    tab_data.description,
    tab_data.enabled,
    tab_data.tab_order
FROM profiles p
CROSS JOIN (
    VALUES 
        ('links', 'Bağlantılar', 'Projelerim ve çalışmalarım', false, 0),
        ('blog', 'Blog', 'Duyurular, haberler ve makaleler', false, 1),
        ('gallery', 'Galeri', 'Fotoğraf albümleri', false, 2),
        ('multimedia', 'Video', 'Videolar ve ses kayıtları', false, 3),
        ('faq', 'SSS', 'Sıkça Sorulan Sorular', false, 4),
        ('contact', 'İletişim', 'Benimle iletişime geç', true, 5),
        ('corporate', 'Kurumsal', 'Şirket ve kariyer bilgileri', false, 6)
) AS tab_data(tab_id, title, description, enabled, tab_order)
WHERE p.role != 'admin'
ON CONFLICT (user_id, tab_id) DO NOTHING;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_content_navigation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_content_navigation_updated_at
    BEFORE UPDATE ON content_navigation
    FOR EACH ROW
    EXECUTE FUNCTION update_content_navigation_updated_at(); 