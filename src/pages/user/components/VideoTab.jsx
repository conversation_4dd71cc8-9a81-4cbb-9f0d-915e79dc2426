import React, { memo } from 'react';
import { FiSettings, FiPlay, FiPlus, FiTrash2, FiVideo } from 'react-icons/fi';

const VideoTab = memo(({ contentData, activeTab, handleContentChange, handleTabToggle, addContentItem, removeContentItem, updateContentItem }) => {
  const currentTab = contentData[activeTab];
  const tabColor = '#f59e0b';

  if (!currentTab) return null;

  const { enabled, title, description, items } = currentTab;

  if (!enabled) {
    return (
      <div className="tab-content-container">
        <div className="tab-header">
          <h3>{title}</h3>
          <div className="tab-toggle">
            <span>Pasif</span>
            <label className="switch">
              <input type="checkbox" checked={enabled} onChange={() => handleTabToggle(activeTab)} />
              <span className="slider round"></span>
            </label>
            <span>Aktif</span>
          </div>
        </div>
        <div className="empty-state">
          <div className="empty-icon">
            <FiVideo size={48} />
          </div>
          <h4>Video sekmesi pasif</h4>
          <p>Bu sekmeyi aktif hale getirmek için yukarıdaki toggle'ı kullanın</p>
          <button className="enable-btn" onClick={() => handleTabToggle(activeTab)}>
            Şimdi Aktif Et
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: `${tabColor}20`, color: tabColor }}>
            <FiPlay size={24} />
          </div>
          <div className="content-title">
            <h2>{title}</h2>
            <p>{description}</p>
          </div>
        </div>
        <div className="content-toggle">
          <button className={`toggle-switch ${enabled ? 'active' : ''}`} onClick={() => handleTabToggle(activeTab)}>
            <div className="toggle-slider"></div>
            <span className="toggle-label">{enabled ? 'Aktif' : 'Pasif'}</span>
          </button>
        </div>
      </div>

      <div className="content-body">
        <div className="content-settings-section">
          <h3><FiSettings size={20} /> Genel Ayarlar</h3>
          <div className="settings-grid">
            <div className="form-group">
              <label>Sekme Başlığı</label>
              <input type="text" value={title} onChange={(e) => handleContentChange(activeTab, 'title', e.target.value)} placeholder="Sekme başlığını girin" />
            </div>
            <div className="form-group">
              <label>Açıklama</label>
              <textarea value={description} onChange={(e) => handleContentChange(activeTab, 'description', e.target.value)} placeholder="Sekme açıklamasını girin" rows={3} />
            </div>
          </div>
        </div>

        <div className="content-items-section">
          <div className="section-header">
            <h3><FiPlay size={20} /> İçerikler</h3>
            <button className="add-content-btn" onClick={() => addContentItem(activeTab)}><FiPlus />Medya Ekle</button>
          </div>
          <div className="content-items-grid">
            {items.map(item => (
              <div key={item.id} className="content-item-card">
                <div className="item-header">
                  <div className="item-number">#{items.indexOf(item) + 1}</div>
                  <button className="delete-item-btn" onClick={() => removeContentItem(activeTab, item.id)} title="Sil"><FiTrash2 /></button>
                </div>
                <div className="item-form">
                  <div className="form-group">
                    <label>Medya Başlığı</label>
                    <input type="text" placeholder="Medya başlığı" value={item.title} onChange={(e) => updateContentItem(activeTab, item.id, 'title', e.target.value)} />
                  </div>
                  <div className="form-group">
                    <label>Video/Ses URL'si</label>
                    <input type="url" placeholder="Video/Ses URL'si (YouTube, SoundCloud vs.)" value={item.url} onChange={(e) => updateContentItem(activeTab, item.id, 'url', e.target.value)} />
                  </div>
                </div>
              </div>
            ))}
            {items.length === 0 && (
              <div className="empty-state">
                <div className="empty-icon"><FiPlay size={48} /></div>
                <h4>Henüz medya yok</h4>
                <p>Bu sekme için ilk medyanızı ekleyin</p>
                <button className="add-first-btn" onClick={() => addContentItem(activeTab)}><FiPlus />İlk Medyayı Ekle</button>
              </div>
            )}
          </div>
        </div>

        <div className="empty-state">
          <div className="empty-icon">
            <FiVideo size={48} />
          </div>
          <h4>Yakında...</h4>
          <p>Video yönetimi özelliği yakında burada olacak.</p>
        </div>
      </div>
    </div>
  );
});

VideoTab.displayName = 'VideoTab';
export default VideoTab; 