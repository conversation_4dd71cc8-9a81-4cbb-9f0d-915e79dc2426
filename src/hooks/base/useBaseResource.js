import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../../supabaseClient';
import { useAuth } from '../useAuth';
import showToast from '../../utils/toast';

/**
 * Generic base hook for all resource management
 * @param {string} tableName - Supabase table name  
 * @param {object} config - Configuration object
 * @returns {object} - Resource management methods and state
 */
export function useBaseResource(config = {}) {
  const { user } = useAuth();
  const abortControllerRef = useRef(null);
  
  const tableName = config.tableName;
  
  // Default configuration
  const defaultConfig = {
    // Table configuration
    primaryKey: 'id',
    userKey: 'user_id',
    orderBy: 'created_at',
    orderDirection: 'desc',
    
    // Feature flags
    enableUserFilter: true,
    enableOrder: false,
    enableCache: true,
    enableRealtime: false,
    
    // Messages
    messages: {
      fetchError: `${tableName || 'İçerik'} yüklenirken bir hata oluştu.`,
      createSuccess: `${tableName || 'İçerik'} başarıyla oluşturuldu.`,
      createError: `${tableName || 'İçerik'} oluşturulurken bir hata oluştu.`,
      updateSuccess: `${tableName || 'İçerik'} başarıyla güncellendi.`,
      updateError: `${tableName || 'İçerik'} güncellenirken bir hata oluştu.`,
      deleteSuccess: `${tableName || 'İçerik'} başarıyla silindi.`,
      deleteError: `${tableName || 'İçerik'} silinirken bir hata oluştu.`,
    },
    
    // Custom field mappings
    fields: {
      displayOrder: 'display_order',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      isActive: 'is_active'
    },
    
    // File upload configuration
    storage: {
      bucket: null,
      path: null
    },
    
    // Relations
    relations: [],
    
    // Custom hooks
    beforeCreate: null,
    afterCreate: null,
    beforeUpdate: null,
    afterUpdate: null,
    beforeDelete: null,
    afterDelete: null,
    
    // Transform functions
    transformData: null,
    transformCreate: null,
    transformUpdate: null
  };
  
  const finalConfig = { ...defaultConfig, ...config };
  
  // State management
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [initialized, setInitialized] = useState(false);
  
  // Cache management
  const cacheKey = `${tableName || 'items'}_${user?.id}`;
  const cacheTimeKey = `${tableName || 'items'}_time_${user?.id}`;
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Build query with relations
  const buildQuery = useCallback(() => {
    let selectClause = '*';
    
    if (finalConfig.relations.length > 0) {
      const relationQueries = finalConfig.relations.map(rel => {
        if (typeof rel === 'string') return rel;
        if (rel.table && rel.fields) {
          return `${rel.table}(${rel.fields})`;
        }
        return rel;
      });
      selectClause = `*, ${relationQueries.join(', ')}`;
    }
    
    let query = supabase.from(tableName).select(selectClause);
    
    // User filter
    if (finalConfig.enableUserFilter && user) {
      query = query.eq(finalConfig.userKey, user.id);
    }
    
    // Ordering
    if (finalConfig.enableOrder && finalConfig.fields.displayOrder) {
      query = query.order(finalConfig.fields.displayOrder, { ascending: true });
    } else {
      query = query.order(finalConfig.orderBy, { ascending: finalConfig.orderDirection === 'asc' });
    }
    
    return query;
  }, [tableName, finalConfig, user]);

  // Fetch all items
  const fetchItems = useCallback(async (forceRefresh = false) => {
    if (!user && finalConfig.enableUserFilter) return;

    try {
      setLoading(true);
      setError(null);
      
      const query = buildQuery();
      const { data, error } = await query;
      
      if (error) throw error;
      
      let processedData = data || [];
      
      // Transform data if needed
      if (finalConfig.transformData) {
        processedData = finalConfig.transformData(processedData);
      }
      
      setItems(processedData);
      setInitialized(true);
      
      return processedData;
    } catch (error) {
      console.error(`Error fetching ${tableName}:`, error);
      setError(error.message);
      showToast.error(finalConfig.messages.fetchError);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, buildQuery, tableName]);

  // Get single item by ID
  const getItemById = useCallback(async (id) => {
    if (!user && finalConfig.enableUserFilter) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      
      let query = supabase.from(tableName).select('*');
      
      if (finalConfig.relations.length > 0) {
        const relationQueries = finalConfig.relations.map(rel => {
          if (typeof rel === 'string') return rel;
          if (rel.table && rel.fields) {
            return `${rel.table}(${rel.fields})`;
          }
          return rel;
        });
        query = query.select(`*, ${relationQueries.join(', ')}`);
      }
      
      query = query.eq(finalConfig.primaryKey, id);
      
      if (finalConfig.enableUserFilter) {
        query = query.eq(finalConfig.userKey, user.id);
      }
      
      const { data, error } = await query.single();
      
      if (error) throw error;
      
      let processedData = data;
      if (finalConfig.transformData) {
        processedData = finalConfig.transformData([data])[0];
      }
      
      return processedData;
    } catch (error) {
      console.error(`Error fetching ${tableName} item:`, error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, tableName]);

  // File upload helper
  const uploadFile = useCallback(async (file, fieldName) => {
    if (!finalConfig.storage?.bucket || !finalConfig.storage?.path) {
      throw new Error('Storage configuration not provided');
    }
    
    const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
    const filePath = `${user.id}/${finalConfig.storage.path}/${fieldName}/${fileName}`;
    
    const { error } = await supabase.storage
      .from(finalConfig.storage.bucket)
      .upload(filePath, file);
    
    if (error) throw error;
    
    const { data } = supabase.storage
      .from(finalConfig.storage.bucket)
      .getPublicUrl(filePath);
    
    return data.publicUrl;
  }, [user, finalConfig.storage]);

  // Create new item
  const createItem = useCallback(async (itemData, files = {}) => {
    if (!user && finalConfig.enableUserFilter) {
      throw new Error('User authentication required');
    }

    try {
      setLoading(true);
      setError(null);

      // Run beforeCreate hook if provided
      if (finalConfig.beforeCreate) {
        itemData = await finalConfig.beforeCreate(itemData, files);
      }
      
      // Handle file uploads
      const uploadedFiles = {};
      for (const [fieldName, file] of Object.entries(files)) {
        if (file) {
          uploadedFiles[fieldName] = await uploadFile(file, fieldName);
        }
      }
      
      // Calculate display order if needed
      let finalData = { ...itemData, ...uploadedFiles };
      
      if (finalConfig.enableOrder && finalConfig.fields.displayOrder) {
        const { data: maxOrderData, error: maxOrderError } = await supabase
          .from(tableName)
          .select(finalConfig.fields.displayOrder)
          .eq(finalConfig.userKey, user.id)
          .order(finalConfig.fields.displayOrder, { ascending: false })
          .limit(1)
          .single();
        
        if (maxOrderError && maxOrderError.code !== 'PGRST116') {
          throw maxOrderError;
        }
        
        const newOrder = (maxOrderData?.[finalConfig.fields.displayOrder] ?? -1) + 1;
        finalData[finalConfig.fields.displayOrder] = newOrder;
      }

      // Add user_id if user filter is enabled
      if (finalConfig.enableUserFilter) {
        finalData.user_id = user.id;
      }

      // Add timestamps
      finalData.created_at = new Date().toISOString();
      finalData.updated_at = new Date().toISOString();

      const query = supabase
        .from(tableName)
        .insert([finalData])
        .select();

      const { data, error } = await query;

      if (error) throw error;

      const newItem = data[0];
      setItems(prev => [...prev, newItem]);
      
      showToast.success(`${tableName} başarıyla oluşturuldu.`);
      return newItem;
    } catch (error) {
      console.error(`Error creating ${tableName}:`, error);
      setError(error.message);
      showToast.error(`${tableName} oluşturulurken bir hata oluştu: ${error.message}`);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, uploadFile]);

  // Update existing item
  const updateItem = useCallback(async (itemId, itemData, files = {}) => {
    if (!user && finalConfig.enableUserFilter) {
      throw new Error('User authentication required');
    }

    try {
      setLoading(true);
      setError(null);

      // Run beforeUpdate hook if provided
      if (finalConfig.beforeUpdate) {
        itemData = await finalConfig.beforeUpdate(itemData, files);
      }
      
      // Handle file uploads
      const uploadedFiles = {};
      for (const [fieldName, file] of Object.entries(files)) {
        if (file) {
          uploadedFiles[fieldName] = await uploadFile(file, fieldName);
        }
      }
      
      let finalData = { ...itemData, ...uploadedFiles };
      
      // Add timestamp
      finalData.updated_at = new Date().toISOString();

             const query = supabase
         .from(tableName)
         .update(finalData)
         .eq('id', itemId);

       if (finalConfig.enableUserFilter) {
         query.eq('user_id', user.id);
       }

      const { data, error } = await query.select().single();

      if (error) throw error;

      // Update items array
      setItems(prev => prev.map(item => 
        item.id === itemId ? data : item
      ));
      
      showToast.success(`${tableName} başarıyla güncellendi.`);
      return data;
    } catch (error) {
      console.error(`Error updating ${tableName}:`, error);
      setError(error.message);
      showToast.error(`${tableName} güncellenirken bir hata oluştu: ${error.message}`);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, uploadFile]);

  // Delete item
  const deleteItem = useCallback(async (id) => {
    if (!user && finalConfig.enableUserFilter) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      
      // Before delete hook
      if (finalConfig.beforeDelete) {
        await finalConfig.beforeDelete(id);
      }
      
      let query = supabase
        .from(tableName)
        .delete()
        .eq(finalConfig.primaryKey, id);
      
      if (finalConfig.enableUserFilter) {
        query = query.eq(finalConfig.userKey, user.id);
      }
      
      const { error } = await query;
      
      if (error) throw error;
      
      // After delete hook
      if (finalConfig.afterDelete) {
        await finalConfig.afterDelete(id);
      }
      
      await fetchItems(true);
      showToast.success(finalConfig.messages.deleteSuccess);
      
    } catch (error) {
      console.error(`Error deleting ${tableName}:`, error);
      setError(error.message);
      showToast.error(finalConfig.messages.deleteError);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, tableName, fetchItems]);

  // Update item order
  const updateItemOrder = useCallback(async (updatedItems) => {
    if (!user && finalConfig.enableUserFilter) {
      throw new Error('User not authenticated');
    }
    
    if (!finalConfig.enableOrder || !finalConfig.fields.displayOrder) {
      throw new Error('Order management not enabled for this resource');
    }

    try {
      setLoading(true);
      
      const updates = updatedItems.map((item, index) =>
        supabase
          .from(tableName)
          .update({ [finalConfig.fields.displayOrder]: index })
          .eq(finalConfig.primaryKey, item[finalConfig.primaryKey])
          .eq(finalConfig.userKey, user.id)
      );
      
      const results = await Promise.all(updates);
      const error = results.find(res => res.error);
      
      if (error) throw error.error;
      
      // Optimistic update
      setItems(updatedItems);
      
      showToast.success('Sıralama başarıyla güncellendi.');
      
    } catch (error) {
      console.error(`Error updating ${tableName} order:`, error);
      setError(error.message);
      showToast.error('Sıralama güncellenirken bir hata oluştu.');
      await fetchItems(true); // Revert on error
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user, finalConfig, tableName, fetchItems]);

  // Initialize data on component mount
  useEffect(() => {
    if (user || !finalConfig.enableUserFilter) {
      fetchItems();
    }
  }, [user, finalConfig.enableUserFilter, fetchItems]);

  // Visibility change listener for cache refresh
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && initialized) {
        fetchItems();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [fetchItems, initialized]);

  return {
    // Data
    items,
    loading,
    error,
    initialized,
    
    // Methods
    fetchItems,
    getItemById,
    createItem,
    updateItem,
    deleteItem,
    updateItemOrder,
    uploadFile,
    
    // Utilities
    refreshCache: () => fetchItems(true)
  };
}
