import { useBaseResource } from './base/useBaseResource';
import { podcastConfig } from './base/useResourceConfigs';

export function usePodcasts() {
  const {
    items: podcasts,
    loading,
    error,
    createItem,
    updateItem,
    deleteItem,
    getItemById,
    updateItemOrder
  } = useBaseResource(podcastConfig);

  // Audio ve kapak dosyası ile podcast oluştur
  const createPodcast = async (podcastData, audioFile, coverFile) => {
    const files = {
      ...(audioFile && { audio: audioFile }),
      ...(coverFile && { cover: coverFile })
    };
    return await createItem(podcastData, files);
  };

  // Audio ve kapak dosyası ile podcast güncelle
  const updatePodcast = async (id, podcastData, audioFile, coverFile) => {
    const files = {
      ...(audioFile && { audio: audioFile }),
      ...(coverFile && { cover: coverFile })
    };
    return await updateItem(id, podcastData, files);
  };

  return {
    podcasts,
    loading,
    error,
    getPodcastById: getItemById,
    createPodcast,
    updatePodcast,
    deletePodcast: deleteItem,
    updatePodcastOrder: updateItemOrder
  };
} 