import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase } from '../../../supabaseClient';
import { useContentBlog } from '../../../contexts/ContentContext';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  FiPlus, FiArrowLeft, FiSave, FiEdit, FiTrash2,
  FiSearch, FiX, FiUpload, FiImage, FiMenu, FiInfo, FiEye, FiFileText
} from 'react-icons/fi';
import AlertContainer from '../../../components/Alert';
import BlogEditor from './BlogEditor';

const BlogManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const [user, setUser] = useState(null);
  const [message, setMessage] = useState({ type: '', text: '' });

  const { posts, fetchPosts, loading, createPost, updatePost, deletePost } = useContentBlog();

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.user) {
        setUser(session.user);
      } else {
        navigate('/login');
      }
    };
    fetchUser();
  }, [navigate]);

  useEffect(() => {
    if (user) {
      fetchPosts();
    }
  }, [user, fetchPosts]);
  
  const showMessage = (type, text) => {
    setMessage({ type, text });
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(posts);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updates = items.map((post, index) => ({
      id: post.id,
      display_order: index,
    }));

    // Optimistic UI update
    fetchPosts();

    const { error } = await supabase.from('blog_posts').upsert(updates);

    if (error) {
      showMessage('error', 'Sıralama güncellenirken bir hata oluştu.');
      // Revert UI change on error
      fetchPosts();
    } else {
      showMessage('success', 'Sıralama başarıyla güncellendi.');
    }
  };

  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = posts.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeletePost = async (postId) => {
     if (!window.confirm('Bu gönderiyi silmek istediğinizden emin misiniz?')) {
      return;
    }
    await deletePost(postId);
    showMessage('success', 'Gönderi başarıyla silindi.');
  }

  const renderList = () => (
    <div className="content-main-area list-page-container">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)', color: '#3b82f6' }}>
            <FiFileText size={24} />
          </div>
          <div className="content-title">
            <h2>Blog Yazıları</h2>
          </div>
          <div className="header-info-box">
            <FiInfo size={16} />
            <span>Sıralamayı değiştirmek için sürükleyip bırakın.</span>
          </div>
        </div>
        
        <div className="content-actions">
          <div className="search-container">
            <div className="search-box">
              <FiSearch size={20} />
              <input
                type="text"
                placeholder="Yazı ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button className="clear-search" onClick={() => setSearchTerm('')}>
                  <FiX size={16} />
                </button>
              )}
            </div>
          </div>
          <button
            className="action-button primary"
            onClick={() => navigate('/dashboard/blogs/add')}
          >
            <FiPlus size={18} />
            <span>Yeni Yazı Ekle</span>
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Blog yazıları yükleniyor...</p>
        </div>
      ) : (
        <div className="list-table-container">
          <div className="list-table-header">
            <div className="header-cell drag-handle"></div>
            <div className="header-cell icon">Resim</div>
            <div className="header-cell title">Başlık</div>
            <div className="header-cell link">Durum</div>
            <div className="header-cell actions">İşlemler</div>
          </div>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="blog-posts">
              {(provided) => (
                <div 
                  className="list-table-body"
                  {...provided.droppableProps} 
                  ref={provided.innerRef}
                >
                  {filteredPosts.length > 0 ? (
                    filteredPosts.map((post, index) => (
                      <Draggable key={post.id} draggableId={post.id.toString()} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`list-table-row ${snapshot.isDragging ? 'dragging' : ''}`}
                          >
                            <div className="body-cell drag-handle" {...provided.dragHandleProps}>
                              <FiMenu size={20} />
                            </div>
                            <div className="body-cell icon">
                              <div className="icon-wrapper">
                                {post.featured_image ? (
                                  <img src={post.featured_image} alt={post.title} />
                                ) : (
                                  <FiImage size={24} />
                                )}
                              </div>
                            </div>
                            <div className="body-cell title">
                              <span>{post.title}</span>
                            </div>
                            <div className="body-cell link">
                              <span className={`status-badge ${post.status}`}>
                                {post.status === 'published' ? 'Yayımda' : 'Taslak'}
                              </span>
                            </div>
                            <div className="body-cell actions">
                              <button
                                className="action-btn edit"
                                onClick={() => navigate(`/dashboard/blogs/edit/${post.id}`)}
                                title="Düzenle"
                              >
                                <FiEdit size={16} />
                              </button>
                              <button
                                className="action-btn delete"
                                onClick={() => handleDeletePost(post.id)}
                                title="Sil"
                              >
                                <FiTrash2 size={16} />
                              </button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))
                  ) : (
                    <div className="empty-state">
                      <div className="empty-icon">
                        <FiFileText size={48} />
                      </div>
                      <h4>{searchTerm ? 'Arama sonucu bulunamadı' : 'Henüz blog yazısı eklenmemiş'}</h4>
                      <p>{searchTerm 
                        ? 'Farklı bir anahtar kelimeyle tekrar deneyin' 
                        : 'İlk blog yazınızı ekleyerek başlayın'}
                      </p>
                      {!searchTerm && (
                        <button 
                          className="add-first-btn"
                          onClick={() => navigate('/dashboard/blogs/add')}
                        >
                          <FiPlus />
                          İlk Yazıyı Ekle
                        </button>
                      )}
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      )}
    </div>
  );
  
  if (!user) {
      return <div className="loading-state"><div className="spinner"></div></div>;
  }
  
  if (mode === 'add' || (mode === 'edit' && id)) {
    return <BlogEditor postId={mode === 'edit' ? id : null} />;
  }

  return (
    <>
      <AlertContainer
        message={message.text}
        type={message.type}
        onClose={() => setMessage({ type: '', text: '' })}
      />
      {renderList()}
    </>
  );
};

export default BlogManager;