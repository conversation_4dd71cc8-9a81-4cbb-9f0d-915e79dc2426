-- Migration: Add service packages table
-- Created: 2024-06-21

-- Hizmet paketleri tablosu
CREATE TABLE service_packages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_months INTEGER NOT NULL, -- 1 = aylık, 12 = yıllık
    currency VARCHAR(3) DEFAULT 'TRY',
    features TEXT[] DEFAULT '{}', -- Paket özelliklerinin listesi
    is_popular BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sample data - Sadece 2 paket: Aylık ve Yıllık
INSERT INTO service_packages (name, description, price, duration_months, features, is_popular, is_active) VALUES
('A<PERSON><PERSON>k Plan', '<PERSON><PERSON><PERSON><PERSON> abonelik paketi - <PERSON><PERSON><PERSON><PERSON><PERSON> zaman iptal edebilirsin', 39.99, 1, ARRAY['Kişisel profil sayfası', 'Sosyal medya bağlantıları', 'QR kod', 'Temel analitik', 'E-posta desteği', 'Özelleştirilebilir tasarım'], false, true),
('Yıllık Plan', 'Yıllık abonelik paketi - %20 indirimli!', 383.99, 12, ARRAY['Kişisel profil sayfası', 'Sosyal medya bağlantıları', 'QR kod', 'Gelişmiş analitik', 'Öncelikli destek', 'Özelleştirilebilir tasarım', 'İstatistik raporları', 'API erişimi'], true, true);

-- Kullanıcı abonelik geçmişi tablosu
CREATE TABLE user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    package_id UUID REFERENCES service_packages(id) ON DELETE RESTRICT,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active', -- active, expired, cancelled, pending
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    amount_paid DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'TRY',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Politikaları
ALTER TABLE service_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Herkes paketleri görebilir
CREATE POLICY "Service packages are viewable by everyone" ON service_packages
    FOR SELECT USING (is_active = true);

-- Sadece admin paketleri düzenleyebilir
CREATE POLICY "Service packages are editable by admins only" ON service_packages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Kullanıcılar kendi aboneliklerini görebilir
CREATE POLICY "Users can view own subscriptions" ON user_subscriptions
    FOR SELECT USING (user_id = auth.uid());

-- Admin tüm abonelikleri görebilir
CREATE POLICY "Admins can view all subscriptions" ON user_subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Abonelik ekleme/güncelleme sadece admin
CREATE POLICY "Only admins can manage subscriptions" ON user_subscriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Indexes for performance
CREATE INDEX idx_service_packages_active ON service_packages(is_active);
CREATE INDEX idx_service_packages_duration ON service_packages(duration_months);
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_dates ON user_subscriptions(start_date, end_date); 