-- Create faq_items table
CREATE TABLE IF NOT EXISTS public.faq_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE public.faq_items ENABLE ROW LEVEL SECURITY;

-- Users can only access their own faq items
CREATE POLICY "Users can view own faq items" ON public.faq_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own faq items" ON public.faq_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own faq items" ON public.faq_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own faq items" ON public.faq_items
    FOR DELETE USING (auth.uid() = user_id);

-- Add indexes for better performance
CREATE INDEX idx_faq_items_user_id ON public.faq_items(user_id);
CREATE INDEX idx_faq_items_created_at ON public.faq_items(created_at); 