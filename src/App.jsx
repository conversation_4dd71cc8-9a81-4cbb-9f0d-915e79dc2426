import React from 'react';
import { SidebarProvider } from './contexts/SidebarContext';
import { ContentProvider } from './contexts/ContentContext';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Home from './pages/Home';
import Profile from './pages/Profile';
import NotFound from './pages/NotFound';
import Login from './pages/Login';
import AdminLayout from './pages/admin/AdminLayout';
import Dashboard from './pages/admin/Dashboard';
import Users from './pages/admin/Users';
import AdminRoute from './components/AdminRoute';
import EditUser from './pages/admin/EditUser';
import Settings from './pages/admin/Settings';
import Packages from './pages/admin/Packages';
import UserProfile from './pages/user/UserProfile';
import ContentManagement from './pages/user/ContentManagement';
import BlogManager from './pages/user/components/BlogManager';
import FaqManager from './pages/user/components/FaqManager';
import LinkManager from './pages/user/components/LinkManager';
import GalleryManager from './pages/user/components/GalleryManager';
import VideoManager from './pages/user/components/VideoManager';
import CatalogManager from './pages/user/components/CatalogManager';
import PodcastManager from './pages/user/components/PodcastManager';

function App() {
  return (
    <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<Login />} />

        {/* User Dashboard - Content Management artık ana dashboard */}
        <Route path="/dashboard/user-profile" element={<UserProfile />} />
        
        {/* Content Management Routes - Artık ana dashboard */}
        <Route path="/dashboard" element={
          <ContentProvider>
            <SidebarProvider>
              <ContentManagement />
            </SidebarProvider>
          </ContentProvider>
        }>
          {/* BlogManager Routes */}
          <Route path="blogs/:mode" element={<BlogManager />} />
          <Route path="blogs/:mode/:id" element={<BlogManager />} />

          {/* FaqManager Routes */}
          <Route path="faqs/:mode" element={<FaqManager />} />
          <Route path="faqs/:mode/:id" element={<FaqManager />} />

          {/* LinkManager Routes */}
          <Route path="links/:mode" element={<LinkManager />} />
          <Route path="links/:mode/:id" element={<LinkManager />} />
          
          {/* GalleryManager Routes */}
          <Route path="galleries/:mode" element={<GalleryManager />} />
          <Route path="galleries/:mode/:id" element={<GalleryManager />} />

          {/* VideoManager Routes */}
          <Route path="videos/:mode" element={<VideoManager />} />
          <Route path="videos/:mode/:id" element={<VideoManager />} />

          {/* CatalogManager Routes */}
          <Route path="catalogs/:mode" element={<CatalogManager />} />
          <Route path="catalogs/:mode/:id" element={<CatalogManager />} />

          {/* PodcastManager Routes */}
          <Route path="podcasts/:mode" element={<PodcastManager />} />
          <Route path="podcasts/:mode/:id" element={<PodcastManager />} />
        </Route>

        {/* Admin Routes - Sadece adminler erişebilir */}
        <Route element={<AdminRoute />}>
          <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<Dashboard />} />
            <Route path="users" element={<Users />} />
            <Route path="users/:userId" element={<EditUser />} />
            <Route path="packages" element={<Packages />} />
            <Route path="settings" element={<Settings />} />
          </Route>
        </Route>

        {/* Profile Routes - Son sırada olmalı */}
        <Route path="/:username" element={<Profile />} />

        {/* Not Found */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

// Örnek sayfaları da ekleyelim ki hata vermesin
// const Page = ({ title }) => <h2>{title}</h2>;
// const Home = () => <Page title="Ana Sayfa - Profilleri Keşfet" />;
// const Profile = () => <Page title="Profil Sayfası" />;
// const NotFound = () => <Page title="404 - Sayfa Bulunamadı" />;

export default App; 