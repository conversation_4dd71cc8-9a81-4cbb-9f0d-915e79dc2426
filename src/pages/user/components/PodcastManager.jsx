import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentPodcasts } from '../../../contexts/ContentContext';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  FiMic, FiPlus, FiArrowLeft, FiSave, FiTrash2, FiEdit, 
  FiSearch, FiMenu, FiUpload, FiPlayCircle, FiEye, FiEyeOff
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const PodcastManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const { 
    podcasts, loading, getPodcastById, createPodcast, 
    updatePodcast, deletePodcast, updatePodcastOrder 
  } = useContentPodcasts();
  
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [formData, setFormData] = useState({
    title: '',
    audio_url: '',
    cover_image_url: '',
    duration_seconds: 0,
    is_active: true,
  });

  const [audioFile, setAudioFile] = useState(null);
  const [coverFile, setCoverFile] = useState(null);
  const [coverPreview, setCoverPreview] = useState('');

  useEffect(() => {
    if (mode === 'edit' && id) {
      const loadPodcast = async () => {
        try {
          const data = await getPodcastById(id);
          if (data) {
            setFormData(data);
            if(data.cover_image_url) setCoverPreview(data.cover_image_url);
            if (data.file_url) setFormData(prev => ({ ...prev, file_url: data.file_url }));
          }
        } catch (error) {
          showToast.error('Podcast bilgileri yüklenirken bir hata oluştu.');
          navigate('/dashboard/podcasts/list');
        }
      };
      loadPodcast();
    } else if (mode === 'add') {
      setFormData({ title: '', audio_url: '', cover_image_url: '', duration_seconds: 0, is_active: true });
      setCoverFile(null);
      setAudioFile(null);
      setCoverPreview('');
    }
  }, [mode, id, getPodcastById, navigate]);
  
  const handleCoverFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setCoverFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setCoverPreview(reader.result);
    reader.readAsDataURL(file);
  };

  const handleAudioFileChange = (e) => {
    const file = e.target.files[0];
    if (!file || !file.type.startsWith('audio/')) {
      showToast.error('Lütfen geçerli bir ses dosyası seçin.');
      return;
    }
    setAudioFile(file);
    const audio = document.createElement('audio');
    audio.src = URL.createObjectURL(file);
    audio.onloadedmetadata = () => {
      setFormData(prev => ({...prev, duration_seconds: Math.round(audio.duration)}));
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.title.trim()) {
      showToast.warning('Başlık alanı zorunludur!');
      return;
    }
    if (mode === 'add' && !audioFile) {
      showToast.warning('Lütfen bir podcast dosyası seçin.');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'edit' && id) {
        await updatePodcast(id, formData, audioFile, coverFile);
      } else {
        await createPodcast(formData, audioFile, coverFile);
      }
      navigate('/dashboard/podcasts/list');
    } catch (error) {
      showToast.error('Podcast kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (podcastId) => {
    if (!window.confirm('Bu podcast\'i silmek istediğinizden emin misiniz?')) return;
    try {
      await deletePodcast(podcastId);
    } catch (error) {
      showToast.error('Podcast silinirken bir hata oluştu.');
    }
  };
  
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(podcasts);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    try {
      await updatePodcastOrder(items);
    } catch (error) {
      showToast.error('Sıralama güncellenirken bir hata oluştu.');
    }
  };
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };
  
  const formatDuration = (seconds) => {
    if (isNaN(seconds) || seconds < 0) return "00:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleToggleStatus = async (podcast) => {
    try {
      await updatePodcast(podcast.id, { is_active: !podcast.is_active });
    } catch (error) {
      showToast.error('Podcast durumu güncellenirken bir hata oluştu.');
    }
  };

  const filteredPodcasts = podcasts.filter(podcast =>
    podcast.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (mode === 'add' || mode === 'edit') {
    return (
      <div className="content-main-area">
        <div className="content-header">
           <div className="content-header-info">
            <div className="content-icon" style={{ backgroundColor: 'rgba(20, 184, 166, 0.1)', color: '#14b8a6' }}>
              <FiPlayCircle size={24} />
            </div>
            <div className="content-title">
              <h2>{mode === 'edit' ? 'Podcast Düzenle' : 'Yeni Podcast Ekle'}</h2>
              <p>Podcast bilgilerinizi ve ses dosyanızı buradan yönetin.</p>
            </div>
          </div>
          <div className="content-actions">
            <button className="action-button secondary" onClick={() => navigate('/dashboard/podcasts/list')}>
              <FiArrowLeft size={18} />
              <span>Listeye Dön</span>
            </button>
          </div>
        </div>
        <div className="content-body">
          <form className="content-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="podcast-title">Podcast Başlığı</label>
              <input type="text" id="podcast-title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Bölüm başlığını girin" required />
            </div>
            <div className="form-group">
                <label>Kapak Resmi (Opsiyonel)</label>
                <div className="file-upload-container">
                    <input type="file" id="cover-file" accept="image/png, image/jpeg" onChange={handleCoverFileChange} />
                    <label htmlFor="cover-file" className="file-upload-label">
                        <FiUpload size={18} />
                        <span>{coverFile ? 'Resmi Değiştir' : 'Resim Seç'}</span>
                    </label>
                    {coverPreview && <div className="image-preview"><img src={coverPreview} alt="Kapak Resmi Önizleme" /></div>}
                </div>
            </div>
             <div className="form-group">
                <label>Ses Dosyası (MP3, WAV, vb.)</label>
                <div className="file-upload-container">
                    <input type="file" id="audio-file" accept="audio/*" onChange={handleAudioFileChange} />
                    <label htmlFor="audio-file" className="file-upload-label">
                        <FiUpload size={18} />
                        <span>{audioFile ? audioFile.name : 'Ses Dosyası Seç'}</span>
                    </label>
                    {formData.audio_url && !audioFile && <a href={formData.audio_url} target="_blank" rel="noreferrer" className="text-sm text-blue-500">Mevcut Dosyayı Dinle</a>}
                </div>
                {formData.duration_seconds > 0 && <small>Süre: {formatDuration(formData.duration_seconds)}</small>}
            </div>
            <div className="form-group">
                <label>Durum</label>
                <div className="toggle-switch">
                    <input type="checkbox" id="is_active" name="is_active" checked={formData.is_active} onChange={handleInputChange} />
                    <label htmlFor="is_active"></label>
                    <span>{formData.is_active ? 'Yayında' : 'Taslak'}</span>
                </div>
            </div>
            <div className="form-actions">
              <button type="button" className="action-button secondary" onClick={() => navigate('/dashboard/podcasts/list')}>İptal</button>
              <button type="submit" className="action-button primary" disabled={saving}>
                <FiSave size={18} />
                <span>{saving ? 'Kaydediliyor...' : (mode === 'edit' ? 'Değişiklikleri Kaydet' : 'Podcast Ekle')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: 'rgba(20, 184, 166, 0.1)', color: '#14b8a6' }}>
            <FiPlayCircle size={24} />
          </div>
          <div className="content-title">
            <h2>Podcast Yönetimi</h2>
            <p>Sesli yayınlarınızı buradan yönetin.</p>
          </div>
        </div>
        <div className="content-actions">
          <button className="action-button primary" onClick={() => navigate('/dashboard/podcasts/add')}>
            <FiPlus size={18} />
            <span>Yeni Podcast Ekle</span>
          </button>
        </div>
      </div>
      
      <div className="content-body">
        <div className="content-toolbar">
          <div className="search-box">
            <FiSearch className="search-icon" />
            <input type="text" placeholder="Podcast ara..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            {searchTerm && <FiEyeOff className="clear-icon" onClick={() => setSearchTerm('')} />}
          </div>
        </div>

        {loading ? (
          <div className="loading-indicator"><p>Podcastler yükleniyor...</p></div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="podcasts">
              {(provided) => (
                <div className="draggable-list" {...provided.droppableProps} ref={provided.innerRef}>
                  {filteredPodcasts.length > 0 ? (
                    filteredPodcasts.map((podcast, index) => (
                      <Draggable key={podcast.id} draggableId={podcast.id.toString()} index={index}>
                        {(provided, snapshot) => (
                          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps} className={`draggable-item ${snapshot.isDragging ? 'dragging' : ''}`}>
                            <div className="drag-handle"><FiMenu /></div>
                            <div className="item-thumbnail">
                              {podcast.cover_image_url ? <img src={podcast.cover_image_url} alt={podcast.title} /> : <div className="thumbnail-placeholder"><FiPlayCircle /></div>}
                            </div>
                            <div className="item-content">
                              <h4 className="item-title">{podcast.title}</h4>
                              <span className="item-meta">Süre: {formatDuration(podcast.duration_seconds)}</span>
                            </div>
                            <div className="item-status">
                              <span className="item-meta"><FiEye size={14} style={{verticalAlign: 'middle', marginRight: '4px'}}/>{podcast.view_count} dinlenme</span>
                              <span className={`status-badge ${podcast.is_active ? 'active' : 'inactive'}`}>{podcast.is_active ? 'Yayında' : 'Taslak'}</span>
                            </div>
                            <div className="item-actions">
                              <button className="action-button-icon" onClick={() => navigate(`/dashboard/podcasts/edit/${podcast.id}`)} title="Düzenle"><FiEdit /></button>
                              <button className="action-button-icon danger" onClick={() => handleDelete(podcast.id)} title="Sil"><FiTrash2 /></button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))
                  ) : (
                    <div className="empty-state">
                      <FiPlayCircle size={48} />
                      <h3>Henüz podcast eklenmemiş.</h3>
                      <p>Yeni bir podcast bölümü ekleyerek başlayabilirsiniz.</p>
                      <button className="action-button primary" onClick={() => navigate('/dashboard/podcasts/add')}>
                        <FiPlus size={18} />
                        <span>İlk Podcasti Ekle</span>
                      </button>
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </div>
    </div>
  );
};

export default PodcastManager; 