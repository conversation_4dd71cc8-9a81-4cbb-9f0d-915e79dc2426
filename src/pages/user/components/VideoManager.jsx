import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentVideos } from '../../../contexts/ContentContext';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  FiPlus, FiArrowLeft, FiSave, FiEdit, FiTrash2, FiSearch, 
  FiX, FiUpload, FiImage, FiVideo, FiMenu, FiCheckSquare, FiSquare
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const VideoManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const {
    videos, loading, getVideoById, createVideo,
    updateVideo, deleteVideo, updateVideoOrder
  } = useContentVideos();
  
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    embed_code: '',
    thumbnail_url: '',
    is_active: true,
  });
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState('');

  useEffect(() => {
    if (mode === 'edit' && id) {
      const loadVideo = async () => {
        try {
          const data = await getVideoById(id);
          if (data) {
            setFormData({
              title: data.title || '',
              description: data.description || '',
              video_url: data.video_url || '',
              is_active: data.is_active,
            });
            if (data.thumbnail_url) setThumbnailPreview(data.thumbnail_url);
          }
        } catch (error) {
          showToast.error('Video yüklenirken bir hata oluştu.');
          navigate('/dashboard/videos/list');
        }
      };
      loadVideo();
    } else if (mode === 'add') {
      setFormData({ title: '', description: '', embed_code: '', thumbnail_url: '', is_active: true });
      setThumbnailFile(null);
      setThumbnailPreview('');
    }
  }, [mode, id, getVideoById, navigate]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (file.size > 5 * 1024 * 1024) {
      showToast.error('Dosya boyutu 5MB\'dan küçük olmalıdır.');
      return;
    }
    setThumbnailFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setThumbnailPreview(reader.result);
    reader.readAsDataURL(file);
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.video_url.trim()) {
      showToast.warning('Başlık ve video linki alanları zorunludur!');
      return;
    }
    setSaving(true);
    try {
      if (mode === 'edit' && id) {
        await updateVideo(id, formData);
      } else {
        await createVideo(formData);
      }
      navigate('/dashboard/videos/list');
    } catch (error) {
      showToast.error('Video kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (videoId) => {
    if (!window.confirm('Bu videoyu silmek istediğinizden emin misiniz?')) return;
    try {
      await deleteVideo(videoId);
      showToast.success('Video başarıyla silindi.');
    } catch (error) {
      showToast.error('Video silinirken bir hata oluştu.');
    }
  };
  
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    const items = Array.from(videos);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    try {
      await updateVideoOrder(items);
    } catch (err) {
      showToast.error('Video sıralaması güncellenirken bir hata oluştu.');
    }
  };

  const handleToggleStatus = async (video) => {
    try {
      await updateVideo(video.id, { is_active: !video.is_active });
    } catch (err) {
      showToast.error('Video durumu güncellenirken bir hata oluştu.');
    }
  };

  const filteredVideos = videos.filter(v => v.title.toLowerCase().includes(searchTerm.toLowerCase()));

  if (mode === 'add' || mode === 'edit') {
    return (
      <div className="content-main-area">
        <div className="content-header">
          <div className="content-header-info">
            <div className="content-icon" style={{ backgroundColor: 'rgba(236, 72, 153, 0.1)', color: '#ec4899' }}>
              <FiVideo size={24} />
            </div>
            <div className="content-title">
              <h2>{mode === 'edit' ? 'Video Düzenle' : 'Yeni Video Ekle'}</h2>
              <p>Video içeriğinizi buradan yönetebilirsiniz.</p>
            </div>
          </div>
          <div className="content-actions">
            <button 
              className="action-button secondary"
              onClick={() => navigate('/dashboard/videos/list')}
            >
              <FiArrowLeft size={18} />
              <span>Listeye Dön</span>
            </button>
          </div>
        </div>
        
        <div className="content-body">
          <form className="content-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="video-title">Video Başlığı</label>
              <input 
                type="text" 
                id="video-title" 
                name="title"
                value={formData.title} 
                onChange={handleInputChange} 
                placeholder="Video başlığını girin"
                required 
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="video-description">Açıklama (Opsiyonel)</label>
              <textarea 
                id="video-description" 
                name="description" 
                value={formData.description}
                onChange={handleInputChange} 
                placeholder="Video hakkında kısa bir açıklama"
                rows="3"
              />
            </div>

            <div className="form-group">
              <label htmlFor="video-embed">Gömme Kodu (Embed Code)</label>
              <textarea 
                id="video-embed" 
                name="embed_code"
                value={formData.embed_code}
                onChange={handleInputChange} 
                placeholder="YouTube, Vimeo vb. platformlardan aldığınız <iframe> kodunu yapıştırın"
                required
                rows="4"
              />
            </div>
            
            <div className="form-group">
              <label>Kapak Resmi</label>
              <div className="file-upload-container">
                <input 
                  type="file" 
                  id="thumbnail-file" 
                  accept="image/png, image/jpeg" 
                  onChange={handleFileChange}
                />
                <label htmlFor="thumbnail-file" className="file-upload-label">
                  <FiUpload size={18} />
                  <span>{thumbnailFile ? 'Resmi Değiştir' : 'Resim Seç'}</span>
                </label>
                {thumbnailPreview && (
                  <div className="image-preview">
                    <img src={thumbnailPreview} alt="Kapak Resmi Önizleme" />
                  </div>
                )}
              </div>
               <small>En iyi görünüm için 1280x720 piksel boyutunda bir resim yükleyin. (Max 5MB)</small>
            </div>
            
            <div className="form-group">
                <label>Durum</label>
                <div className="toggle-switch">
                    <input 
                        type="checkbox" 
                        id="is_active" 
                        name="is_active" 
                        checked={formData.is_active} 
                        onChange={handleInputChange} 
                    />
                    <label htmlFor="is_active"></label>
                    <span>{formData.is_active ? 'Yayında' : 'Taslak'}</span>
                </div>
            </div>

            <div className="form-actions">
              <button 
                type="button" 
                className="action-button secondary"
                onClick={() => navigate('/dashboard/videos/list')}
              >
                İptal
              </button>
              <button type="submit" className="action-button primary" disabled={saving}>
                <FiSave size={18} />
                <span>{saving ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: 'rgba(236, 72, 153, 0.1)', color: '#ec4899' }}>
            <FiVideo size={24} />
          </div>
          <div className="content-title">
            <h2>Video Galerisi</h2>
            <p>Video içeriklerinizi buradan yönetebilirsiniz.</p>
          </div>
        </div>
        <div className="content-actions">
          <button 
            className="action-button primary"
            onClick={() => navigate('/dashboard/videos/add')}
          >
            <FiPlus size={18} />
            <span>Yeni Video Ekle</span>
          </button>
        </div>
      </div>
      
      <div className="content-body">
        <div className="content-toolbar">
          <div className="search-box">
            <FiSearch className="search-icon" />
            <input 
              type="text" 
              placeholder="Videolar içinde ara..." 
              value={searchTerm} 
              onChange={(e) => setSearchTerm(e.target.value)} 
            />
            {searchTerm && <FiX className="clear-icon" onClick={() => setSearchTerm('')} />}
          </div>
        </div>

        {loading ? (
          <div className="loading-indicator">
            <p>Videolar yükleniyor...</p>
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="videos">
              {(provided) => (
                <div className="draggable-list" {...provided.droppableProps} ref={provided.innerRef}>
                  {filteredVideos.length > 0 ? (
                    filteredVideos.map((video, index) => (
                      <Draggable key={video.id} draggableId={video.id.toString()} index={index}>
                        {(provided, snapshot) => (
                          <div 
                            ref={provided.innerRef} 
                            {...provided.draggableProps} 
                            {...provided.dragHandleProps}
                            className={`draggable-item ${snapshot.isDragging ? 'dragging' : ''}`}
                          >
                            <div className="drag-handle">
                              <FiMenu />
                            </div>
                            <div className="item-thumbnail video-thumbnail">
                              {video.thumbnail_url ? (
                                <img src={video.thumbnail_url} alt={video.title} />
                              ) : (
                                <div className="thumbnail-placeholder"><FiImage /></div>
                              )}
                            </div>
                            <div className="item-content">
                              <h4 className="item-title">{video.title}</h4>
                            </div>
                            <div className="item-status">
                              <span className={`status-badge ${video.is_active ? 'active' : 'inactive'}`}>
                                {video.is_active ? 'Yayında' : 'Taslak'}
                              </span>
                            </div>
                            <div className="item-actions">
                              <button 
                                className="action-button-icon"
                                onClick={() => navigate(`/dashboard/videos/edit/${video.id}`)}
                                title="Düzenle"
                              >
                                <FiEdit />
                              </button>
                              <button 
                                className="action-button-icon danger"
                                onClick={() => handleDelete(video.id)}
                                title="Sil"
                              >
                                <FiTrash2 />
                              </button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))
                  ) : (
                    <div className="empty-state">
                      <FiVideo size={48} />
                      <h3>Henüz video eklenmemiş.</h3>
                      <p>Yeni bir video ekleyerek galerini oluşturmaya başla.</p>
                      <button 
                        className="action-button primary"
                        onClick={() => navigate('/dashboard/videos/add')}
                      >
                        <FiPlus size={18} />
                        <span>İlk Videonu Ekle</span>
                      </button>
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </div>
    </div>
  );
};

export default VideoManager; 