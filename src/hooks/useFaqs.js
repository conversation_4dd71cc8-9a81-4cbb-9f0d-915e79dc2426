import { useBaseResource } from './base/useBaseResource';
import { faqConfig } from './base/useResourceConfigs';

/**
 * FAQ management hook using the generic base resource
 */
export function useFaqs() {
  const baseResource = useBaseResource('faq_items', faqConfig);
  
  return {
    // Data
    faqs: baseResource.items,
    loading: baseResource.loading,
    error: baseResource.error,
    initialized: baseResource.initialized,
    
    // Methods - FAQ specific names
    fetchFaqs: baseResource.fetchItems,
    getFaqById: baseResource.getItemById,
    createFaq: baseResource.createItem,
    updateFaq: baseResource.updateItem,
    deleteFaq: baseResource.deleteItem,
    
    // Utilities
    refreshFaqs: baseResource.refreshCache
  };
} 