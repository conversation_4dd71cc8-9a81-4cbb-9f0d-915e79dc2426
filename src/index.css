/* Modular CSS Architecture */

/* Base Styles */
@import './styles/base/variables.css';
@import './styles/base/reset.css';

/* Layout Styles */
@import './styles/layout/admin.css';
@import './styles/layout/dashboard.css';

/* Component Styles */
@import './styles/components/buttons.css';
@import './styles/components/forms.css';
@import './styles/components/notifications.css';
@import './styles/components/alerts.css';
@import './styles/components/blog-editor.css';

/* Page Styles */
@import './styles/pages/home.css';
@import './styles/pages/auth.css';
@import './styles/pages/user-profile.css';
@import './styles/pages/user-dashboard.css';
@import './styles/pages/content-management.css';
@import './styles/pages/profile.css';

/* Global Toast Notifications Styles */
.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.Toastify__toast {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  min-height: 64px !important;
  padding: 16px !important;
}

.Toastify__toast--success {
  background: linear-gradient(135deg, #10b981, #34d399) !important;
  color: white !important;
}

.Toastify__toast--error {
  background: linear-gradient(135deg, #ef4444, #f87171) !important;
  color: white !important;
}

.Toastify__toast--info {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  color: white !important;
}

.Toastify__toast--warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24) !important;
  color: white !important;
}

.Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.8) !important;
  height: 3px !important;
}

.Toastify__close-button {
  color: rgba(255, 255, 255, 0.8) !important;
  opacity: 0.8 !important;
}

.Toastify__close-button:hover {
  opacity: 1 !important;
}

.Toastify__toast-container {
  width: 380px !important;
}

@media (max-width: 640px) {
  .Toastify__toast-container {
    width: calc(100vw - 32px) !important;
    left: 16px !important;
    right: 16px !important;
  }
}