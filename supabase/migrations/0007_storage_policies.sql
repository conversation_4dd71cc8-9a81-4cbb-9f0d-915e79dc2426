-- Önce mevcut politikaları temizle
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;

-- Storage objeleri için güvenlik politikaları

-- Her<PERSON> avatars bucket'ındaki resimleri görebilir
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

-- <PERSON><PERSON><PERSON><PERSON><PERSON>lar kendi avatar'larını yükleyebilir
CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' 
    AND auth.uid() IS NOT NULL
  );

-- Kullanıcılar avatars bucket'ında update yapabilir (kendi dosyaları için)
CREATE POLICY "Users can update avatars" ON storage.objects
  FOR UPDATE USING (bucket_id = 'avatars');

-- <PERSON>llanıcılar avatars bucket'ında delete yapabilir (kendi dosyaları için)
CREATE POLICY "Users can delete avatars" ON storage.objects
  FOR DELETE USING (bucket_id = 'avatars'); 