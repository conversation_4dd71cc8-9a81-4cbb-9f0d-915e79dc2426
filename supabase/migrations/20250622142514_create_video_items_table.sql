-- Create the video_items table
CREATE TABLE public.video_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL CHECK (char_length(title) > 0),
    description TEXT,
    thumbnail_url TEXT,
    embed_code TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    view_count INTEGER NOT NULL DEFAULT 0,
    display_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comments to columns for clarity
COMMENT ON COLUMN public.video_items.title IS 'Video title';
COMMENT ON COLUMN public.video_items.description IS 'Video description';
COMMENT ON COLUMN public.video_items.thumbnail_url IS 'URL for the video thumbnail image';
COMMENT ON COLUMN public.video_items.embed_code IS 'Embed code for the video (e.g., YouTube iframe)';
COMMENT ON COLUMN public.video_items.is_active IS 'Whether the video is currently active and visible';
COMMENT ON COLUMN public.video_items.view_count IS 'How many times the video has been viewed';
COMMENT ON COLUMN public.video_items.display_order IS 'Order in which the videos are displayed';


-- Create a trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_video_items_updated
BEFORE UPDATE ON public.video_items
FOR EACH ROW
EXECUTE PROCEDURE public.handle_updated_at();

-- Enable Row Level Security
ALTER TABLE public.video_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Allow users to see their own videos"
ON public.video_items
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Allow users to insert their own videos"
ON public.video_items
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own videos"
ON public.video_items
FOR UPDATE USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own videos"
ON public.video_items
FOR DELETE USING (auth.uid() = user_id);

-- Create a storage bucket for video thumbnails
INSERT INTO storage.buckets (id, name, public)
VALUES ('video-thumbnails', 'video-thumbnails', true)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for the new storage bucket
CREATE POLICY "Allow authenticated users to upload video thumbnails"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'video-thumbnails' AND (storage.foldername(name))[1] = auth.uid()::text);

CREATE POLICY "Allow anyone to view video thumbnails"
ON storage.objects FOR SELECT
USING (bucket_id = 'video-thumbnails');

CREATE POLICY "Allow users to update their own video thumbnails"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'video-thumbnails' AND (storage.foldername(name))[1] = auth.uid()::text);

CREATE POLICY "Allow users to delete their own video thumbnails"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'video-thumbnails' AND (storage.foldername(name))[1] = auth.uid()::text);
