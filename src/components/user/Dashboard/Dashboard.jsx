import React from 'react';
import { useContent } from '../../../contexts/ContentContext';
import { FiDatabase, FiActivity, FiUsers, FiTrendingUp } from 'react-icons/fi';
import './Dashboard.css';

function Dashboard() {
  const { 
    getTotalCounts, 
    isLoading, 
    hasError, 
    getAllData,
    user 
  } = useContent();

  const counts = getTotalCounts;
  const allData = getAllData;
  
  const stats = [
    {
      title: 'Blog Yazıları',
      count: counts.blog,
      icon: <FiDatabase className="stat-icon" />,
      color: 'blue',
      data: allData.blog.posts
    },
    {
      title: '<PERSON><PERSON>',
      count: counts.gallery,
      icon: <FiActivity className="stat-icon" />,
      color: 'green',
      data: allData.gallery
    },
    {
      title: 'Video İçerikleri',
      count: counts.videos,
      icon: <FiUsers className="stat-icon" />,
      color: 'purple',
      data: allData.videos
    },
    {
      title: 'SSS Soruları',
      count: counts.faqs,
      icon: <FiTrendingUp className="stat-icon" />,
      color: 'orange',
      data: allData.faqs
    },
    {
      title: 'Podcast\'ler',
      count: counts.podcasts,
      icon: <FiDatabase className="stat-icon" />,
      color: 'red',
      data: allData.podcasts
    },
    {
      title: 'Katalog Dosyaları',
      count: counts.catalog,
      icon: <FiActivity className="stat-icon" />,
      color: 'indigo',
      data: allData.catalog
    },
    {
      title: 'Linkler',
      count: counts.links,
      icon: <FiUsers className="stat-icon" />,
      color: 'pink',
      data: allData.links
    }
  ];

  const totalContent = Object.values(counts).reduce((sum, count) => sum + count, 0);

  if (isLoading()) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Dashboard yükleniyor...</p>
      </div>
    );
  }

  if (hasError()) {
    return (
      <div className="dashboard-error">
        <p>Dashboard yüklenirken bir hata oluştu.</p>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>İçerik Yönetimi Dashboard</h1>
        <p className="dashboard-subtitle">
          Merhaba {user?.name || user?.email}! İçeriklerinizi buradan yönetebilirsiniz.
        </p>
      </div>

      <div className="dashboard-overview">
        <div className="overview-card total">
          <div className="overview-icon">
            <FiDatabase />
          </div>
          <div className="overview-content">
            <h3>Toplam İçerik</h3>
            <p className="overview-count">{totalContent}</p>
          </div>
        </div>
      </div>

      <div className="dashboard-stats">
        {stats.map((stat, index) => (
          <div key={index} className={`stat-card ${stat.color}`}>
            <div className="stat-header">
              {stat.icon}
              <h3>{stat.title}</h3>
            </div>
            <div className="stat-count">
              {stat.count}
            </div>
            {stat.data && stat.data.length > 0 && (
              <div className="stat-recent">
                <p>Son eklenen:</p>
                <span>
                  {stat.data[0]?.title || 
                   stat.data[0]?.name || 
                   stat.data[0]?.question || 
                   'İsimsiz içerik'}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="dashboard-quick-actions">
        <h2>Hızlı İşlemler</h2>
        <div className="quick-actions-grid">
          <button className="quick-action-btn">
            <FiDatabase />
            <span>Yeni Blog Yazısı</span>
          </button>
          <button className="quick-action-btn">
            <FiActivity />
            <span>Galeri Oluştur</span>
          </button>
          <button className="quick-action-btn">
            <FiUsers />
            <span>Video Ekle</span>
          </button>
          <button className="quick-action-btn">
            <FiTrendingUp />
            <span>SSS Ekle</span>
          </button>
        </div>
      </div>

      {allData.blog.posts && allData.blog.posts.length > 0 && (
        <div className="dashboard-recent-content">
          <h2>Son Blog Yazıları</h2>
          <div className="recent-content-list">
            {allData.blog.posts.slice(0, 5).map((post, index) => (
              <div key={index} className="recent-content-item">
                <div className="recent-content-info">
                  <h4>{post.title}</h4>
                  <p>{new Date(post.created_at).toLocaleDateString('tr-TR')}</p>
                </div>
                <div className="recent-content-status">
                  <span className={`status ${post.status || 'draft'}`}>
                    {post.status === 'published' ? 'Yayınlandı' : 'Taslak'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default Dashboard; 