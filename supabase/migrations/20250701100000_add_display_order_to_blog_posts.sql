ALTER TABLE public.blog_posts
ADD COLUMN display_order integer;

UPDATE public.blog_posts
SET display_order = (
  SELECT rn - 1
  FROM (
    SELECT id, row_number() OVER (PARTITION BY user_id ORDER BY created_at ASC) as rn
    FROM public.blog_posts
  ) as t
  WHERE t.id = public.blog_posts.id
);

ALTER TABLE public.blog_posts
ALTER COLUMN display_order SET NOT NULL;

CREATE INDEX idx_blog_posts_display_order ON public.blog_posts(user_id, display_order); 