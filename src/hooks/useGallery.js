import { useBaseResource } from './base/useBaseResource';
import { galleryConfig } from './base/useResourceConfigs';
import { supabase } from '../supabaseClient';
import { useAuth } from './useAuth';
import showToast from '../utils/toast';

export function useGallery() {
  const { user } = useAuth();
  const {
    items: galleries,
    loading,
    error,
    createItem,
    updateItem,
    deleteItem,
    getItemById: baseGetItemById,
    updateItemOrder
  } = useBaseResource(galleryConfig);

  // Gallery'yi image'ları ile birlikte getir
  const getGalleryById = async (id) => {
    try {
      const gallery = await baseGetItemById(id);
      
      const { data: images, error: imagesError } = await supabase
        .from('gallery_images')
        .select('*')
        .eq('album_id', id)
        .order('image_order', { ascending: true });
      
      if (imagesError) throw imagesError;
      
      return { ...gallery, images: images || [] };
    } catch (error) {
      console.error(`Error fetching gallery ${id}:`, error);
      showToast.error('<PERSON>ri detayı yüklenirken bir hata oluştu.');
      throw error;
    }
  };

  // Gallery oluştur
  const createGallery = async (galleryData) => {
    return await createItem(galleryData);
  };

  // Gallery güncelle
  const updateGallery = async (id, galleryData) => {
    return await updateItem(id, galleryData);
  };

  // Gallery sil (image'ları da sil)
  const deleteGallery = async (galleryId) => {
    try {
      // Önce bu gallerinin tüm resimlerini sil
      const { data: images } = await supabase
        .from('gallery_images')
        .select('image_url')
        .eq('album_id', galleryId);

      if (images) {
        const filePaths = images.map(img => 
          img.image_url.substring(img.image_url.lastIndexOf(user.id))
        );
        if (filePaths.length > 0) {
          await supabase.storage.from('gallery-images').remove(filePaths);
        }
      }

      // Sonra gallery'yi sil
      return await deleteItem(galleryId);
    } catch (error) {
      console.error('Error deleting gallery:', error);
      showToast.error('Galeri silinirken bir hata oluştu.');
      throw error;
    }
  };

  // Gallery order güncellemesi (album_order kullanıyor)
  const updateAlbumOrder = async (orderedGalleries) => {
    return await updateItemOrder(orderedGalleries);
  };

  // Image upload
  const uploadImage = async (file, albumId) => {
    if (!user) throw new Error('User not authenticated');
    
    const fileExt = file.name.split('.').pop();
    const fileName = `${user.id}/${albumId}/${Date.now()}.${fileExt}`;

    const { error } = await supabase.storage
      .from('gallery-images')
      .upload(fileName, file);
    if (error) throw error;

    const { data: { publicUrl } } = supabase.storage
      .from('gallery-images')
      .getPublicUrl(fileName);
      
    const { data: maxOrderData } = await supabase
      .from('gallery_images')
      .select('image_order')
      .eq('album_id', albumId)
      .order('image_order', { ascending: false })
      .limit(1)
      .single();

    const newOrder = (maxOrderData?.image_order ?? -1) + 1;

    const { data: newImage, error: insertError } = await supabase
      .from('gallery_images')
      .insert([{
        album_id: albumId,
        image_url: publicUrl,
        image_title: file.name.split('.').slice(0, -1).join('.'),
        image_order: newOrder,
      }])
      .select()
      .single();

    if (insertError) throw insertError;

    showToast.success('Resim başarıyla yüklendi.');
    return newImage;
  };

  // Image detaylarını güncelle
  const updateImageDetails = async (imageId, details) => {
    const { data, error } = await supabase
      .from('gallery_images')
      .update(details)
      .eq('id', imageId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  };

  // Image sil
  const deleteImage = async (imageId, imageUrl) => {
    try {
      // Storage'dan dosyayı sil
      if (imageUrl) {
        const filePath = imageUrl.substring(imageUrl.lastIndexOf(user.id));
        await supabase.storage.from('gallery-images').remove([filePath]);
      }

      // Database'den kaydı sil
      const { error } = await supabase
        .from('gallery_images')
        .delete()
        .eq('id', imageId);
      
      if (error) throw error;
      
      showToast.success('Resim başarıyla silindi.');
    } catch (error) {
      console.error('Error deleting image:', error);
      showToast.error('Resim silinirken bir hata oluştu.');
      throw error;
    }
  };

  // Image sırasını güncelle
  const updateImageOrder = async (albumId, orderedImages) => {
    try {
      const updates = orderedImages.map((image, index) => ({
        id: image.id,
        image_order: index,
      }));

      const { error } = await supabase
        .from('gallery_images')
        .upsert(updates);

      if (error) throw error;
      
      showToast.success('Resim sırası güncellendi.');
    } catch (error) {
      console.error('Error updating image order:', error);
      showToast.error('Resim sırası güncellenirken bir hata oluştu.');
      throw error;
    }
  };

  return {
    galleries,
    loading,
    error,
    getGalleryById,
    createGallery,
    updateGallery,
    deleteGallery,
    updateAlbumOrder,
    uploadImage,
    updateImageDetails,
    deleteImage,
    updateImageOrder
  };
} 