import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import QRCode from 'react-qr-code';
import { 
  FiHome, 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiMapPin, 
  FiBriefcase, 
  FiEdit3, 
  FiSave, 
  FiX, 
  FiCamera, 
  FiInstagram, 
  FiLinkedin, 
  FiGlobe,
  FiArrowLeft,
  FiUpload,
  FiCheck,
  FiMessageSquare,
  FiAtSign,
  FiCheckCircle,
  FiAlertCircle,
  FiInfo,
  FiXCircle,
  FiFileText,
  FiDownload,
  FiShare2,
  FiLock,
  FiEye,
  FiEyeOff,
  FiSettings,
  FiImage
} from 'react-icons/fi';
import { 
  FaFacebook, 
  FaXTwitter, 
  FaYoutube, 
  FaTiktok, 
  FaTelegram, 
  FaSnapchat, 
  FaSpotify, 
  FaReddit, 
  FaPinterest,
  FaWhatsapp,
  FaQrcode
} from 'react-icons/fa6';

const UserProfile = () => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [activeTab, setActiveTab] = useState('user-info');
  const navigate = useNavigate();

  // Form states
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    username: '',
    phone: '',
    location: '',
    profession: '',
    company: '',
    slogan: '',
    avatar_url: '',
    website: '',
    instagram: '',
    linkedin: '',
    facebook: '',
    x_twitter: '',
    youtube: '',
    tiktok: '',
    telegram: '',
    snapchat: '',
    spotify: '',
    reddit: '',
    pinterest: '',
    whatsapp_number: ''
  });

  // Customization states
  const [customizationData, setCustomizationData] = useState({
    theme_color: '#6366f1',
    background_style: 'gradient',
    card_style: 'modern',
    button_style: 'rounded',
    font_style: 'modern',
    layout_style: 'default',
    show_social_icons: true,
    show_contact_info: true,
    show_qr_code: true,
    profile_visibility: 'public'
  });

  // Password change states
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  
  const [passwordChanging, setPasswordChanging] = useState(false);

  const [usernameChecking, setUsernameChecking] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState(null);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    checkUserAndLoadProfile();
  }, []);

  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 4000);
  };

  const AlertComponent = ({ message }) => {
    if (!message.text) return null;

    const getAlertConfig = () => {
      switch (message.type) {
        case 'success':
          return {
            icon: FiCheckCircle,
            className: 'alert-success',
            bgColor: 'rgba(34, 197, 94, 0.1)',
            borderColor: 'rgba(34, 197, 94, 0.3)',
            iconColor: '#22c55e'
          };
        case 'error':
          return {
            icon: FiXCircle,
            className: 'alert-error',
            bgColor: 'rgba(239, 68, 68, 0.1)',
            borderColor: 'rgba(239, 68, 68, 0.3)',
            iconColor: '#ef4444'
          };
        case 'warning':
          return {
            icon: FiAlertCircle,
            className: 'alert-warning',
            bgColor: 'rgba(245, 158, 11, 0.1)',
            borderColor: 'rgba(245, 158, 11, 0.3)',
            iconColor: '#f59e0b'
          };
        case 'info':
        default:
          return {
            icon: FiInfo,
            className: 'alert-info',
            bgColor: 'rgba(59, 130, 246, 0.1)',
            borderColor: 'rgba(59, 130, 246, 0.3)',
            iconColor: '#3b82f6'
          };
      }
    };

    const config = getAlertConfig();
    const IconComponent = config.icon;

    return (
      <div 
        className={`modern-alert ${config.className}`}
        style={{
          background: config.bgColor,
          borderColor: config.borderColor
        }}
      >
        <div className="alert-content">
          <IconComponent 
            size={20} 
            style={{ color: config.iconColor }} 
            className="alert-icon"
          />
          <span className="alert-text">{message.text}</span>
        </div>
        <button 
          className="alert-close"
          onClick={() => setMessage({ type: '', text: '' })}
          style={{ color: config.iconColor }}
        >
          <FiX size={18} />
        </button>
      </div>
    );
  };

  const checkUserAndLoadProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        navigate('/');
        return;
      }

      // Admin kontrolü
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profile?.role === 'admin') {
        navigate('/login');
        return;
      }

      setUser(user);
      setProfile(profile);
      
      if (profile) {
        setFormData({
          full_name: profile.full_name || '',
          email: profile.email || user.email || '',
          username: profile.username || '',
          phone: profile.phone || '',
          location: profile.location || '',
          profession: profile.profession || '',
          company: profile.company || '',
          slogan: profile.slogan || '',
          avatar_url: profile.avatar_url || '',
          website: profile.website || '',
          instagram: profile.instagram || '',
          linkedin: profile.linkedin || '',
          facebook: profile.facebook || '',
          x_twitter: profile.x_twitter || '',
          youtube: profile.youtube || '',
          tiktok: profile.tiktok || '',
          telegram: profile.telegram || '',
          snapchat: profile.snapchat || '',
          spotify: profile.spotify || '',
          reddit: profile.reddit || '',
          pinterest: profile.pinterest || '',
          whatsapp_number: profile.whatsapp_number || ''
        });

        // Özelleştirme verilerini yükle
        setCustomizationData({
          theme_color: profile.theme_color || '#6366f1',
          background_style: profile.background_style || 'gradient',
          card_style: profile.card_style || 'modern',
          button_style: profile.button_style || 'rounded',
          font_style: profile.font_style || 'modern',
          layout_style: profile.layout_style || 'default',
          show_social_icons: profile.show_social_icons !== false,
          show_contact_info: profile.show_contact_info !== false,
          show_qr_code: profile.show_qr_code !== false,
          profile_visibility: profile.profile_visibility || 'public'
        });
      }

    } catch (error) {
      console.error('Error loading profile:', error);
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Username değişirse availability check et
    if (name === 'username') {
      setUsernameAvailable(null);
      if (value && value !== profile?.username) {
        checkUsernameAvailability(value);
      }
    }
  };

  const handleCustomizationChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCustomizationData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handlePasswordUpdate = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      showMessage('error', 'Tüm şifre alanlarını doldurun');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      showMessage('error', 'Yeni şifreler eşleşmiyor');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      showMessage('error', 'Yeni şifre en az 6 karakter olmalıdır');
      return;
    }

    try {
      setPasswordChanging(true);

      // Önce mevcut şifre ile giriş yap (doğrulama için)
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: passwordData.currentPassword
      });

      if (signInError) {
        throw new Error('Mevcut şifre yanlış');
      }

      // Şifre güncelle
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) throw updateError;

      showMessage('success', 'Şifre başarıyla güncellendi');
      
      // Form temizle
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      setShowPasswords({
        current: false,
        new: false,
        confirm: false
      });

    } catch (error) {
      showMessage('error', error.message || 'Şifre güncellenirken hata oluştu');
    } finally {
      setPasswordChanging(false);
    }
  };

  const handleEmailUpdate = async () => {
    try {
      const { error } = await supabase.auth.updateUser({
        email: formData.email
      });

      if (error) throw error;

      showMessage('success', 'E-posta adresi güncellendi. Yeni e-posta adresinizi doğrulamanız gerekiyor.');
    } catch (error) {
      showMessage('error', error.message || 'E-posta güncellenirken hata oluştu');
    }
  };

  const checkUsernameAvailability = async (username) => {
    if (!username || username.length < 3) {
      setUsernameAvailable(null);
      return;
    }

    setUsernameChecking(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .neq('id', user.id);

      if (error) throw error;

      setUsernameAvailable(data.length === 0);
    } catch (error) {
      console.error('Error checking username:', error);
      setUsernameAvailable(null);
    } finally {
      setUsernameChecking(false);
    }
  };

  const handlePhotoUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Dosya boyutu kontrolü (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showMessage('error', 'Dosya boyutu 5MB\'dan küçük olmalı.');
      return;
    }

    // Dosya türü kontrolü
    if (!file.type.startsWith('image/')) {
      showMessage('error', 'Sadece görsel dosyaları yükleyebilirsiniz.');
      return;
    }

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      setFormData(prev => ({
        ...prev,
        avatar_url: publicUrl
      }));

      // Avatar URL'sini veritabanına kaydet
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id);

      if (updateError) {
        console.error('Error updating avatar URL:', updateError);
        showMessage('error', 'Fotoğraf yüklendi ama profil güncellenemedi.');
        return;
      }

      // Profile state'ini güncelle
      setProfile(prev => ({ ...prev, avatar_url: publicUrl }));

      showMessage('success', 'Fotoğraf başarıyla yüklendi ve profil güncellendi!');
    } catch (error) {
      console.error('Error uploading photo:', error);
      showMessage('error', 'Fotoğraf yüklenirken hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setUploading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);
      setMessage({ type: '', text: '' });

      // Username değişti ise ve müsait değilse hata ver
      if (formData.username !== profile?.username && usernameAvailable === false) {
        showMessage('error', 'Bu kullanıcı adı zaten kullanılıyor.');
        setSaving(false);
        return;
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: formData.full_name,
          username: formData.username,
          phone: formData.phone,
          location: formData.location,
          profession: formData.profession,
          company: formData.company,
          slogan: formData.slogan,
          avatar_url: formData.avatar_url,
          website: formData.website,
          instagram: formData.instagram,
          linkedin: formData.linkedin,
          facebook: formData.facebook,
          x_twitter: formData.x_twitter,
          youtube: formData.youtube,
          tiktok: formData.tiktok,
          telegram: formData.telegram,
          snapchat: formData.snapchat,
          spotify: formData.spotify,
          reddit: formData.reddit,
          pinterest: formData.pinterest,
          whatsapp_number: formData.whatsapp_number,
          // Özelleştirme verileri
          theme_color: customizationData.theme_color,
          background_style: customizationData.background_style,
          card_style: customizationData.card_style,
          button_style: customizationData.button_style,
          font_style: customizationData.font_style,
          layout_style: customizationData.layout_style,
          show_social_icons: customizationData.show_social_icons,
          show_contact_info: customizationData.show_contact_info,
          show_qr_code: customizationData.show_qr_code,
          profile_visibility: customizationData.profile_visibility,
          updated_at: new Date()
        })
        .eq('id', user.id);

      if (error) throw error;

      showMessage('success', 'Profil başarıyla güncellendi!');
      
      // Profile state'ini güncelle
      setProfile(prev => ({ ...prev, ...formData, ...customizationData }));
    } catch (error) {
      console.error('Error updating profile:', error);
      showMessage('error', 'Profil güncellenirken hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  // QR kod indirme fonksiyonu
  const handleDownloadQR = () => {
    try {
      // QR kod URL'sini oluşturalım
      const username = formData.username || 'profile';
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=500x500&data=https://zylo.me/${username}`;
      
      // Yeni pencerede QR kod görüntüsünü açalım
      window.open(qrCodeUrl, '_blank');
      
      // Kullanıcıya bilgi verelim
      showMessage('info', 'QR kod açıldı. Resmi kaydetmek için sağ tıklayıp "Resmi Farklı Kaydet" seçeneğini kullanabilirsiniz.');
    } catch (error) {
      console.error("QR kod açma hatası:", error);
      showMessage('error', 'QR kod açılamadı!');
    }
  };
  
  // Profil linkini yeni sayfada açma fonksiyonu
  const openProfileLink = () => {
    const profileUrl = `https://zylo.me/${formData.username}`;
    window.open(profileUrl, '_blank');
  };
  
  // Bağlantı paylaşma fonksiyonu
  const handleShareLink = async () => {
    const profileUrl = `https://zylo.me/${formData.username}`;
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: `${formData.full_name || 'Zylo.me'} Profili`,
          text: `${formData.full_name || 'Zylo.me'} profilimi ziyaret et`,
          url: profileUrl
        });
      } else {
        // Tarayıcı paylaşım API'sini desteklemiyorsa panoya kopyala
        navigator.clipboard.writeText(profileUrl);
        showMessage('success', 'Bağlantı panoya kopyalandı!');
      }
    } catch (error) {
      console.error('Paylaşım hatası:', error);
    }
  };

  if (loading) {
    return (
      <div className="user-profile-loading">
        <div className="loading-spinner"></div>
        <p>Profil yükleniyor...</p>
      </div>
    );
  }

  return (
    <div className="user-profile">
      {/* Header */}
      <header className="user-profile-header">
        <div className="dashboard-container">
          <div className="header-content">
            <button className="back-btn" onClick={() => navigate('/dashboard')}>
              <FiArrowLeft />
              <span>Dashboard'a Dön</span>
            </button>
            
            <div className="header-title">
              <h1>Profil Ayarları</h1>
              <p>
                Profil fotoğrafınızı, unvanınızı, sosyal medya hesaplarınızı ve 
                diğer kişisel bilgilerinizi buradan güncelleyebilirsiniz.
              </p>
              <p>Kişisel bilgilerinizi ve bağlantılarınızı yönetin</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="profile-main">
        <div className="dashboard-container">
          {/* Modern Alert */}
          <AlertComponent message={message} />

          {/* Profile Card */}
          <div className="profile-card">
            {/* Profile Header */}
            <div className="profile-card-header">
              <div className="profile-avatar-section">
                <div className="profile-avatar-large">
                  {formData.avatar_url ? (
                    <img src={formData.avatar_url} alt="Profile" className="avatar-image" />
                  ) : (
                    <div className="avatar-placeholder-large">
                      <FiUser />
                    </div>
                  )}
                  <div className="avatar-edit-icon" onClick={() => !uploading && document.getElementById('avatar-upload').click()}>
                    {uploading ? (
                      <div className="btn-spinner"></div>
                    ) : (
                      <FiEdit3 />
                    )}
                  </div>
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    style={{ display: 'none' }}
                    disabled={uploading}
                  />
                </div>
                <div className="profile-basic-info">
                  <h2>{formData.full_name || 'İsim Belirtilmemiş'}</h2>
                  <p>{formData.profession || 'Meslek Belirtilmemiş'}</p>
                  {formData.slogan && <p className="profile-slogan">"{formData.slogan}"</p>}
                  
                  
                </div>
                
                {/* QR Code Section */}
                {formData.username && (
                  <div className="profile-qr-section">
                    <div className="qr-code-container">
                      <QRCode 
                        value={`https://zylo.me/${formData.username}`} 
                        size={120} 
                        level="H"
                        className="qr-code"
                      />
                    </div>
                    <div className="qr-info">
                      <h4>Profil Bağlantınız</h4>
                      <p className="profile-link" onClick={openProfileLink} style={{ cursor: 'pointer' }}>zylo.me/{formData.username}</p>
                      <div className="qr-actions">
                        <button 
                          className="qr-action-btn" 
                          title="QR Kodu İndir"
                          onClick={handleDownloadQR}
                        >
                          <FiDownload size={16} />
                        </button>
                        <button 
                          className="qr-action-btn" 
                          title="Bağlantıyı Paylaş"
                          onClick={handleShareLink}
                        >
                          <FiShare2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Tabs */}
            <div className="profile-tabs">
              <button 
                className={`tab-btn ${activeTab === 'user-info' ? 'active' : ''}`}
                onClick={() => setActiveTab('user-info')}
              >
                <FiUser />
                <span>Kullanıcı Bilgileri</span>
              </button>
              <button 
                className={`tab-btn ${activeTab === 'profile-info' ? 'active' : ''}`}
                onClick={() => setActiveTab('profile-info')}
              >
                <FiBriefcase />
                <span>Profil Bilgileri</span>
              </button>
              <button 
                className={`tab-btn ${activeTab === 'social-media' ? 'active' : ''}`}
                onClick={() => setActiveTab('social-media')}
              >
                <FiInstagram />
                <span>Sosyal Medya</span>
              </button>
              <button 
                className={`tab-btn ${activeTab === 'contact' ? 'active' : ''}`}
                onClick={() => setActiveTab('contact')}
              >
                <FiMail />
                <span>İletişim</span>
              </button>
              <button 
                className={`tab-btn ${activeTab === 'customization' ? 'active' : ''}`}
                onClick={() => setActiveTab('customization')}
              >
                <FiImage />
                <span>Profilini Özelleştir</span>
              </button>
            </div>

            {/* Tab Content */}
            <div className="profile-content">
              {/* Kullanıcı Bilgileri Tab */}
              {activeTab === 'user-info' && (
                <div className="user-info-section">
                  <div className="user-info-grid">
                    <div className="form-group">
                      <label htmlFor="full_name">Ad Soyad *</label>
                      <input
                        id="full_name"
                        name="full_name"
                        type="text"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        placeholder="Adınız ve soyadınız"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="username">
                        <FiAtSign />
                        Kullanıcı Adı
                      </label>
                      <div className="username-input-wrapper">
                        <input
                          id="username"
                          name="username"
                          type="text"
                          value={formData.username}
                          onChange={handleInputChange}
                          placeholder="kullaniciadi"
                          minLength={3}
                          maxLength={30}
                        />
                        {usernameChecking && <div className="username-checking">Kontrol ediliyor...</div>}
                        {usernameAvailable === true && <FiCheck className="username-available" />}
                        {usernameAvailable === false && <FiX className="username-unavailable" />}
                      </div>
                      {formData.username && formData.username.length >= 3 && (
                        <small className={usernameAvailable === true ? 'success' : usernameAvailable === false ? 'error' : ''}>
                          {usernameAvailable === true && 'Kullanıcı adı müsait ✓'}
                          {usernameAvailable === false && 'Bu kullanıcı adı zaten kullanılıyor ✗'}
                          {usernameAvailable === null && usernameChecking && 'Kontrol ediliyor...'}
                        </small>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="email">E-posta Adresi</label>
                      <div className="email-input-wrapper">
                        <input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                        />
                        <button 
                          type="button"
                          className="email-update-btn"
                          onClick={handleEmailUpdate}
                          title="E-posta güncelle"
                        >
                          <FiEdit3 />
                        </button>
                      </div>
                      <small>E-posta değişikliği için doğrulama gereklidir</small>
                    </div>
                  </div>

                  {/* Şifre Değiştirme Bölümü */}
                  <div className="password-change-section">
                    <h3>
                      <FiLock />
                      Şifre Değiştir
                    </h3>
                    <div className="password-grid">
                      <div className="form-group">
                        <label htmlFor="currentPassword">Mevcut Şifre</label>
                        <div className="password-input-wrapper">
                          <input
                            id="currentPassword"
                            name="currentPassword"
                            type={showPasswords.current ? "text" : "password"}
                            value={passwordData.currentPassword}
                            onChange={handlePasswordChange}
                            placeholder="Mevcut şifrenizi girin"
                          />
                          <button 
                            type="button"
                            className="password-toggle-btn"
                            onClick={() => togglePasswordVisibility('current')}
                          >
                            {showPasswords.current ? <FiEyeOff /> : <FiEye />}
                          </button>
                        </div>
                      </div>

                      <div className="form-group">
                        <label htmlFor="newPassword">Yeni Şifre</label>
                        <div className="password-input-wrapper">
                          <input
                            id="newPassword"
                            name="newPassword"
                            type={showPasswords.new ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={handlePasswordChange}
                            placeholder="Yeni şifrenizi girin"
                            minLength={6}
                          />
                          <button 
                            type="button"
                            className="password-toggle-btn"
                            onClick={() => togglePasswordVisibility('new')}
                          >
                            {showPasswords.new ? <FiEyeOff /> : <FiEye />}
                          </button>
                        </div>
                      </div>

                      <div className="form-group">
                        <label htmlFor="confirmPassword">Yeni Şifre Tekrar</label>
                        <div className="password-input-wrapper">
                          <input
                            id="confirmPassword"
                            name="confirmPassword"
                            type={showPasswords.confirm ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={handlePasswordChange}
                            placeholder="Yeni şifrenizi tekrar girin"
                            minLength={6}
                          />
                          <button 
                            type="button"
                            className="password-toggle-btn"
                            onClick={() => togglePasswordVisibility('confirm')}
                          >
                            {showPasswords.confirm ? <FiEyeOff /> : <FiEye />}
                          </button>
                        </div>
                      </div>
                    </div>

                    <button 
                      type="button"
                      className="password-update-btn"
                      onClick={handlePasswordUpdate}
                      disabled={passwordChanging || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                    >
                      {passwordChanging ? (
                        <>
                          <div className="btn-spinner"></div>
                          Şifre Güncelleniyor...
                        </>
                      ) : (
                        <>
                          <FiLock />
                          Şifreyi Güncelle
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Profil Bilgileri Tab */}
              {activeTab === 'profile-info' && (
                <div className="profile-info-section">
                  <div className="profile-info-grid">
                    <div className="form-group">
                      <label htmlFor="full_name_profile">Ad Soyad</label>
                      <input
                        id="full_name_profile"
                        name="full_name"
                        type="text"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        placeholder="Adınız ve soyadınız"
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="profession">
                        <FiBriefcase />
                        Meslek
                      </label>
                      <input
                        id="profession"
                        name="profession"
                        type="text"
                        value={formData.profession}
                        onChange={handleInputChange}
                        placeholder="Emlak Danışmanı"
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="slogan">
                        <FiMessageSquare />
                        Slogan
                      </label>
                      <input
                        id="slogan"
                        name="slogan"
                        type="text"
                        value={formData.slogan}
                        onChange={handleInputChange}
                        placeholder="Kısa ve etkileyici bir slogan"
                        maxLength={100}
                      />
                      <small>{formData.slogan.length}/100 karakter</small>
                    </div>
                  </div>
                </div>
              )}

              {/* Sosyal Medya Tab */}
              {activeTab === 'social-media' && (
                <div className="social-media-section">
                  <div className="social-section">
                    
                    <div className="social-media-grid">
                      <div className="form-group">
                        <label htmlFor="instagram">
                          <FiInstagram />
                          Instagram
                        </label>
                        <input
                          id="instagram"
                          name="instagram"
                          type="text"
                          value={formData.instagram}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="linkedin">
                          <FiLinkedin />
                          LinkedIn
                        </label>
                        <input
                          id="linkedin"
                          name="linkedin"
                          type="text"
                          value={formData.linkedin}
                          onChange={handleInputChange}
                          placeholder="kullanici-adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="facebook">
                          <FaFacebook />
                          Facebook
                        </label>
                        <input
                          id="facebook"
                          name="facebook"
                          type="text"
                          value={formData.facebook}
                          onChange={handleInputChange}
                          placeholder="kullanici.adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="x_twitter">
                          <FaXTwitter />
                          X
                        </label>
                        <input
                          id="x_twitter"
                          name="x_twitter"
                          type="text"
                          value={formData.x_twitter}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="youtube">
                          <FaYoutube />
                          YouTube
                        </label>
                        <input
                          id="youtube"
                          name="youtube"
                          type="text"
                          value={formData.youtube}
                          onChange={handleInputChange}
                          placeholder="@kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="tiktok">
                          <FaTiktok />
                          TikTok
                        </label>
                        <input
                          id="tiktok"
                          name="tiktok"
                          type="text"
                          value={formData.tiktok}
                          onChange={handleInputChange}
                          placeholder="@kullanici_adi"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="social-section">
                    <div className="social-media-grid">
                      <div className="form-group">
                        <label htmlFor="telegram">
                          <FaTelegram />
                          Telegram
                        </label>
                        <input
                          id="telegram"
                          name="telegram"
                          type="text"
                          value={formData.telegram}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="snapchat">
                          <FaSnapchat />
                          Snapchat
                        </label>
                        <input
                          id="snapchat"
                          name="snapchat"
                          type="text"
                          value={formData.snapchat}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="spotify">
                          <FaSpotify />
                          Spotify
                        </label>
                        <input
                          id="spotify"
                          name="spotify"
                          type="text"
                          value={formData.spotify}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="reddit">
                          <FaReddit />
                          Reddit
                        </label>
                        <input
                          id="reddit"
                          name="reddit"
                          type="text"
                          value={formData.reddit}
                          onChange={handleInputChange}
                          placeholder="u/kullanici_adi"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="pinterest">
                          <FaPinterest />
                          Pinterest
                        </label>
                        <input
                          id="pinterest"
                          name="pinterest"
                          type="text"
                          value={formData.pinterest}
                          onChange={handleInputChange}
                          placeholder="kullanici_adi"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* İletişim Tab */}
              {activeTab === 'contact' && (
                <div className="contact-section">
                  <div className="contact-info-section">
                    <div className="contact-grid">
                      <div className="form-group">
                        <label htmlFor="phone">
                          <FiPhone />
                          Telefon
                        </label>
                        <input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+90 555 123 45 67"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="whatsapp_number">
                          <FaWhatsapp />
                          WhatsApp
                        </label>
                        <input
                          id="whatsapp_number"
                          name="whatsapp_number"
                          type="tel"
                          value={formData.whatsapp_number}
                          onChange={handleInputChange}
                          placeholder="+90 555 123 45 67"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="location">
                          <FiMapPin />
                          Konum
                        </label>
                        <input
                          id="location"
                          name="location"
                          type="text"
                          value={formData.location}
                          onChange={handleInputChange}
                          placeholder="Google Maps Linki"
                        />
                      </div>

                      
                    </div>
                  </div>
                </div>
              )}

              {/* Profilini Özelleştir Tab */}
              {activeTab === 'customization' && (
                <div className="customization-section">
                  <div className="customization-content">
                    
                    {/* Tema Rengi */}
                    <div className="customization-group">
                      <h3>
                        <FiImage />
                        Tema Rengi
                      </h3>
                      <div className="color-options">
                        <div className="color-grid">
                          {[
                            { name: 'İndigo', value: '#6366f1' },
                            { name: 'Mavi', value: '#3b82f6' },
                            { name: 'Mor', value: '#8b5cf6' },
                            { name: 'Pembe', value: '#ec4899' },
                            { name: 'Kırmızı', value: '#ef4444' },
                            { name: 'Turuncu', value: '#f97316' },
                            { name: 'Yeşil', value: '#10b981' },
                            { name: 'Camgöbeği', value: '#06b6d4' }
                          ].map((color) => (
                            <label key={color.value} className="color-option">
                              <input
                                type="radio"
                                name="theme_color"
                                value={color.value}
                                checked={customizationData.theme_color === color.value}
                                onChange={handleCustomizationChange}
                              />
                              <div 
                                className="color-preview"
                                style={{ backgroundColor: color.value }}
                              ></div>
                              <span>{color.name}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Arka Plan Stili */}
                    <div className="customization-group">
                      <h3>
                        <FiSettings />
                        Arka Plan Stili
                      </h3>
                      <div className="style-options">
                        <label className="style-option">
                          <input
                            type="radio"
                            name="background_style"
                            value="gradient"
                            checked={customizationData.background_style === 'gradient'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview gradient-preview"></div>
                          <span>Gradyan</span>
                        </label>
                        <label className="style-option">
                          <input
                            type="radio"
                            name="background_style"
                            value="solid"
                            checked={customizationData.background_style === 'solid'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview solid-preview"></div>
                          <span>Düz Renk</span>
                        </label>
                        <label className="style-option">
                          <input
                            type="radio"
                            name="background_style"
                            value="pattern"
                            checked={customizationData.background_style === 'pattern'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview pattern-preview"></div>
                          <span>Desenli</span>
                        </label>
                      </div>
                    </div>

                    {/* Kart Stili */}
                    <div className="customization-group">
                      <h3>
                        <FiSettings />
                        Kart Stili
                      </h3>
                      <div className="style-options">
                        <label className="style-option">
                          <input
                            type="radio"
                            name="card_style"
                            value="modern"
                            checked={customizationData.card_style === 'modern'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview card-modern"></div>
                          <span>Modern</span>
                        </label>
                        <label className="style-option">
                          <input
                            type="radio"
                            name="card_style"
                            value="classic"
                            checked={customizationData.card_style === 'classic'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview card-classic"></div>
                          <span>Klasik</span>
                        </label>
                        <label className="style-option">
                          <input
                            type="radio"
                            name="card_style"
                            value="minimal"
                            checked={customizationData.card_style === 'minimal'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="style-preview card-minimal"></div>
                          <span>Minimal</span>
                        </label>
                      </div>
                    </div>

                    {/* Görünürlük Ayarları */}
                    <div className="customization-group">
                      <h3>
                        <FiEye />
                        Görünürlük Ayarları
                      </h3>
                      <div className="visibility-options">
                        <label className="checkbox-option">
                          <input
                            type="checkbox"
                            name="show_social_icons"
                            checked={customizationData.show_social_icons}
                            onChange={handleCustomizationChange}
                          />
                          <span className="checkmark"></span>
                          <span>Sosyal medya ikonlarını göster</span>
                        </label>
                        
                        <label className="checkbox-option">
                          <input
                            type="checkbox"
                            name="show_contact_info"
                            checked={customizationData.show_contact_info}
                            onChange={handleCustomizationChange}
                          />
                          <span className="checkmark"></span>
                          <span>İletişim bilgilerini göster</span>
                        </label>
                        
                        <label className="checkbox-option">
                          <input
                            type="checkbox"
                            name="show_qr_code"
                            checked={customizationData.show_qr_code}
                            onChange={handleCustomizationChange}
                          />
                          <span className="checkmark"></span>
                          <span>QR kodu göster</span>
                        </label>
                      </div>
                    </div>

                    {/* Profil Gizliliği */}
                    <div className="customization-group">
                      <h3>
                        <FiLock />
                        Profil Gizliliği
                      </h3>
                      <div className="privacy-options">
                        <label className="privacy-option">
                          <input
                            type="radio"
                            name="profile_visibility"
                            value="public"
                            checked={customizationData.profile_visibility === 'public'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="privacy-info">
                            <span className="privacy-title">Herkese Açık</span>
                            <span className="privacy-desc">Profiliniz herkes tarafından görülebilir</span>
                          </div>
                        </label>
                        
                        <label className="privacy-option">
                          <input
                            type="radio"
                            name="profile_visibility"
                            value="private"
                            checked={customizationData.profile_visibility === 'private'}
                            onChange={handleCustomizationChange}
                          />
                          <div className="privacy-info">
                            <span className="privacy-title">Gizli</span>
                            <span className="privacy-desc">Sadece doğrudan bağlantı ile erişilebilir</span>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>

            {/* Genel Kaydet Butonu */}
            <div className="form-actions">
              <button 
                className="save-btn"
                onClick={handleSaveProfile}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <div className="btn-spinner"></div>
                    Kaydediliyor...
                  </>
                ) : (
                  <>
                    <FiSave />
                    Değişiklikleri Kaydet
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserProfile; 