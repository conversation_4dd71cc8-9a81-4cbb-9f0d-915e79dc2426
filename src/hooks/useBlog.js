import { useState, useCallback } from 'react';
import { useBaseResource } from './base/useBaseResource';
import { blogConfig } from './base/useResourceConfigs';
import { supabase } from '../supabaseClient';
import { useAuth } from './useAuth';
import { v4 as uuidv4 } from 'uuid';
import showToast from '../utils/toast';

export function useBlog() {
  const { user } = useAuth();
  const [categories, setCategories] = useState([]);
  
  const {
    items: posts,
    loading,
    error,
    createItem,
    updateItem,
    deleteItem,
    getItemById: baseGetItemById,
    updateItemOrder,
    uploadFile
  } = useBaseResource(blogConfig);

  // Blog posts listesini getir (relations ile)
  const fetchPosts = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          blog_post_categories(
            blog_categories(*)
          )
        `)
        .eq('user_id', user.id)
        .order('display_order', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Blog posts yüklenirken hata:', error);
      showToast.error('Blog gönderileri yüklenirken bir hata oluştu.');
      throw error;
    }
  }, [user]);

  // Blog kategorilerini getir
  const fetchCategories = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('*')
        .eq('user_id', user.id)
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Blog kategorileri yüklenirken hata:', error);
      setError(error.message);
      showToast.error('Blog kategorileri yüklenirken bir hata oluştu.');
    }
  }, [user]);

  // Tekil blog post getir (relations ile)
  const fetchPostById = useCallback(async (postId) => {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          blog_post_categories(
            blog_categories(*)
          )
        `)
        .eq('id', postId)
        .eq('user_id', user.id)
        .single();

      if (error) throw error;
      
      // Categories'i düz array'e çevir
      const categories = data.blog_post_categories?.map(bpc => bpc.blog_categories) || [];
      return { ...data, categories };
    } catch (error) {
      console.error('Blog post yüklenirken hata:', error);
      showToast.error('Blog gönderisi yüklenirken bir hata oluştu.');
      throw error;
    }
  }, [user]);

  // Blog post oluştur
  const createPost = async (postData, featuredImageFile) => {
    try {
      const files = featuredImageFile ? { featured_image: featuredImageFile } : {};
      const categoryIds = postData.categoryIds;
      delete postData.categoryIds; // createItem'e gitmemesi için sil
      
      const newPost = await createItem(postData, files);

      // Kategorileri bağla
      if (categoryIds && categoryIds.length > 0) {
        await linkCategoriesToPost(newPost.id, categoryIds);
      }

      return newPost;
    } catch (error) {
      console.error('Blog post oluşturulurken hata:', error);
      throw error;
    }
  };

  // Blog post güncelle
  const updatePost = async (postId, postData, featuredImageFile) => {
    try {
      const files = featuredImageFile ? { featured_image: featuredImageFile } : {};
      const categoryIds = postData.categoryIds;
      delete postData.categoryIds; // updateItem'e gitmemesi için sil

      const updatedPost = await updateItem(postId, postData, files);

      // Kategorileri güncelle
      if (categoryIds !== undefined) {
        // Önce mevcut kategorileri sil
        await supabase
          .from('blog_post_categories')
          .delete()
          .eq('post_id', postId);

        // Yeni kategorileri bağla
        if (categoryIds.length > 0) {
          await linkCategoriesToPost(postId, categoryIds);
        }
      }

      return updatedPost;
    } catch (error) {
      console.error('Blog post güncellenirken hata:', error);
      throw error;
    }
  };

  // Blog post sil
  const deletePost = async (postId) => {
    try {
      // İlişkili kategorileri sil
      await supabase
        .from('blog_post_categories')
        .delete()
        .eq('post_id', postId);

      return await deleteItem(postId);
    } catch (error) {
      console.error('Blog post silinirken hata:', error);
      throw error;
    }
  };

  // Blog kategori oluştur
  const createCategory = async (categoryData) => {
    if (!user) throw new Error('Kullanıcı oturumu gerekli');

    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .insert([{
          ...categoryData,
          user_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      await fetchCategories();
      showToast.success('Blog kategorisi başarıyla oluşturuldu.');
      return data;
    } catch (error) {
      console.error('Blog kategori oluşturulurken hata:', error);
      showToast.error('Blog kategorisi oluşturulurken bir hata oluştu.');
      throw error;
    }
  };

  // Blog kategori güncelle
  const updateCategory = async (categoryId, categoryData) => {
    if (!user) throw new Error('Kullanıcı oturumu gerekli');

    try {
      const { data, error } = await supabase
        .from('blog_categories')
        .update({
          ...categoryData,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;

      await fetchCategories();
      showToast.success('Blog kategorisi başarıyla güncellendi.');
      return data;
    } catch (error) {
      console.error('Blog kategori güncellenirken hata:', error);
      showToast.error('Blog kategorisi güncellenirken bir hata oluştu.');
      throw error;
    }
  };

  // Blog kategori sil
  const deleteCategory = async (categoryId) => {
    if (!user) throw new Error('Kullanıcı oturumu gerekli');

    try {
      // Önce bu kategoriye bağlı post'ları kontrol et
      const { data: linkedPosts, error: checkError } = await supabase
        .from('blog_post_categories')
        .select('post_id')
        .eq('category_id', categoryId);

      if (checkError) throw checkError;

      if (linkedPosts && linkedPosts.length > 0) {
        showToast.error('Bu kategoriye bağlı blog gönderileri var. Önce onları silin veya kategorisini değiştirin.');
        return;
      }

      const { error } = await supabase
        .from('blog_categories')
        .delete()
        .eq('id', categoryId)
        .eq('user_id', user.id);

      if (error) throw error;

      await fetchCategories();
      showToast.success('Blog kategorisi başarıyla silindi.');
    } catch (error) {
      console.error('Blog kategori silinirken hata:', error);
      showToast.error('Blog kategorisi silinirken bir hata oluştu.');
      throw error;
    }
  };

  // Blog image upload
  const uploadImage = async (file, postId = null) => {
    try {
      return await uploadFile(file, 'content');
    } catch (error) {
      console.error('Blog resim yüklenirken hata:', error);
      showToast.error('Resim yüklenirken bir hata oluştu.');
      throw error;
    }
  };

  // Kategorileri post'a bağla
  const linkCategoriesToPost = async (postId, categoryIds) => {
    try {
      const links = categoryIds.map(categoryId => ({
        post_id: postId,
        category_id: categoryId
      }));

      const { error } = await supabase
        .from('blog_post_categories')
        .insert(links);

      if (error) throw error;
    } catch (error) {
      console.error('Kategoriler bağlanırken hata:', error);
      throw error;
    }
  };

  return {
    posts,
    categories,
    loading,
    error,
    fetchPosts,
    fetchCategories,
    fetchPostById,
    createPost,
    updatePost,
    deletePost,
    createCategory,
    updateCategory,
    deleteCategory,
    uploadImage,
    updateItemOrder,
  };
} 