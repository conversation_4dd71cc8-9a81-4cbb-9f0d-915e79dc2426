-- Renaming portfolio_items to link_items and adjusting schema as requested.

-- 1. Rename the table
ALTER TABLE "public"."portfolio_items" RENAME TO "link_items";

-- 2. Rename the primary key constraint. 
-- The name 'portfolio_items_pkey' is the default for a table named 'portfolio_items'.
-- If your key has a different name, this will need to be adjusted.
ALTER INDEX "portfolio_items_pkey" RENAME TO "link_items_pkey";

-- 3. Drop the 'description' column as it's no longer needed.
ALTER TABLE "public"."link_items" DROP COLUMN IF EXISTS "description";

-- 4. Make the 'link' column mandatory (NOT NULL).
-- It might be nullable from the previous migration.
ALTER TABLE "public"."link_items" ALTER COLUMN "link" SET NOT NULL;

-- 5. Add a check to ensure the link is not an empty string.
ALTER TABLE "public"."link_items" ADD CONSTRAINT "link_not_empty" CHECK (link <> '');

-- RLS policies are tied to the table and are automatically carried over with the rename.
-- No action needed for policies unless they need to be renamed for clarity, which is not critical.
