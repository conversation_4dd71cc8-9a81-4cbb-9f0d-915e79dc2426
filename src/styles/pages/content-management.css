/* Modern Minimal Content Management Styles */

.content-management {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

/* Modern Header */
.content-management-header {
  background: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.header-action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.25);
}

.header-right {
  display: flex;
  align-items: center;
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(15px);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.welcome-text {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 400;
}

.user-name {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 600;
}

.logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 0.5rem;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .header-center {
    display: none;
  }
  
  .header-container {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .header-left,
  .header-right {
    justify-content: center;
  }
  
  .user-welcome {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .header-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .user-info {
    text-align: center;
  }
  
  .logout-btn {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

.header-title h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.header-title p {
  margin: 0.25rem 0 0 0;
  color: var(--text-muted);
  font-size: 0.8125rem;
  font-weight: 400;
}

.header-info-box {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 10px;
  color: var(--text-muted);
  font-size: 0.8rem;
  font-weight: 400;
  margin-left: 1.5rem;
  transition: all 0.3s ease;
}

.header-info-box:hover {
  border-color: rgba(102, 126, 234, 0.4);
  color: var(--text-primary);
}

.header-info-box svg {
  color: #667eea;
  flex-shrink: 0;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Modern Layout */
.content-management-layout {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  gap: 2rem;
  padding: 2rem;
}

/* Minimal Sidebar */
.content-sidebar {
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(31, 41, 55, 0.9) 50%,
    rgba(55, 65, 81, 0.85) 100%);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 1rem;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  position: sticky;
  top: 120px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Zarif Scrollbar Stilleri */
.content-sidebar::-webkit-scrollbar {
  width: 8px;
}

.content-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  margin: 8px 0;
}

.content-sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.6) 0%, 
    rgba(118, 75, 162, 0.6) 100%);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.content-sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.8) 0%, 
    rgba(118, 75, 162, 0.8) 100%);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.4);
}

.content-sidebar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 1) 0%, 
    rgba(118, 75, 162, 1) 100%);
}

/* Firefox için scrollbar */
.content-sidebar {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.6) rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 18px;
  margin-bottom: 1.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  width: 100%;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
}

.sidebar-header:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 15px 40px rgba(102, 126, 234, 0.5),
    0 8px 20px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.sidebar-header:hover::after {
  transform: rotate(45deg) translateX(100%);
}

.sidebar-header.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 10px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-item-wrapper {
  position: relative;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.875rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  user-select: none;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  /* Buton olmadığı için eklenen özellikler */
  outline: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.sidebar-nav-item:hover, .sidebar-nav-item:focus {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.15);
  outline: none;
}

.sidebar-nav-item.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #8b8cf5;
  box-shadow: 
    0 6px 20px rgba(102, 126, 234, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-nav-item.disabled {
  opacity: 0.4;
}

.sidebar-nav-item.disabled .nav-item-title {
  text-decoration: line-through;
}

.sidebar-nav-item.dragging {
  transform: rotate(2deg) scale(1.02);
  z-index: 10; /* Sürükleme sırasında diğer öğelerin üzerinde görünmesi için */
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  background: white;
  border-color: #667eea;
}

.nav-item-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.nav-item-drag-handle {
  opacity: 0.3;
  cursor: grab;
  display: flex;
  align-items: center;
  padding: 0.25rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.nav-item-drag-handle:hover {
  opacity: 0.6;
  background: rgba(0, 0, 0, 0.04);
}

.drag-dots {
  display: grid;
  grid-template-columns: repeat(2, 3px);
  gap: 2px;
}

.drag-dots span {
  width: 3px;
  height: 3px;
  background: currentColor;
  border-radius: 50%;
}

.nav-item-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.2) 0%, 
    rgba(118, 75, 162, 0.15) 100%);
  border-radius: 10px;
  transition: all 0.3s ease;
  color: #8b8cf5;
  box-shadow: 
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  font-size: 1.1rem;
}

.sidebar-nav-item.active .nav-item-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 
    0 6px 15px rgba(102, 126, 234, 0.3),
    0 2px 5px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.sidebar-nav-item:hover .nav-item-icon {
  transform: scale(1.1) rotate(-5deg);
}

.nav-item-content {
  flex: 1;
  min-width: 0;
}

.nav-item-title {
  display: block;
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.01em;
  transition: all 0.3s ease;
}

.nav-item-status {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
  font-weight: 400;
  transition: all 0.3s ease;
}

.sidebar-nav-item:hover .nav-item-title {
  color: white;
  transform: translateY(-1px);
}

.sidebar-nav-item:hover .nav-item-status {
  color: rgba(255, 255, 255, 0.7);
}

.nav-item-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* Modern Switch */
.nav-item-switch {
  position: relative;
  width: 40px;
  height: 22px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 11px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  flex-shrink: 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item-switch.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 
    0 4px 15px rgba(102, 126, 234, 0.4),
    0 2px 5px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
}

.switch-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.nav-item-switch.active .switch-slider {
  transform: translateX(18px);
  box-shadow: 
    0 2px 6px rgba(0, 0, 0, 0.2),
    0 0 0 2px rgba(255, 255, 255, 0.2);
}

.sidebar-nav-item:hover .nav-item-switch:not(.active) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.nav-item-submenu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--text-muted);
  transition: all 0.2s ease;
  border-radius: 6px;
}

.nav-item-submenu-toggle:hover {
  background: rgba(0, 0, 0, 0.04);
  color: var(--text-secondary);
}

.nav-item-submenu-toggle.open {
  transform: rotate(180deg);
  color: #667eea;
}

/* Modern Submenu */
.nav-submenu {
  margin-top: 0.5rem;
  margin-left: 2.5rem;
  border-left: 2px solid rgba(0, 0, 0, 0.06);
  padding-left: 1rem;
}

.nav-submenu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem 0.75rem;
  background: none;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  margin-bottom: 0.25rem;
  font-weight: 400;
}

.nav-submenu-item:hover {
  background: rgba(0, 0, 0, 0.04);
  color: var(--text-primary);
}

.nav-submenu-item.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-weight: 500;
}

/* Drop Indicators */
.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
  border-radius: 1px;
  z-index: 100;
  box-shadow: 0 0 8px rgba(102, 126, 234, 0.6);
}

.drop-indicator.drop-before {
  top: -1px;
}

.drop-indicator.drop-after {
  bottom: -1px;
}

/* Modern Main Content */
.content-main {
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(31, 41, 55, 0.9) 50%,
    rgba(55, 65, 81, 0.85) 100%);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 24px;
  padding: 0;
  min-height: 600px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.content-main::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(102, 126, 234, 0.15),
    transparent 60%
  );
  pointer-events: none;
}

.content-section {
  background: transparent;
  border: none;
  border-radius: 0;
  overflow: visible;
  margin-bottom: 2rem;
}

.content-section-header {
  padding: 0 0 1.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  margin-bottom: 2rem;
  position: relative;
}

.content-section-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 1px;
}

.content-section-header h2 {
  margin: 0;
  color: white;
  font-size: 1.75rem;
  font-weight: 700;
  letter-spacing: -0.03em;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.content-section-header p {
  margin: 0.75rem 0 0 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9375rem;
  font-weight: 400;
  line-height: 1.5;
}

.content-section-body {
  padding: 0;
}

/* Modern Disabled State */
.disabled-section {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px dashed rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.disabled-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.disabled-section h4 {
  margin: 0 0 0.75rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.disabled-section p {
  margin: 0 0 2rem 0;
  font-size: 0.9375rem;
  opacity: 0.8;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.enable-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.enable-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
}

.enable-btn:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 15px 40px rgba(102, 126, 234, 0.5),
    0 8px 20px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.enable-btn:hover::after {
  transform: rotate(45deg) translateX(100%);
}

/* Modern Actions Bar */
.content-actions {
  position: sticky;
  bottom: 2rem;
  background: rgba(17, 24, 39, 0.85);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 1.25rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 1.25rem;
  margin-top: 0;
  border-radius: 20px;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.4),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  z-index: 10;
}

.save-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 8px 25px rgba(16, 185, 129, 0.4),
    0 4px 10px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.save-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 15px 40px rgba(16, 185, 129, 0.5),
    0 8px 20px rgba(16, 185, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.save-btn:hover:not(:disabled)::after {
  transform: rotate(45deg) translateX(100%);
}

.save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 
    0 4px 15px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.6) 0%, rgba(5, 150, 105, 0.6) 100%);
}

/* Loading State */
.content-management-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--text-secondary);
  background: var(--bg-primary);
}

.content-management-loading p {
  margin-top: 1rem;
  font-size: 0.875rem;
  font-weight: 400;
}

/* Modern Overview Tab Styles */
.overview-content {
  padding: 1rem;
  max-width: 1100px;
  margin: 0 auto;
}

.overview-header {
  margin-bottom: 3rem;
  position: relative;
}

.overview-header::after {
  content: '';
  position: absolute;
  bottom: -1.5rem;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, transparent);
  border-radius: 3px;
}

.overview-title h2 {
  margin: 0 0 0.75rem 0;
  color: white;
  font-size: 2.25rem;
  font-weight: 700;
  letter-spacing: -0.03em;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.overview-title p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(31, 41, 55, 0.9) 50%,
    rgba(55, 65, 81, 0.85) 100%);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(102, 126, 234, 0.15),
    transparent 60%
  );
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(102, 126, 234, 0.7);
}

.stat-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 
    0 15px 40px rgba(102, 126, 234, 0.6),
    0 8px 20px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 0.375rem 0;
  color: #ffffff;
  font-size: 2rem;
  font-weight: 800;
  letter-spacing: -0.03em;
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.stat-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
}

.stat-desc {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-content h3 {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.stat-card:hover .stat-content p,
.stat-card:hover .stat-desc {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

/* Section Headers */
.section-header {
  margin-bottom: 1.75rem;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 0;
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, #667eea, transparent);
  border-radius: 2px;
}

.section-header h3 {
  margin: 0 0 0.5rem 0;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9375rem;
  font-weight: 400;
  line-height: 1.5;
}

/* Recent Content Section */
.recent-content-section {
  margin-bottom: 3rem;
}

.recent-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.recent-item-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.recent-item-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.recent-item-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.recent-item-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b8cf5;
  transition: all 0.3s ease;
}

.recent-item-card:hover .recent-item-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.recent-item-info {
  flex: 1;
  min-width: 0;
}

.recent-item-info h4 {
  margin: 0 0 0.25rem 0;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  letter-spacing: -0.01em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-item-tab {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8125rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  display: inline-block;
}

.recent-item-content {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  line-height: 1.5;
}

.recent-item-content p {
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  border: 1px dashed rgba(255, 255, 255, 0.1);
  grid-column: 1 / -1;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: #8b8cf5;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.25rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9375rem;
}

/* Quick Actions Section */
.quick-actions-section {
  margin-bottom: 2rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.quick-action-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-align: left;
  width: 100%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.6) 50%, 
    transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.quick-action-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.quick-action-card:hover::before {
  transform: translateX(0);
}

.quick-action-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b8cf5;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.quick-action-card:hover .quick-action-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.quick-action-content {
  flex: 1;
  min-width: 0;
}

.quick-action-content h3 {
  margin: 0 0 0.375rem 0;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  letter-spacing: -0.01em;
  transition: all 0.3s ease;
}

.quick-action-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.quick-action-card:hover .quick-action-content h3 {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.quick-action-card:hover .quick-action-content p {
  color: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
  
  .recent-items-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
  }
  
  .stat-content h3 {
    font-size: 1.75rem;
  }
  
  .overview-title h2 {
    font-size: 1.75rem;
  }
  
  .section-header h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.25rem;
  }
  
  .recent-item-card,
  .quick-action-card {
    padding: 1.25rem;
  }
  
  .overview-title h2 {
    font-size: 1.5rem;
  }
  
  .overview-title p {
    font-size: 1rem;
  }
  
  .empty-state {
    padding: 1rem;
  }
}

/* Animasyon ve Geçiş Efektleri */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

@keyframes shine {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.recent-item-card {
  animation: fadeIn 0.5s ease-out forwards;
  opacity: 0;
}

.section-icon, .header-icon {
  animation: pulse 2s infinite;
  margin-right: 8px;
}

.action-arrow {
  margin-left: auto;
  color: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(-10px);
}

.quick-action-card:hover .action-arrow {
  opacity: 1;
  transform: translateX(0);
  color: rgba(255, 255, 255, 0.9);
}

.stat-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  background-size: 200% 100%;
  animation: shine 3s infinite linear;
  top: 0;
  left: 0;
  border-radius: inherit;
  pointer-events: none;
}

/* Gelişmiş hover efektleri */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.6) 50%, 
    transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 1;
}

.stat-card:hover::before {
  transform: translateX(0);
}

/* Gelişmiş kart efektleri */
.stat-card, .recent-item-card, .quick-action-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stat-card:hover .stat-icon {
  animation: pulse 1.5s infinite;
}

/* Blog Editörü Stilleri */
.blog-add-page .content-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  margin-bottom: 2rem;
  border-radius: 16px;
  padding: 1.5rem;
}

.blog-editor-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(15px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.blog-editor-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.3) 50%, 
    transparent 100%);
  z-index: 1;
}

.blog-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.06) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 18px;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

.form-input {
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-textarea {
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-upload-area {
  position: relative;
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-upload-label:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.upload-icon {
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.file-upload-label:hover .upload-icon {
  color: #3b82f6;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.upload-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.upload-subtitle {
  color: var(--text-muted);
  font-size: 0.75rem;
}

.image-preview {
  margin-top: 1rem;
  border-radius: 12px;
  overflow: hidden;
  max-width: 200px;
}

.image-preview img {
  width: 100%;
  height: auto;
  display: block;
}

.editor-wrapper {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  overflow: hidden;
  min-height: 450px;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.editor-wrapper:hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(102, 126, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.editor-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.5) 50%, 
    transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.editor-wrapper:focus-within::before {
  opacity: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.primary {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.9) 0%, 
    rgba(118, 75, 162, 0.9) 100%);
  color: white;
  border: 1px solid rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.action-button.primary::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
}

.action-button.primary:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 12px 35px rgba(102, 126, 234, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 1) 0%, 
    rgba(118, 75, 162, 1) 100%);
}

.action-button.primary:hover:not(:disabled)::before {
  transform: rotate(15deg) scale(1.5);
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  padding: 0.875rem 1.25rem;
  min-width: auto;
}

.action-button.secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.action-button.outline {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.875rem 1.5rem;
}

.action-button.outline:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ReactQuill Özel Stilleri */
.editor-wrapper .ql-container,
.blog-editor-wrapper .ql-container {
  background: rgba(255, 255, 255, 0.03);
  border: none;
  border-radius: 0 0 16px 16px;
  color: var(--text-primary);
  font-size: 0.95rem;
  line-height: 1.7;
  min-height: 350px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.editor-wrapper .ql-toolbar,
.blog-editor-wrapper .ql-toolbar {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  border: none;
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding: 1rem 1.5rem;
  backdrop-filter: blur(5px);
}

.editor-wrapper .ql-editor,
.blog-editor-wrapper .ql-editor {
  color: var(--text-primary);
  padding: 2rem;
  font-size: 0.95rem;
  line-height: 1.7;
  background: transparent;
}

.editor-wrapper .ql-editor.ql-blank::before,
.blog-editor-wrapper .ql-editor.ql-blank::before {
  color: var(--text-muted);
  font-style: normal;
  opacity: 0.7;
  content: 'Blog içeriğinizi buraya yazın...';
  font-size: 0.95rem;
}

.editor-wrapper .ql-snow .ql-stroke,
.blog-editor-wrapper .ql-snow .ql-stroke {
  stroke: var(--text-secondary);
  transition: stroke 0.3s ease;
}

.editor-wrapper .ql-snow .ql-fill,
.blog-editor-wrapper .ql-snow .ql-fill {
  fill: var(--text-secondary);
  transition: fill 0.3s ease;
}

.editor-wrapper .ql-snow .ql-picker-label,
.blog-editor-wrapper .ql-snow .ql-picker-label {
  color: var(--text-secondary);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  transition: all 0.3s ease;
}

.editor-wrapper .ql-snow .ql-picker-label:hover,
.blog-editor-wrapper .ql-snow .ql-picker-label:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.editor-wrapper .ql-snow .ql-picker-options,
.blog-editor-wrapper .ql-snow .ql-picker-options {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.editor-wrapper .ql-snow .ql-picker-item:hover,
.blog-editor-wrapper .ql-snow .ql-picker-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* Toolbar butonları */
.editor-wrapper .ql-snow .ql-toolbar button,
.blog-editor-wrapper .ql-snow .ql-toolbar button {
  border-radius: 6px;
  padding: 0.4rem;
  transition: all 0.3s ease;
}

.editor-wrapper .ql-snow .ql-toolbar button:hover,
.blog-editor-wrapper .ql-snow .ql-toolbar button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.editor-wrapper .ql-snow .ql-toolbar button.ql-active,
.blog-editor-wrapper .ql-snow .ql-toolbar button.ql-active {
  background: rgba(102, 126, 234, 0.2);
  color: #8b8cf5;
}

/* Editor içerik stilleri */
.editor-wrapper .ql-editor h1,
.blog-editor-wrapper .ql-editor h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: var(--text-primary);
}

.editor-wrapper .ql-editor h2,
.blog-editor-wrapper .ql-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: var(--text-primary);
}

.editor-wrapper .ql-editor h3,
.blog-editor-wrapper .ql-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: var(--text-primary);
}

.editor-wrapper .ql-editor p,
.blog-editor-wrapper .ql-editor p {
  margin: 0.75rem 0;
  color: var(--text-primary);
}

.editor-wrapper .ql-editor blockquote,
.blog-editor-wrapper .ql-editor blockquote {
  border-left: 4px solid rgba(102, 126, 234, 0.5);
  padding-left: 1rem;
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0 8px 8px 0;
}

.editor-wrapper .ql-editor ul, .editor-wrapper .ql-editor ol,
.blog-editor-wrapper .ql-editor ul, .blog-editor-wrapper .ql-editor ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

/* Focus durumu */
.editor-wrapper:focus-within .ql-toolbar,
.blog-editor-wrapper:focus-within .ql-toolbar {
  border-bottom-color: rgba(102, 126, 234, 0.3);
}

/* Blog Editörü Responsive */
@media (max-width: 768px) {
  .blog-editor-container {
    padding: 1rem;
  }
  
  .form-section {
    padding: 1rem;
  }
  
  .action-group {
    flex-direction: column;
    width: 100%;
  }
  
  .action-button {
    width: 100%;
  }
}

/* Blog Editörü Ana Alan Stilleri & Genel İçerik Alanı */
.content-main-area {
  padding: 2rem;
  height: 100%;
}

/* ==========================================================================
   Content Header v3 - Clean & Minimal 
   ========================================================================== */

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.content-header:hover {
  border-color: rgba(255, 255, 255, 0.12);
  background: rgba(255, 255, 255, 0.05);
}

.content-header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: rgba(102, 126, 234, 0.9);
  transition: all 0.3s ease;
}

.content-icon:hover {
  background: rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.content-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.3;
}

.content-title p {
  margin: 0.375rem 0 0 0;
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.4;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  justify-content: space-between;
}

.content-body {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  min-height: 400px;
  backdrop-filter: blur(10px);
  padding: 2rem;
}

/* Form Row Stili */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .content-main-area {
    padding: 1rem;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.25rem;
    padding: 1.25rem;
  }
  
  .content-header-info {
    gap: 0.875rem;
  }
  
  .content-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
  }
  
  .content-title h2 {
    font-size: 1.25rem;
  }
  
  .content-title p {
    font-size: 0.8125rem;
  }
  
  .content-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .list-page-container .search-container {
    width: 100%;
    order: 2;
  }
  
  .action-button.primary {
    order: 1;
    flex-grow: 1; /* Make button take available space */
  }
}

/* ReactQuill İçin Özel Container */
.blog-editor-wrapper {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
}

/* Empty State Stilleri */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  min-height: 300px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--text-muted);
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 2rem 0;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.add-first-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4);
}

.add-first-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

/* Modern Blog Editor Styles */
.modern-blog-editor {
  min-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* Header */
.blog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(31, 41, 55, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  padding: 0.5rem 1rem;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 10px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateX(-2px);
}

.header-info h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.header-info p {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.draft-btn, .publish-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  justify-content: center;
}

.draft-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.draft-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.publish-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4);
}

.publish-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

.draft-btn:disabled, .publish-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.spinner-sm {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Main Content */
.blog-main {
  flex: 1;
  overflow: hidden;
  padding: 2rem;
}

.blog-form {
  height: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  height: 100%;
}

/* Content Column */
.content-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 0;
}

.title-input {
  width: 100%;
  padding: 1rem 0;
  background: transparent;
  border: none;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  placeholder-color: var(--text-muted);
  outline: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: border-color 0.3s ease;
}

.title-input:focus {
  border-bottom-color: #3b82f6;
}

.title-input::placeholder {
  color: var(--text-muted);
  opacity: 0.6;
}

.excerpt-input {
  width: 100%;
  padding: 0.75rem 0;
  background: transparent;
  border: none;
  font-size: 1rem;
  color: var(--text-secondary);
  placeholder-color: var(--text-muted);
  outline: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  resize: none;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.excerpt-input:focus {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.excerpt-input::placeholder {
  color: var(--text-muted);
  opacity: 0.5;
}

.excerpt-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.character-counter {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(0, 0, 0, 0.5);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  backdrop-filter: blur(10px);
  pointer-events: none;
}

/* Editor */
.editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Settings Column */
.settings-column {
  display: flex;
  flex-direction: column;
}

.settings-panel {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 120px;
  height: fit-content;
}

.panel-section {
  margin-bottom: 1.5rem;
}

.panel-section:last-child {
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Image Upload */
.image-upload {
  width: 100%;
}

.file-input-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.image-upload-area {
  position: relative;
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.image-upload-area:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 0.875rem;
  gap: 0.5rem;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-primary);
  font-size: 0.875rem;
  gap: 0.75rem;
}

.image-upload-area.uploading {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  cursor: not-allowed;
}

.uploaded-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.remove-image-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* Sidebar Publish Button */
.publish-btn-sidebar {
  width: 100%;
  margin-top: 1rem;
  padding: 0.875rem 1.25rem;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4);
}

.publish-btn-sidebar:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

.publish-btn-sidebar:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.2);
}

/* Responsive */
@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .settings-column {
    order: -1;
  }
  
  .settings-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .blog-header {
    padding: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header-left {
    flex: 1;
  }
  
  .header-actions {
    width: 100%;
    order: 1;
  }
  
  .blog-main {
    padding: 1rem;
  }
  
  .title-input {
    font-size: 1.5rem;
  }
  
  .settings-panel {
    grid-template-columns: 1fr;
  }
}

/* Modern Editor Direct Styling */
.content-column .ql-toolbar {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 16px 16px 0 0;
}

.content-column .ql-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.06) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none;
  border-radius: 0 0 16px 16px;
  font-size: 1rem;
  line-height: 1.7;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.content-column .ql-editor {
  padding: 1.5rem;
  color: var(--text-primary);
  background: transparent;
  border: none;
  outline: none;
  font-size: 16px;
  line-height: 1.7;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.content-column .ql-editor::-webkit-scrollbar {
  width: 8px;
}

.content-column .ql-editor::-webkit-scrollbar-track {
  background: transparent;
}

.content-column .ql-editor::-webkit-scrollbar-thumb {
  background: rgba(139, 140, 245, 0.3);
  border-radius: 4px;
}

.content-column .ql-editor::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 140, 245, 0.5);
}

/* Typography styling */
.content-column .ql-editor h1,
.content-column .ql-editor h2,
.content-column .ql-editor h3 {
  color: var(--text-primary);
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.3;
}

.content-column .ql-editor h1 {
  font-size: 2rem;
}

.content-column .ql-editor h2 {
  font-size: 1.5rem;
}

.content-column .ql-editor h3 {
  font-size: 1.25rem;
}

.content-column .ql-editor p {
  margin: 1rem 0;
  color: var(--text-secondary);
}

.content-column .ql-editor blockquote {
  background: rgba(139, 140, 245, 0.1);
  border-left: 4px solid #8b8cf5;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--text-secondary);
}

.content-column .ql-editor ul,
.content-column .ql-editor ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-column .quill {
    border-radius: 12px;
  }
  
  .content-column .ql-container {
    max-height: 400px;
  }
  
  .content-column .ql-editor {
    min-height: 250px;
    max-height: 400px;
    padding: 1rem;
    font-size: 14px;
  }
  
  .content-column .ql-toolbar {
    padding: 0.75rem 1rem;
    border-radius: 12px 12px 0 0;
  }
  
  .content-column .ql-toolbar button {
    width: 28px;
    height: 28px;
    margin: 0 1px;
  }
}

/* Light Quill Icons */
.content-column .ql-snow .ql-stroke {
  stroke: rgba(255, 255, 255, 0.9);
}

.content-column .ql-snow .ql-fill,
.content-column .ql-snow .ql-stroke.ql-fill {
  fill: rgba(255, 255, 255, 0.9);
}

.content-column .ql-snow .ql-toolbar button:hover .ql-stroke {
  stroke: #8b8cf5;
}

.content-column .ql-snow .ql-toolbar button:hover .ql-fill,
.content-column .ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill {
  fill: #8b8cf5;
}

.content-column .ql-snow .ql-toolbar button.ql-active .ql-stroke {
  stroke: #8b8cf5;
}

.content-column .ql-snow .ql-toolbar button.ql-active .ql-fill,
.content-column .ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill {
  fill: #8b8cf5;
}

/* Blog List Styles */
.blog-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: var(--text-muted);
  gap: 1rem;
}

.modern-blog-list {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

.blog-controls {
  padding: 1rem 0 2rem 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-container > svg {
  position: absolute;
  left: 1rem;
  color: var(--text-muted);
  pointer-events: none;
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clear-search:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.blog-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.25rem;
  padding: 0;
}

.blog-post-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.blog-post-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.post-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-post-card:hover .post-image img {
  transform: scale(1.05);
}

.post-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-muted);
}

.post-status {
  position: absolute;
  top: 12px;
  right: 12px;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(10px);
}

.status-badge.published {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge.draft {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.post-content {
  padding: 1rem 1.25rem;
}

.post-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.post-date, .post-views {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.post-views {
  color: rgba(34, 197, 94, 0.8);
}

.post-updated {
  font-style: italic;
  opacity: 0.7;
  font-size: 0.7rem;
}

.post-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.875rem 1.25rem;
  background: rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-btn.edit:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.action-btn.view:hover {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.action-btn.delete:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* Responsive */
@media (max-width: 1200px) {
  .blog-posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }
}

@media (max-width: 768px) {
  .blog-posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }
  
  .post-content {
    padding: 1rem;
  }
  
  .post-actions {
    padding: 0.75rem 1rem;
  }

  .blog-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .blog-posts-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading Spinner */
.blog-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--primary-color, #667eea);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* FAQ Specific Styles */
.faq-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faq-item-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.faq-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8b5cf6, #a855f7, #9333ea);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.faq-item-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(139, 92, 246, 0.2),
    0 4px 10px rgba(0, 0, 0, 0.1);
}

.faq-item-card:hover::before {
  transform: scaleX(1);
}

.faq-content {
  flex: 1;
  padding-right: 1rem;
}

.faq-question {
  margin-bottom: 1rem;
}

.faq-question h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5;
}

.faq-answer {
  margin-bottom: 1.5rem;
}

.faq-answer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.faq-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.faq-date {
  color: var(--text-muted);
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.faq-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.faq-item-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.faq-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: var(--text-muted);
}

.faq-loading .spinner {
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .faq-item-card {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .faq-content {
    padding-right: 0;
  }

  .faq-actions {
    align-self: flex-end;
  }

  .faq-question h3 {
    font-size: 1.125rem;
  }

  .content-form .form-group label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
  }

  .content-form .form-group input,
  .content-form .form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    resize: vertical;
  }

  .content-form .form-group input:focus,
  .content-form .form-group textarea:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.5);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }

  .content-form .form-group input::placeholder,
  .content-form .form-group textarea::placeholder {
    color: var(--text-muted);
  }

  .content-form .form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Portfolio Styles */
.portfolio-form {
  max-width: none;
}

.form-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.form-section h3 {
  margin: 0 0 1rem 0;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: rgba(255, 255, 255, 0.5);
  z-index: 1;
  pointer-events: none;
}

.input-with-icon input {
  padding-left: 2.75rem;
}

.form-hint {
  display: block;
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Icon Upload Styles */
.icon-upload-area {
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.03);
}

.icon-upload-area:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.05);
}

.icon-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.upload-label:hover {
  color: #8b8cf5;
}

.upload-label svg {
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.upload-label:hover svg {
  color: #8b8cf5;
  transform: scale(1.1);
}

.upload-label span {
  font-size: 0.9rem;
  font-weight: 500;
}

.upload-label small {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.icon-preview {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.icon-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.icon-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-preview:hover .icon-overlay {
  opacity: 1;
}

.remove-icon-btn {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-icon-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* Portfolio Grid Styles */
.portfolio-controls {
  margin-bottom: 2rem;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.portfolio-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.portfolio-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.portfolio-card:hover::before {
  opacity: 1;
}

.portfolio-icon {
  width: 64px;
  height: 64px;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-icon img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 8px;
}

.portfolio-icon svg {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.portfolio-card:hover .portfolio-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.portfolio-card:hover .portfolio-icon svg {
  color: white;
}

.portfolio-content {
  flex: 1;
  margin-bottom: 1rem;
}

.portfolio-title {
  margin: 0 0 0.5rem 0;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.01em;
  line-height: 1.3;
}

.portfolio-description {
  margin: 0 0 0.75rem 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.portfolio-link {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  color: #8b8cf5;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.portfolio-link:hover {
  color: #a5a6f6;
  transform: translateX(2px);
}

.portfolio-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.portfolio-date {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8125rem;
}

.portfolio-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.7);
}

.portfolio-loading .spinner {
  margin-bottom: 1rem;
}

@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .portfolio-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
  }
  
  .portfolio-card {
    padding: 1.25rem;
  }
  
  .form-section {
    padding: 1.25rem;
  }
  
  .portfolio-icon {
    width: 56px;
    height: 56px;
  }
  
  .portfolio-icon img {
    width: 32px;
    height: 32px;
  }
}

/* Galeri yönetimi stilleri */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.gallery-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
}

.gallery-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.gallery-card-header {
  padding: 1.5rem 1.5rem 1rem;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
}

.gallery-card-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  flex: 1;
}

.gallery-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-toggle {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-toggle.active {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.status-toggle.inactive {
  background: rgba(156, 163, 175, 0.1);
  color: #6b7280;
}

.status-toggle:hover {
  transform: scale(1.1);
}

.gallery-description {
  padding: 0 1.5rem;
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.gallery-stats {
  padding: 0 1.5rem;
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.gallery-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.gallery-link {
  padding: 0 1.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6366f1;
  font-size: 0.875rem;
}

.gallery-link a {
  color: inherit;
  text-decoration: none;
  word-break: break-all;
}

.gallery-link a:hover {
  text-decoration: underline;
}

.gallery-actions {
  padding: 1rem 1.5rem;
  background: #f9fafb;
  display: flex;
  gap: 0.75rem;
  border-top: 1px solid #f3f4f6;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.upload-btn:hover {
  background: #5856eb;
}

.empty-images {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
}

.empty-images p {
  margin: 0.5rem 0;
  font-size: 0.875rem;
}

.empty-images .text-sm {
  font-size: 0.75rem;
  color: #9ca3af;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.image-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.image-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.image-preview {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.2s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  transition: all 0.2s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.move-btn,
.delete-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.move-btn {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.move-btn:hover {
} 

/* ==========================================================================
   Link Manager (Portfolio renamed)
   ========================================================================== */

.link-form {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.form-main-content {
  flex: 2;
  min-width: 300px;
}

.form-sidebar {
  flex: 1;
  min-width: 250px;
}

.icon-uploader .icon-preview {
  width: 128px;
  height: 128px;
  background-color: var(--color-background);
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  overflow: hidden;
}

.icon-uploader .icon-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.icon-uploader .icon-preview svg {
  color: var(--color-text-tertiary);
}

.icon-uploader .icon-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-uploader .upload-btn,
.icon-uploader .remove-btn {
  flex: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.icon-uploader .upload-btn {
  background-color: var(--color-primary-muted);
  color: var(--color-primary);
  border: 1px solid var(--color-primary-muted);
}

.icon-uploader .upload-btn:hover {
  background-color: var(--color-primary-hover-muted);
}

.icon-uploader .remove-btn {
  background-color: var(--color-danger-muted);
  color: var(--color-danger);
  border: 1px solid var(--color-danger-muted);
}

.icon-uploader .remove-btn:hover {
  background-color: var(--color-danger-hover-muted);
}

.form-actions-full {
  width: 100%;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}


.links-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.link-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.2s, transform 0.2s;
}

.link-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.link-card.dragging {
  box-shadow: var(--shadow-xl);
  background-color: var(--color-primary-muted);
  transform: rotate(2deg);
}

.link-card-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  color: var(--color-text-tertiary);
}

.link-card-drag-handle:active {
  cursor: grabbing;
}


.link-card-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  background-color: var(--color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.link-card-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.link-card-icon svg {
  color: var(--color-text-tertiary);
}

.link-card-content {
  flex-grow: 1;
}

.link-card-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--color-text-primary);
}

.link-card-content a {
  font-size: 0.9rem;
  color: var(--color-primary);
  text-decoration: none;
  word-break: break-all;
}

.link-card-content a:hover {
  text-decoration: underline;
}

.link-card-actions {
  display: flex;
  gap: 0.5rem;
}

.link-card-actions button {
  padding: 0.5rem;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  color: var(--color-text-secondary);
  transition: background-color 0.2s, color 0.2s;
}

.link-card-actions button:hover {
  background-color: var(--color-background);
  color: var(--color-primary);
}

.content-form .form-group .file-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
}

.content-form .form-group .file-upload-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: background-color 0.2s;
}

.content-form .form-group .file-upload-label:hover {
  background-color: var(--color-gray-200);
}

.content-form .form-group .file-upload-container input[type="file"] {
  display: none;
}

.content-form .form-group .image-preview {
  margin-top: 1rem;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-large);
  padding: 0.5rem;
  background-color: var(--color-gray-50);
}

.content-form .form-group .image-preview img {
  max-width: 300px;
  max-height: 200px;
  border-radius: var(--border-radius-medium);
}

.content-form .form-group .toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.content-form .form-group .toggle-switch input[type="checkbox"] {
    display: none;
}

.content-form .form-group .toggle-switch label {
    cursor: pointer;
    width: 48px;
    height: 26px;
    background: var(--color-gray-300);
    display: block;
    border-radius: 26px;
    position: relative;
    transition: background-color 0.2s;
}

.content-form .form-group .toggle-switch label:after {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    transition: 0.2s;
}

.content-form .form-group .toggle-switch input:checked + label {
    background: var(--color-primary);
}

.content-form .form-group .toggle-switch input:checked + label:after {
    left: calc(100% - 3px);
    transform: translateX(-100%);
}

.content-form .form-group .toggle-switch span {
    font-weight: 500;
    color: var(--color-gray-700);
}

.content-form .form-group .toggle-switch span {
  margin-left: 12px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

/* === Video Manager List Styles === */
.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.draggable-item {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.draggable-item:hover {
  border-color: var(--primary-color-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}

.draggable-item.dragging {
  background-color: var(--bg-tertiary);
  box-shadow: 0 10px 20px rgba(0,0,0,0.15);
  transform: scale(1.02) translateY(-2px);
}

.drag-handle {
  color: var(--text-muted);
  margin-right: 1rem;
  cursor: grab;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.item-thumbnail.video-thumbnail {
  width: 120px;
  height: 68px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1.5rem;
  flex-shrink: 0;
  background-color: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-thumbnail.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: var(--text-muted);
}

.item-content {
  flex-grow: 1;
}

.item-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
  margin: 0;
}

.item-status {
  margin: 0 1.5rem;
  flex-shrink: 0;
}

.status-badge {
  padding: 0.3rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active, .status-badge.published {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.inactive, .status-badge.draft {
  background-color: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button-icon {
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.action-button-icon:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.action-button-icon.danger:hover {
  color: #ef4444;
}

.content-toolbar {
  margin-bottom: 1.5rem;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-box .search-icon {
  position: absolute;
  top: 50%;
  left: 1rem;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border-radius: 12px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color-light);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.search-box .clear-icon {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clear-search:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

/* === Gallery Manager List Styles === */

.gallery-grid-new {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-card-new {
  background-color: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.gallery-card-new:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  border-color: var(--primary-color-light);
}

.gallery-card-cover {
  position: relative;
  height: 200px;
  background-color: var(--bg-tertiary);
}

.gallery-card-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-card-new:hover .gallery-card-cover img {
  transform: scale(1.05);
}

.gallery-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, transparent 50%);
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 0.75rem;
}

.image-count {
  background-color: rgba(0,0,0,0.6);
  color: white;
  padding: 0.25rem 0.6rem;
  border-radius: 20px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  backdrop-filter: blur(5px);
}

.gallery-card-content {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.gallery-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.gallery-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.gallery-card-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  flex-grow: 1;
  margin-bottom: 1rem;
}

.gallery-card-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color-light);
}

/* === Gallery Manager Form Styles === */

.gallery-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.gallery-form-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 2rem;
  margin-top: 1.5rem;
}

.gallery-form-main {
  background-color: var(--bg-secondary);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.gallery-form-sidebar .sidebar-section {
  background-color: var(--bg-secondary);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.gallery-form-sidebar .sidebar-section h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.gallery-form-sidebar .sidebar-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.gallery-form-sidebar .sidebar-section input {
  width: 100%;
}

.sidebar-note {
  font-size: 0.875rem;
  color: var(--text-muted);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
}

.w-full {
  width: 100%;
}

.image-manager-section {
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
}

.image-manager-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.image-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 1.5rem;
  transition: border-color 0.2s ease;
}

.image-upload-area:hover {
  border-color: var(--primary-color-light);
}

.image-upload-area input[type="file"] {
  display: none;
}

.image-upload-label {
  cursor: pointer;
  color: var(--primary-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.image-list-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

.image-list-preview {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.image-list-details {
  flex-grow: 1;
}

.image-title-input {
  width: 100%;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.image-list-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.empty-state-inner {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
}

/* === Table Styles === */
.table-container {
  width: 100%;
  overflow-x: auto;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
}

.content-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.content-table th,
.content-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.content-table th {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-muted);
  background-color: var(--bg-tertiary);
}

.content-table thead tr:first-child th:first-child {
  border-top-left-radius: 12px;
}
.content-table thead tr:first-child th:last-child {
  border-top-right-radius: 12px;
}

.content-table td {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.content-table tbody tr:hover {
  background-color: var(--bg-tertiary);
}

.content-table .question-cell {
  font-weight: 500;
  color: var(--text-primary);
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-table .actions-column {
  width: 100px;
  text-align: right;
}

/* ==========================================================================
   Generic List Page & Table Styles
   ========================================================================== */

.list-page-container .content-header {
  align-items: center;
}

.list-page-container .content-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.list-page-container .search-container {
  width: 340px;
}

.list-page-container .search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.list-page-container .search-box svg {
  position: absolute;
  left: 1.25rem;
  color: rgba(255, 255, 255, 0.6);
  z-index: 1;
  transition: color 0.3s ease;
}

.list-page-container .search-box input {
  width: 100%;
  padding: 1rem 3rem 1rem 3.5rem;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  backdrop-filter: blur(15px);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.list-page-container .search-box input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

.list-page-container .search-box input:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.15) 0%, 
    rgba(118, 75, 162, 0.1) 100%);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.2),
    0 0 0 4px rgba(102, 126, 234, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.list-page-container .search-box input:focus + svg {
  color: rgba(102, 126, 234, 0.8);
}

.list-page-container .search-box .clear-search {
  position: absolute;
  right: 0.75rem;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.list-page-container .search-box .clear-search:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  transform: scale(1.1);
}

.list-table-container {
  width: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.list-table-header,
.list-table-row {
  display: grid;
  grid-template-columns: 50px 80px 1fr 1fr 120px;
  gap: 1.5rem;
  align-items: center;
  padding: 1rem 1.5rem;
  transition: background-color 0.2s ease;
}

.list-table-header {
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-muted);
  letter-spacing: 0.05em;
}

.list-table-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.list-table-row:last-child {
  border-bottom: none;
}

.list-table-row:hover {
  background: rgba(255, 255, 255, 0.05);
}

.list-table-row.dragging {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
  transform: scale(1.01);
  border-radius: 12px;
}

.body-cell {
  display: flex;
  align-items: center;
  min-width: 0;
}

.header-cell.actions,
.body-cell.actions {
  justify-content: flex-end;
}

.body-cell.drag-handle {
  cursor: grab;
  color: var(--text-muted);
  transition: color 0.2s ease;
}

.body-cell.drag-handle:hover {
  color: var(--text-primary);
}

.body-cell.icon .icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.body-cell.icon .icon-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.body-cell.icon .icon-wrapper svg {
  color: var(--text-muted);
  width: 24px;
  height: 24px;
}

.body-cell.title span {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.body-cell.link a {
  color: #8b8cf5;
  text-decoration: none;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.2s ease;
}

.body-cell.link a:hover {
  color: #a5a6f6;
  text-decoration: underline;
}

.body-cell.actions {
  gap: 0.5rem;
}

.body-cell.actions .action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.body-cell.actions .action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.body-cell.actions .action-btn.edit:hover {
  background-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
}

.body-cell.actions .action-btn.delete:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.list-table-body .empty-state {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

/* List page specific styles */
.list-page-container .content-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
}

.list-page-container .content-actions {
  width: 100%;
  justify-content: space-between;
}

/* ==========================================================================
   Link Form Styles
   ========================================================================== */
.link-form {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-areas: 
    "main sidebar"
    "actions actions";
  gap: 2rem 2.5rem;
}

.form-main-content { 
  grid-area: main; 
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}
.form-sidebar { 
  grid-area: sidebar; 
}
.form-actions-full { 
  grid-area: actions; 
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
}
.form-actions-full .action-button {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.form-group input[type="text"],
.form-group input[type="url"] {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem 1.25rem;
  color: var(--text-primary);
  font-size: 0.9375rem;
  transition: all 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="url"]:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.icon-uploader {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  text-align: center;
}

.icon-uploader .icon-preview {
  width: 120px;
  height: 120px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  border: 2px dashed rgba(255, 255, 255, 0.15);
}

.icon-uploader .icon-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
}

.icon-uploader .icon-actions {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.icon-uploader .upload-btn,
.icon-uploader .remove-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-uploader .upload-btn {
  background-color: #3b82f6;
  color: white;
}

.icon-uploader .upload-btn:hover {
  background-color: #2563eb;
}

.icon-uploader .remove-btn {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.icon-uploader .remove-btn:hover {
  background-color: rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

/* Form Row Stili */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* === Fix for Quill Editor Layout === */
.modern-blog-editor .content-column {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.modern-blog-editor .editor-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.modern-blog-editor .quill {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
}

.modern-blog-editor .ql-container {
  flex-grow: 1;
  border: none;
  font-size: 1rem;
  line-height: 1.7;
}

.modern-blog-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.9) 0%, 
    rgba(30, 41, 59, 0.8) 100%);
  padding: 1rem 1.5rem;
}

.modern-blog-editor .ql-editor {
  padding: 1.5rem;
  color: var(--text-primary);
  background: transparent;
  border: none;
  outline: none;
  font-size: 16px;
  line-height: 1.7;
  min-height: 300px;
}
/* === End Fix === */

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-column .quill {
    border-radius: 12px;
  }
  
  .content-column .ql-container {
    max-height: 400px;
  }
  
  .content-column .ql-editor {
    min-height: 250px;
    max-height: 400px;
    padding: 1rem;
    font-size: 14px;
  }
  
  .content-column .ql-toolbar {
    padding: 0.75rem 1rem;
    border-radius: 12px 12px 0 0;
  }
  
  .content-column .ql-toolbar button {
    width: 28px;
    height: 28px;
    margin: 0 1px;
  }
}

/* === Simple Editor Styles === */
.simple-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-grow: 1;
}

.editor-toolbar {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.9) 0%, 
    rgba(30, 41, 59, 0.8) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-btn {
  padding: 6px 12px;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.toolbar-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
}

.content-textarea {
  flex-grow: 1;
  width: 100%;
  padding: 20px;
  background: transparent;
  border: none;
  outline: none;
  color: #e2e8f0;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  min-height: 400px;
}

.content-textarea::placeholder {
  color: rgba(226, 232, 240, 0.5);
  font-style: italic;
}

.content-textarea:focus {
  outline: none;
}
/* === End Simple Editor === */

/* Content Error Boundary */
.content-error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  margin: 2rem;
  text-align: center;
}

.content-error-boundary h2 {
  color: #ef4444;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.content-error-boundary p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.content-error-boundary button {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.content-error-boundary button:hover {
  background: #dc2626;
}