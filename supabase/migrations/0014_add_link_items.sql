-- <PERSON><PERSON><PERSON>ı öğeleri tablosu
CREATE TABLE IF NOT EXISTS portfolio_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  link TEXT,
  icon_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS (Row Level Security) politikaları
ALTER TABLE portfolio_items ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON><PERSON>lar sadece kendi bağlantılarını görebilir
CREATE POLICY "Allow users to see their own links" ON portfolio_items
  FOR SELECT USING (auth.uid() = user_id);

-- <PERSON><PERSON><PERSON><PERSON><PERSON>lar sadece kendi bağlantılarını ekleyebilir
CREATE POLICY "Allow users to insert their own links" ON portfolio_items
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sadece kendi bağlantılarını güncelleyebilir
CREATE POLICY "Allow users to update their own links" ON portfolio_items
  FOR UPDATE USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Kullanıcılar sadece kendi bağlantılarını silebilir
CREATE POLICY "Allow users to delete their own links" ON portfolio_items
  FOR DELETE USING (auth.uid() = user_id);

-- İndeksler (performans için)
CREATE INDEX IF NOT EXISTS idx_portfolio_items_user_id ON portfolio_items(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_items_created_at ON portfolio_items(created_at DESC);

-- Storage bucket'ı oluştur (eğer yoksa)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'portfolio',
  'portfolio',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- Storage politikaları
CREATE POLICY "Allow public access to icons" ON storage.objects
  FOR SELECT USING (bucket_id = 'portfolio');

CREATE POLICY "Allow authenticated users to upload icons" ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'portfolio' AND (storage.foldername(name))[1] = auth.uid()::text); 