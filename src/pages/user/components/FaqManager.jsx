import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useContentFaqs } from '../../../contexts/ContentContext';
import {
  FiPlus, FiArrowLeft, FiSave, FiEdit, FiTrash2, 
  FiHelpCircle, FiList, FiSearch, FiX
} from 'react-icons/fi';
import showToast from '../../../utils/toast';

const FaqManager = () => {
  const navigate = useNavigate();
  const { mode, id } = useParams();
  const { 
    faqs, 
    loading, 
    getFaqById, 
    createFaq, 
    updateFaq, 
    deleteFaq 
  } = useContentFaqs();
  
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [formData, setFormData] = useState({
    question: '',
    answer: ''
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      const loadFaq = async () => {
        try {
          const data = await getFaqById(id);
          if (data) {
            setFormData({
              question: data.question || '',
              answer: data.answer || ''
            });
          }
        } catch (error) {
          navigate('/dashboard/faqs/list');
        }
      };
      loadFaq();
    }
  }, [mode, id, getFaqById, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.question.trim() || !formData.answer.trim()) {
      showToast.warning('Soru ve cevap alanları boş bırakılamaz!');
      return;
    }

    setSaving(true);
    try {
      if (mode === 'edit' && id) {
        await updateFaq(id, formData);
      } else {
        await createFaq(formData);
      }
      navigate('/dashboard/faqs/list');
    } catch (error) {
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (faqId) => {
    if (!window.confirm('Bu SSS\'yi silmek istediğinizden emin misiniz?')) {
      return;
    }
    try {
      await deleteFaq(faqId);
    } catch (error) {
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      {mode === 'add' || mode === 'edit' ? (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)', color: '#8b5cf6' }}>
                <FiHelpCircle size={24} />
              </div>
              <div className="content-title">
                <h2>{mode === 'edit' ? 'SSS Düzenle' : 'Yeni SSS Ekle'}</h2>
                <p>{mode === 'edit' ? 'Mevcut SSS\'yi düzenleyin' : 'Sık sorulan soru ekleyin'}</p>
              </div>
            </div>
            <div className="content-actions">
              <button 
                className="action-button secondary"
                onClick={() => navigate('/dashboard/faqs/list')}
              >
                <FiArrowLeft size={18} />
                <span>Listeye Dön</span>
              </button>
            </div>
          </div>
          
          <div className="content-body">
            <form className="content-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="faq-question">Soru *</label>
                <input 
                  type="text" 
                  id="faq-question" 
                  name="question"
                  value={formData.question}
                  onChange={handleInputChange}
                  placeholder="Soruyu girin"
                  required
                  disabled={loading}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="faq-answer">Cevap *</label>
                <textarea 
                  id="faq-answer" 
                  name="answer"
                  value={formData.answer}
                  onChange={handleInputChange}
                  placeholder="Cevabı girin"
                  rows={6}
                  required
                  disabled={loading}
                />
              </div>
              
              <div className="form-actions">
                <button 
                  type="button" 
                  className="action-button secondary"
                  onClick={() => navigate('/dashboard/faqs/list')}
                  disabled={saving}
                >
                  İptal
                </button>
                <button 
                  type="submit" 
                  className="action-button primary"
                  disabled={saving || loading}
                >
                  {saving ? (
                    <>
                      <div className="spinner-sm"></div>
                      <span>{mode === 'edit' ? 'Güncelleniyor...' : 'Kaydediliyor...'}</span>
                    </>
                  ) : (
                    <>
                      <FiSave size={18} />
                      <span>{mode === 'edit' ? 'Güncelle' : 'SSS Kaydet'}</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      ) : (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: 'rgba(139, 92, 246, 0.1)', color: '#8b5cf6' }}>
                <FiHelpCircle size={24} />
              </div>
              <div className="content-title">
                <h2>SSS Listesi</h2>
                <p>Sık sorulan soruları görüntüleyin ve düzenleyin</p>
              </div>
            </div>
            <div className="content-actions">
              <button 
                className="action-button primary"
                onClick={() => navigate('/dashboard/faqs/add')}
              >
                <FiPlus size={18} />
                <span>Yeni SSS Ekle</span>
              </button>
            </div>
          </div>
          
          <div className="content-body">
            <div className="content-toolbar">
              <div className="search-box">
                <FiSearch className="search-icon" />
                <input
                  type="text"
                  placeholder="SSS içinde ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
                {searchTerm && (
                  <button onClick={clearSearch} className="clear-icon">
                    <FiX size={16} />
                  </button>
                )}
              </div>
            </div>

            {loading ? (
              <div className="loading-indicator"><p>SSS listesi yükleniyor...</p></div>
            ) : filteredFaqs.length > 0 ? (
              <div className="table-container">
                <table className="content-table">
                  <thead>
                    <tr>
                      <th>Soru</th>
                      <th>Cevap</th>
                      <th>Oluşturma Tarihi</th>
                      <th className="actions-column">Eylemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredFaqs.map((faq) => (
                      <tr key={faq.id}>
                        <td className="question-cell">{faq.question}</td>
                        <td>{faq.answer.substring(0, 100)}{faq.answer.length > 100 ? '...' : ''}</td>
                        <td>{new Date(faq.created_at).toLocaleDateString('tr-TR')}</td>
                        <td>
                          <div className="item-actions">
                            <button 
                              className="action-button-icon"
                              onClick={() => navigate(`/dashboard/faqs/edit/${faq.id}`)}
                              title="Düzenle"
                            >
                              <FiEdit />
                            </button>
                            <button 
                              className="action-button-icon danger"
                              onClick={() => handleDelete(faq.id)}
                              title="Sil"
                            >
                              <FiTrash2 />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="empty-state">
                <div className="empty-icon">
                  <FiHelpCircle size={48} />
                </div>
                <h4>
                  {searchTerm ? 'Arama sonucu bulunamadı' : 'Henüz SSS eklenmemiş'}
                </h4>
                <p>
                  {searchTerm 
                    ? 'Farklı arama terimleri deneyebilirsiniz' 
                    : 'İlk SSS\'nizi ekleyerek başlayın'
                  }
                </p>
                {!searchTerm && (
                  <button 
                    className="add-first-btn"
                    onClick={() => navigate('/dashboard/faqs/add')}
                  >
                    <FiPlus />
                    İlk SSS'yi Ekle
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FaqManager; 