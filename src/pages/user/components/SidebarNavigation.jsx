import React, { memo } from 'react';

// SidebarNavigation bileşeni - memo ile sarılarak gereksiz render'lar<PERSON>lüyoruz
const SidebarNavigation = memo(({
  TABS,
  tabOrder,
  contentData,
  draggedTab,
  dropTarget,
  dropPosition,
  activeTab,
  location,
  setShowWelcome,
  navigate,
  setActiveTab,
  handleDragStart,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleDragEnd,
  handleTabToggle
}) => {
  return (
    <nav className="sidebar-nav">
      {tabOrder.map(tabId => {
        const tab = TABS.find(t => t.id === tabId);
        if (!tab) return null;
        
        const IconComponent = tab.icon;
        // Ana sekme aktiflik kontrolü
        const isActive = (() => {
          // Blog özel durumu - route tabanlı kontrol
          if (tab.id === 'blog') {
            return location.pathname.includes('/blogs');
          }
          
          // SSS özel durumu - route tabanlı kontrol
          if (tab.id === 'faq') {
            return location.pathname.includes('/faqs');
          }
          
          // Diğer sekmeler için - activeTab tabanlı kontrol  
          // Doğrudan bu sekme aktifse
          if (activeTab === tab.id) return true;
          
          // Bu sekmenin alt menü öğesi aktifse
          if (activeTab?.startsWith(`${tab.id}-`)) return true;
          
          return false;
        })();
        const isEnabled = contentData[tab.id].enabled;
        const isDragging = draggedTab === tab.id;
        const isDropTarget = dropTarget === tab.id;
        const showDropBefore = isDropTarget && dropPosition === 'before';
        const showDropAfter = isDropTarget && dropPosition === 'after';
        
        return (
          <NavItem
            key={tab.id}
            tab={tab}
            isActive={isActive}
            isEnabled={isEnabled}
            isDragging={isDragging}
            isDropTarget={isDropTarget}
            showDropBefore={showDropBefore}
            showDropAfter={showDropAfter}
            draggedTab={draggedTab}
            setShowWelcome={setShowWelcome}
            navigate={navigate}
            setActiveTab={setActiveTab}
            handleDragStart={handleDragStart}
            handleDragOver={handleDragOver}
            handleDragLeave={handleDragLeave}
            handleDrop={handleDrop}
            handleDragEnd={handleDragEnd}
            handleTabToggle={handleTabToggle}
          />
        );
      })}
    </nav>
  );
});

// NavItem bileşeni - her bir navigasyon öğesi için ayrı bir bileşen
const NavItem = memo(({
  tab,
  isActive,
  isEnabled,
  isDragging,
  isDropTarget,
  showDropBefore,
  showDropAfter,
  draggedTab,
  setShowWelcome,
  navigate,
  setActiveTab,
  handleDragStart,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleDragEnd,
  handleTabToggle
}) => {
  const IconComponent = tab.icon;
  
  const handleClick = () => {
    // Hoşgeldin sayfasını kapat
    setShowWelcome(false);
    
    // Her buton doğrudan kendi list sayfasına gitsin
    if (tab.id === 'links') {
      navigate('/dashboard/links/list');
    } else if (tab.id === 'blog') {
      navigate('/dashboard/blogs/list');
    } else if (tab.id === 'gallery') {
      navigate('/dashboard/galleries/list');
    } else if (tab.id === 'video') {
      navigate('/dashboard/videos/list');
    } else if (tab.id === 'faq') {
      navigate('/dashboard/faqs/list');
    } else if (tab.id === 'catalog') {
      navigate('/dashboard/catalogs/list');
    } else if (tab.id === 'podcast') {
      navigate('/dashboard/podcasts/list');
    } else {
      // Alt menüsü olmayan sekmeler için sekmeyi aktifleştir ve ana URL'e dön
      navigate('/dashboard');
      setActiveTab(tab.id);
    }
  };
  
  const handleKeyDown = (e) => {
    // Enter veya Space tuşuna basıldığında tıklama işlemi yap
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };
  
  const handleToggleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Drag işlemi devam ediyorsa switch'i engelle
    if (draggedTab) {
      return false;
    }
    
    // Parent element'in drag durumunu kontrol et
    const parentItem = e.target.closest('.sidebar-nav-item');
    if (parentItem && parentItem.classList.contains('dragging')) {
      return false;
    }
    
    handleTabToggle(tab.id);
    return false;
  };
  
  return (
    <div className="nav-item-wrapper">
      {showDropBefore && <div className="drop-indicator drop-before"></div>}
      <div 
        draggable
        data-tab-id={tab.id}
        className={`sidebar-nav-item ${isActive ? 'active' : ''} ${isEnabled ? 'enabled' : 'disabled'} ${isDragging ? 'dragging' : ''} ${isDropTarget ? 'drop-target' : ''}`}
        onClick={handleClick}
        onDragStart={(e) => handleDragStart(e, tab.id)}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, tab.id)}
        onDragEnd={handleDragEnd}
        style={{ 
          cursor: isDragging ? 'grabbing' : 'grab',
          opacity: isDragging ? 0.6 : 1,
          transform: isDragging ? 'rotate(3deg) scale(1.02)' : 'none',
          boxShadow: isDragging ? '0 8px 25px rgba(0,0,0,0.2)' : ''
        }}
        role="button"
        tabIndex="0"
        title="Sürükleyerek sıralamayı değiştirebilirsiniz"
        onKeyDown={handleKeyDown}
      >
        <div className="nav-item-left">
          <div className="nav-item-drag-handle">
            <div className="drag-dots">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div className="nav-item-icon" style={{ color: isActive ? tab.color : '' }}>
            <IconComponent size={20} />
          </div>
          <div className="nav-item-content">
            <span className="nav-item-title">{tab.name}</span>
            <span className="nav-item-status">
              {isEnabled ? 'Aktif' : 'Pasif'}
            </span>
          </div>
        </div>
        
        <div className="nav-item-right">
          <div className="nav-item-controls">
            <div 
              className={`nav-item-switch ${isEnabled ? 'active' : ''}`}
              onClick={handleToggleClick}
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
              onDragStart={(e) => {
                e.preventDefault();
                return false;
              }}
              title={isEnabled ? 'Pasif yap' : 'Aktif yap'}
            >
              <div className="switch-slider"></div>
            </div>
          </div>
        </div>
      </div>
      {showDropAfter && <div className="drop-indicator drop-after"></div>}
    </div>
  );
});

// Bileşen adlarını tanımla (debugging için)
SidebarNavigation.displayName = 'SidebarNavigation';
NavItem.displayName = 'NavItem';

export default SidebarNavigation;
