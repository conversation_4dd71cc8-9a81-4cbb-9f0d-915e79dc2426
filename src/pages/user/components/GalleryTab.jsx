import { memo } from 'react';
import { FiSettings, FiImage, FiPlus, FiTrash2 } from 'react-icons/fi';

const GalleryTab = memo(({ contentData, activeTab, handleContentChange, handleTabToggle, addContentItem, removeContentItem, updateContentItem }) => {
  const currentTab = contentData[activeTab];
  const tabColor = '#10b981';

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: `${tabColor}20`, color: tabColor }}>
            <FiImage size={24} />
          </div>
          <div className="content-title">
            <h2>{currentTab.title}</h2>
            <p>{currentTab.description}</p>
          </div>
        </div>
        <div className="content-toggle">
          <button className={`toggle-switch ${currentTab.enabled ? 'active' : ''}`} onClick={() => handleTabToggle(activeTab)}>
            <div className="toggle-slider"></div>
            <span className="toggle-label">{currentTab.enabled ? 'Aktif' : 'Pasif'}</span>
          </button>
        </div>
      </div>

      <div className="content-body">
        <div className="content-settings-section">
          <h3><FiSettings size={20} /> Genel Ayarlar</h3>
          <div className="settings-grid">
            <div className="form-group">
              <label>Sekme Başlığı</label>
              <input type="text" value={currentTab.title} onChange={(e) => handleContentChange(activeTab, 'title', e.target.value)} placeholder="Sekme başlığını girin" />
            </div>
            <div className="form-group">
              <label>Açıklama</label>
              <textarea value={currentTab.description} onChange={(e) => handleContentChange(activeTab, 'description', e.target.value)} placeholder="Sekme açıklamasını girin" rows={3} />
            </div>
          </div>
        </div>

        {currentTab.enabled && (
          <div className="content-items-section">
            <div className="section-header">
              <h3><FiImage size={20} /> İçerikler</h3>
              <button className="add-content-btn" onClick={() => addContentItem(activeTab)}><FiPlus />Fotoğraf Ekle</button>
            </div>
            <div className="content-items-grid">
              {currentTab.items.map(item => (
                <div key={item.id} className="content-item-card">
                  <div className="item-header">
                    <div className="item-number">#{currentTab.items.indexOf(item) + 1}</div>
                    <button className="delete-item-btn" onClick={() => removeContentItem(activeTab, item.id)} title="Sil"><FiTrash2 /></button>
                  </div>
                  <div className="item-form">
                    <div className="form-group">
                      <label>Fotoğraf Başlığı</label>
                      <input type="text" placeholder="Fotoğraf başlığı" value={item.title} onChange={(e) => updateContentItem(activeTab, item.id, 'title', e.target.value)} />
                    </div>
                    <div className="form-group">
                      <label>Fotoğraf URL'si</label>
                      <input type="url" placeholder="Fotoğraf URL'si" value={item.url} onChange={(e) => updateContentItem(activeTab, item.id, 'url', e.target.value)} />
                    </div>
                  </div>
                </div>
              ))}
              {currentTab.items.length === 0 && (
                <div className="empty-state">
                  <div className="empty-icon"><FiImage size={48} /></div>
                  <h4>Henüz fotoğraf yok</h4>
                  <p>Bu sekme için ilk fotoğrafınızı ekleyin</p>
                  <button className="add-first-btn" onClick={() => addContentItem(activeTab)}><FiPlus />İlk Fotoğrafı Ekle</button>
                </div>
              )}
            </div>
          </div>
        )}

        {!currentTab.enabled && (
          <div className="disabled-state">
            <div className="disabled-icon"><FiImage size={48} /></div>
            <h4>Galeri sekmesi pasif</h4>
            <p>Bu sekmeyi aktif hale getirmek için yukarıdaki toggle'ı kullanın</p>
            <button className="enable-btn" onClick={() => handleTabToggle(activeTab)}>Sekmeyi Aktif Et</button>
          </div>
        )}
      </div>
    </div>
  );
});

GalleryTab.displayName = 'GalleryTab';
export default GalleryTab; 