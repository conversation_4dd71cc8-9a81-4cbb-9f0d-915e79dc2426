import { memo, useEffect } from 'react';
import { 
  FiArrowRight,
  FiPieChart,
  FiSettings,
  FiUserPlus,
  FiLink,
  FiEdit, 
  FiShare2,
  FiZap,
  FiCommand,
  FiCode,
  FiTrendingUp
} from 'react-icons/fi';
import { motion } from 'framer-motion';

const WelcomeTab = memo(({ setShowWelcome }) => {
  useEffect(() => {
    // Add CSS animations to head
    const style = document.createElement('style');
    style.textContent = `
      @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
      }
      
      @keyframes pulse {
        0% { opacity: 0.5; }
        50% { opacity: 1; }
        100% { opacity: 0.5; }
      }
      
      .gradient-text {
        background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
        background-size: 200% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientMove 8s ease infinite;
      }
      
      .glass-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }
      
      .glass-card:hover {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.15);
        transform: translateY(-4px);
        transition: all 0.3s ease;
      }
      
      .floating-particle {
        animation: float 15s infinite linear;
      }
      
      .hero-bg {
        background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 50%, #0f172a 100%);
        position: relative;
        overflow: hidden;
      }
      
      .hero-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
        pointer-events: none;
      }
      
      .glow-button {
        position: relative;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        border: none;
        border-radius: 12px;
        padding: 16px 32px;
        color: white;
        font-weight: 600;
        font-size: 18px;
        cursor: pointer;
        overflow: hidden;
        transition: all 0.3s ease;
      }
      
      .glow-button:hover {
        transform: scale(1.05);
        box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
      }
      
      .glow-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }
      
      .glow-button:hover::before {
        left: 100%;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  const features = [
    {
      icon: <FiCode className="w-6 h-6" />,
      title: 'Modern Arayüz',
      description: 'İçeriklerinizi yönetmek için tasarlanmış temiz ve sezgisel arayüz.',
      gradient: 'linear-gradient(135deg, #3b82f6, #1e40af)'
    },
    {
      icon: <FiZap className="w-6 h-6" />,
      title: 'Hızlı Düzenleme',
      description: 'Profilinizi ve içeriklerinizi saniyeler içinde güncelleyin.',
      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'
    },
    {
      icon: <FiTrendingUp className="w-6 h-6" />,
      title: 'Detaylı Analizler',
      description: 'Ziyaretçi verilerinizi gerçek zamanlı olarak takip edin.',
      gradient: 'linear-gradient(135deg, #10b981, #059669)'
    },
    {
      icon: <FiSettings className="w-6 h-6" />,
      title: 'Tam Kontrol',
      description: 'Her detayı istediğiniz gibi özelleştirin ve yönetin.',
      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
    }
  ];

  const quickStartSteps = [
    {
      step: 1,
      title: 'Profilini Oluştur',
      description: 'Kişisel bilgilerini girerek ve bir profil fotoğrafı yükleyerek başla.',
      icon: <FiUserPlus className="w-6 h-6" />,
      gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'
    },
    {
      step: 2,
      title: 'Linklerini Ekle',
      description: 'Sosyal medya hesaplarını, web siteni ve diğer önemli bağlantıları ekle.',
      icon: <FiLink className="w-6 h-6" />,
      gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'
    },
    {
      step: 3,
      title: 'Görünümü Kişiselleştir',
      description: 'Profilinin renklerini, yazı tiplerini ve düzenini kendine göre ayarla.',
      icon: <FiEdit className="w-6 h-6" />,
      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
    },
    {
      step: 4,
      title: 'Paylaş!',
      description: 'Eşsiz Zylome linkini kopyala ve hedef kitlenle paylaşmaya başla.',
      icon: <FiShare2 className="w-6 h-6" />,
      gradient: 'linear-gradient(135deg, #10b981, #059669)'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2, delayChildren: 0.3 }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100, damping: 12 }
    }
  };

  const cardVariants = {
    hidden: { y: 30, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 12 }
    },
    hover: {
      y: -10,
      scale: 1.05,
      transition: { type: 'spring', stiffness: 400, damping: 15 }
    }
  };

  return (
    <div 
      className="hero-bg" 
      style={{ 
        minHeight: '100vh',
        color: '#f1f5f9',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}
    >
      {/* Floating Particles */}
      <div style={{ position: 'absolute', inset: 0, overflow: 'hidden', pointerEvents: 'none' }}>
        {[...Array(25)].map((_, i) => (
          <div
            key={i}
            className="floating-particle"
            style={{
              position: 'absolute',
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '50%',
              animationDelay: `${Math.random() * 15}s`,
              animationDuration: `${Math.random() * 10 + 15}s`
            }}
          />
        ))}
      </div>

      <div style={{ 
        position: 'relative', 
        zIndex: 10, 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '96px 24px' 
      }}>
        {/* Hero Section */}
        <motion.div 
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          style={{ marginBottom: '128px' }}
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', 
            gap: '48px', 
            alignItems: 'center' 
          }}>
            {/* Sol Taraf - Başlık */}
            <motion.div variants={itemVariants}>
              <div 
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  background: 'rgba(59, 130, 246, 0.1)',
                  backdropFilter: 'blur(10px)',
                  color: '#93c5fd',
                  fontSize: '14px',
                  fontWeight: '500',
                  padding: '8px 20px',
                  borderRadius: '20px',
                  marginBottom: '32px',
                  border: '1px solid rgba(59, 130, 246, 0.2)'
                }}
              >
                <span 
                  style={{
                    width: '8px',
                    height: '8px',
                    backgroundColor: '#60a5fa',
                    borderRadius: '50%',
                    marginRight: '8px',
                    animation: 'pulse 2s infinite'
                  }}
                />
                ZYLOME Yönetim Paneline Hoş Geldiniz
          </div>
          
              <h1 style={{ 
                fontSize: 'clamp(48px, 8vw, 84px)',
                fontWeight: '900',
                color: 'white',
                marginBottom: '24px',
                lineHeight: '1.1',
                letterSpacing: '-0.025em'
              }}>
                Dijital Kimliğiniz{' '}
                <span className="gradient-text">
                  Yeniden Tasarlandı
                </span>
              </h1>
              
              <p style={{ 
                fontSize: '20px',
                color: '#94a3b8',
                marginBottom: '32px',
                lineHeight: '1.6'
              }}>
                Zylome ile dijital varlığınızı yönetmek artık çok daha kolay. Modern, hızlı ve tamamen size özel.
              </p>
              
              <button
                onClick={() => setShowWelcome(false)}
                className="glow-button"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <span style={{ position: 'relative', zIndex: 10 }}>Başlayalım</span>
                <FiArrowRight style={{ width: '20px', height: '20px', position: 'relative', zIndex: 10 }} />
              </button>
            </motion.div>

            {/* Sağ Taraf - Feature Cards */}
            <motion.div 
              variants={containerVariants}
              style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(2, 1fr)', 
                gap: '16px' 
              }}
            >
              {features.slice(0, 4).map((feature, index) => (
                <motion.div
                  key={index}
                  variants={cardVariants}
                  whileHover="hover"
                  className="glass-card"
                  style={{
                    padding: '24px',
                    borderRadius: '24px',
                    position: 'relative',
                    height: '160px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    transform: index % 2 === 1 ? 'translateY(32px)' : 'translateY(0)',
                    cursor: 'pointer'
                  }}
                >
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      background: feature.gradient,
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white'
                    }}
                  >
                    {feature.icon}
                  </div>
                  <div>
                    <h3 style={{ 
                      fontSize: '18px', 
                      fontWeight: '700', 
                      color: 'white', 
                      marginBottom: '4px' 
                    }}>
                      {feature.title}
                    </h3>
                    <p style={{ 
                      fontSize: '14px', 
                      color: '#94a3b8', 
                      lineHeight: '1.4' 
                    }}>
                      {feature.description.split(' ').slice(0, 4).join(' ')}...
                    </p>
            </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </motion.div>

        {/* Quick Start Guide */}
        <div style={{ marginBottom: '128px' }}>
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.6 }}
            style={{ textAlign: 'center', marginBottom: '64px' }}
          >
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              background: 'rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(10px)',
              padding: '4px 16px',
              borderRadius: '20px',
              marginBottom: '24px',
              gap: '12px'
            }}>
              <span style={{ 
                height: '4px', 
                width: '48px', 
                background: 'linear-gradient(90deg, #3b82f6, #8b5cf6)', 
                borderRadius: '2px' 
              }} />
              <span style={{ color: '#94a3b8', fontWeight: '500' }}>BAŞLANGIÇ KILAVUZU</span>
              <span style={{ 
                height: '4px', 
                width: '48px', 
                background: 'linear-gradient(90deg, #8b5cf6, #ec4899)', 
                borderRadius: '2px' 
              }} />
            </div>
            <h2 style={{ 
              fontSize: 'clamp(36px, 6vw, 60px)', 
              fontWeight: '900', 
              color: 'white', 
              marginBottom: '24px' 
            }}>
              Hızlı Başlangıç
            </h2>
            <p style={{ 
              fontSize: '20px', 
              color: '#94a3b8', 
              maxWidth: '600px', 
              margin: '0 auto' 
            }}>
              Dört kolay adımda profesyonel profilinizi oluşturun.
            </p>
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={containerVariants}
            style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
              gap: '24px' 
            }}
          >
            {quickStartSteps.map((step) => (
              <motion.div
                key={step.step}
                variants={cardVariants}
                whileHover="hover"
                className="glass-card"
                style={{
                  padding: '32px',
                  borderRadius: '16px',
                  position: 'relative',
                  cursor: 'pointer'
                }}
              >
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    background: step.gradient,
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: '24px',
                    color: 'white'
                  }}
                >
                  {step.icon}
            </div>
                <div style={{ 
                  fontSize: '48px', 
                  fontWeight: '900', 
                  color: 'rgba(255, 255, 255, 0.1)', 
                  marginBottom: '16px' 
                }}>
                  0{step.step}
          </div>
                <h3 style={{ 
                  fontSize: '20px', 
                  fontWeight: '700', 
                  color: 'white', 
                  marginBottom: '12px' 
                }}>
                  {step.title}
                </h3>
                <p style={{ 
                  color: '#94a3b8', 
                  lineHeight: '1.6' 
                }}>
                  {step.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Features Section */}
        <div style={{ marginBottom: '128px' }}>
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.6 }}
            style={{ textAlign: 'center', marginBottom: '64px' }}
          >
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              background: 'rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(10px)',
              padding: '4px 16px',
              borderRadius: '20px',
              marginBottom: '24px',
              gap: '12px'
            }}>
              <span style={{ 
                height: '4px', 
                width: '48px', 
                background: 'linear-gradient(90deg, #3b82f6, #8b5cf6)', 
                borderRadius: '2px' 
              }} />
              <span style={{ color: '#94a3b8', fontWeight: '500' }}>ÖZELLİKLER</span>
              <span style={{ 
                height: '4px', 
                width: '48px', 
                background: 'linear-gradient(90deg, #8b5cf6, #ec4899)', 
                borderRadius: '2px' 
              }} />
      </div>
            <h2 style={{ 
              fontSize: 'clamp(36px, 6vw, 60px)', 
              fontWeight: '900', 
              color: 'white', 
              marginBottom: '24px' 
            }}>
              Güçlü Özellikler
            </h2>
            <p style={{ 
              fontSize: '20px', 
              color: '#94a3b8', 
              maxWidth: '600px', 
              margin: '0 auto' 
            }}>
              İhtiyacınız olan her şey, tek bir yerde.
            </p>
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={containerVariants}
            style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', 
              gap: '24px' 
            }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={cardVariants}
                whileHover="hover"
                className="glass-card"
                style={{
                  padding: '32px',
                  borderRadius: '16px',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '24px',
                  cursor: 'pointer'
                }}
              >
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    background: feature.gradient,
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                    color: 'white'
                  }}
                >
                  {feature.icon}
                </div>
                <div>
                  <h3 style={{ 
                    fontSize: '20px', 
                    fontWeight: '700', 
                    color: 'white', 
                    marginBottom: '12px' 
                  }}>
                    {feature.title}
                  </h3>
                  <p style={{ 
                    color: '#94a3b8', 
                    lineHeight: '1.6' 
                  }}>
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
      </div>

        {/* Footer */}
        <div style={{ 
          position: 'relative', 
          padding: '40px 0', 
          textAlign: 'center' 
        }}>
          <div style={{ 
            position: 'absolute', 
            top: 0, 
            left: '50%', 
            transform: 'translateX(-50%)', 
            height: '1px', 
            width: '75%', 
            background: 'linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent)' 
          }} />
          <p style={{ 
            fontSize: '14px', 
            color: '#64748b' 
          }}>
            Zylome ile dijital varlığınızı yönetmeye başlayın
          </p>
        </div>
      </div>
    </div>
  );
});

WelcomeTab.displayName = 'WelcomeTab';

export default WelcomeTab; 