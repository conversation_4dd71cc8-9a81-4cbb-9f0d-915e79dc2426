/* Authentication Pages Styles */

.auth-container {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing);
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.05;
}

.auth-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

.auth-card {
  position: relative;
  z-index: 1;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  width: 100%;
  max-width: 400px;
  box-shadow: var(--shadow-lg);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.auth-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--accent-primary);
  font-size: 2rem;
  font-weight: 700;
}

.auth-logo svg {
  font-size: 2.5rem;
}

.auth-header h1 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
}

.auth-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9375rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.auth-form .form-group {
  margin-bottom: 0;
}

.auth-form .form-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.875rem;
}

.auth-form .form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

.auth-form .form-group input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.auth-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: 0.875rem 1rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: var(--transition);
}

.auth-submit:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.auth-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-submit svg {
  flex-shrink: 0;
}

.auth-footer {
  margin-top: var(--spacing-xl);
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.auth-footer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.auth-footer a {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.auth-footer a:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-lg) 0;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--border-color);
}

.auth-divider span {
  padding: 0 var(--spacing);
}

.auth-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius);
  color: var(--danger);
  padding: var(--spacing);
  font-size: 0.875rem;
  margin-bottom: var(--spacing);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.auth-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--radius);
  color: var(--success);
  padding: var(--spacing);
  font-size: 0.875rem;
  margin-bottom: var(--spacing);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.auth-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-muted);
  flex-direction: column;
  gap: var(--spacing);
}

.auth-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.auth-loading p {
  margin: 0;
  font-size: 0.875rem;
}

/* Social Login Buttons */
.social-login {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.social-btn:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.social-btn svg {
  flex-shrink: 0;
  font-size: 1.25rem;
}

.social-btn.google {
  border-color: #db4437;
  color: #db4437;
}

.social-btn.google:hover {
  background: #db4437;
  color: white;
}

.social-btn.github {
  border-color: #333;
  color: #333;
}

.social-btn.github:hover {
  background: #333;
  color: white;
}

/* Admin Role Toggle */
.role-toggle {
  display: flex;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden;
  margin-bottom: var(--spacing);
}

.role-option {
  flex: 1;
  padding: var(--spacing) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
}

.role-option.active {
  background: var(--accent-primary);
  color: white;
}

.role-option:hover:not(.active) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Password Strength Indicator */
.password-strength {
  margin-top: var(--spacing-xs);
  display: flex;
  gap: var(--spacing-xs);
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  transition: var(--transition);
}

.strength-bar.weak {
  background: var(--danger);
}

.strength-bar.medium {
  background: var(--warning);
}

.strength-bar.strong {
  background: var(--success);
}

.password-requirements {
  margin-top: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.requirement {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.requirement.met {
  color: var(--success);
}

.requirement svg {
  flex-shrink: 0;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--spacing-sm);
  }

  .auth-card {
    padding: var(--spacing-xl);
  }

  .auth-logo {
    font-size: 1.75rem;
  }

  .auth-logo svg {
    font-size: 2rem;
  }

  .auth-header h1 {
    font-size: 1.5rem;
  }

  .auth-form {
    gap: var(--spacing);
  }
} 