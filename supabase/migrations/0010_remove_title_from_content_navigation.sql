-- Remove only the title column from content_navigation table since we're using fixed tab names
-- Keep tab_order, enabled, description columns for user customization
ALTER TABLE content_navigation DROP COLUMN IF EXISTS title;

-- The table still maintains:
-- - tab_order: for custom tab ordering per user
-- - enabled: for enabling/disabling tabs per user  
-- - description: for future use
-- - user_id, tab_id: for user-specific configurations 