/* Profile Page Styles */

.profile-not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  padding: var(--spacing);
}

.not-found-content {
  text-align: center;
  color: var(--text-muted);
  max-width: 400px;
}

.not-found-content svg {
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.not-found-content h2 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-secondary);
  font-size: 1.5rem;
  font-weight: 600;
}

.not-found-content p {
  margin: 0;
  font-size: 0.9375rem;
  line-height: 1.6;
}

.modern-profile-container {
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
}

/* Profile Background */
.profile-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400px;
  z-index: 0;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.1;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(67, 56, 202, 0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

/* Profile Header */
.profile-header {
  position: relative;
  z-index: 1;
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
  border-bottom: 1px solid var(--border-color);
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(67, 56, 202, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.profile-header-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xl);
  text-align: center;
  position: relative;
  z-index: 1;
}

.modern-profile-avatar {
  position: relative;
  flex-shrink: 0;
  line-height: normal;
  margin-top: 15px;
}

.modern-profile-avatar img,
.avatar-placeholder {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  border: 5px solid white;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.modern-profile-avatar:hover img,
.modern-profile-avatar:hover .avatar-placeholder {
  transform: scale(1.05);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
}

.avatar-placeholder {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3.5rem;
  font-weight: 700;
}

.profile-info {
  flex: 1;
  min-width: 0;
  width: 100%;
}

.profile-name {
  margin: 0 auto var(--spacing-sm) auto;
  color: var(--text-primary);
  font-size: 2.75rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.025em;
  text-align: center;
}

.profile-slogan {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-muted);
  font-size: 1.125rem;
  font-style: italic;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  justify-content: center;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.meta-item svg {
  color: var(--accent-primary);
  flex-shrink: 0;
}

/* Social Links Section */
.modern-social-section {
  position: relative;
  z-index: 1;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 10px;
}

.social-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
}

.social-links-modern {
  display: flex;
  gap: var(--spacing);
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: var(--spacing-xs) 0;
  justify-content: flex-start;
}

.social-links-modern::-webkit-scrollbar {
  display: none;
}

/* Scroll fade effects */
.scroll-fade-left,
.scroll-fade-right {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 40px;
  pointer-events: none;
  z-index: 2;
}

.scroll-fade-left {
  left: var(--spacing);
  background: linear-gradient(to right, var(--bg-primary), transparent);
  display: none;
}

.scroll-fade-right {
  right: var(--spacing);
  background: linear-gradient(to left, var(--bg-primary), transparent);
}

.social-link-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  flex-shrink: 0;
  height: 50px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--social-color, var(--accent-primary));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.social-link-modern:hover::before {
  opacity: 1;
}

.social-link-modern:hover {
  color: white;
  border-color: var(--social-color, var(--accent-primary));
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.social-link-modern svg {
  position: relative;
  z-index: 1;
}

/* WhatsApp Section */
.whatsapp-section {
  position: relative;
  z-index: 1;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
}

.whatsapp-btn-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing);
  background: #25D366;
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing) var(--spacing-xl);
  font-weight: 600;
  font-size: 1.125rem;
  transition: var(--transition);
  max-width: 400px;
  margin: 0 auto;
}

.whatsapp-btn-modern:hover {
  background: #128C7E;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Tabs Navigation */
.modern-tabs-container {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs-nav {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem var(--spacing);
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-nav::-webkit-scrollbar {
  display: none;
}

.tab-btn-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 1.05rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  white-space: nowrap;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.tab-btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.tab-btn-modern:hover::before {
  left: 100%;
}

.tab-btn-modern::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-btn-modern:hover {
  color: white;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.tab-btn-modern:hover::after {
  opacity: 1;
}

.tab-btn-modern.active {
  color: #6366f1;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(99, 102, 241, 0.08));
  border-color: rgba(99, 102, 241, 0.4);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.25), 0 0 0 1px rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.tab-btn-modern.active::after {
  opacity: 1;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), transparent);
}

.tab-btn-modern svg {
  flex-shrink: 0;
}

/* Tab Content */
.modern-tab-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing);
  min-height: 400px;
}

/* Contact Section */
.contact-section {
  padding: var(--spacing-lg) 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.contact-card {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);
  cursor: pointer;
}

.contact-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.contact-card.whatsapp {
  border-color: #25D366;
}

.contact-card.whatsapp:hover {
  background: rgba(37, 211, 102, 0.05);
  border-color: #25D366;
}

.contact-card.email {
  border-color: var(--info);
}

.contact-card.email:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: var(--info);
}

.contact-card.location {
  border-color: var(--warning);
}

.contact-card.location:hover {
  background: rgba(245, 158, 11, 0.05);
  border-color: var(--warning);
}

.contact-card span {
  flex: 1;
  font-weight: 600;
}

.contact-card svg:first-child {
  color: inherit;
  flex-shrink: 0;
}

.contact-card svg:last-child {
  color: var(--text-muted);
  flex-shrink: 0;
}

/* Loading States */
.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--text-secondary);
}

.profile-loading p {
  margin-top: var(--spacing);
  font-size: 0.875rem;
}

.profile-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
}

.not-found-content {
  text-align: center;
  color: var(--text-secondary);
  max-width: 400px;
  padding: var(--spacing-xl);
}

.not-found-content svg {
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.not-found-content h2 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
}

.not-found-content p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.portfolio-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition);
}

.portfolio-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.portfolio-image {
  width: 100%;
  height: 200px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  position: relative;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.portfolio-content {
  padding: var(--spacing);
}

.portfolio-title {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.portfolio-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Blog Grid */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.blog-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition);
}

.blog-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.blog-image {
  width: 100%;
  height: 200px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  position: relative;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blog-content {
  padding: var(--spacing-lg);
}

.blog-title {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
}

.blog-excerpt {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.blog-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-muted);
  font-size: 0.75rem;
}

.blog-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.blog-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.blog-status.published {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.blog-status.draft {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-muted);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing);
  margin-top: var(--spacing-lg);
}

.gallery-item {
  aspect-ratio: 1;
  background: var(--bg-tertiary);
  border-radius: var(--radius);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
}

.gallery-item:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Stats Display */
.stats-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.stat-item svg {
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-header-content {
    gap: 5px;
  }

  .modern-profile-avatar img,
  .avatar-placeholder {
    width: 120px;
    height: 120px;
  }

  .profile-name {
    font-size: 2.25rem;
  }

  .tabs-nav {
    padding: 1rem var(--spacing-sm);
    gap: 0.75rem;
  }

  .modern-tab-content {
    padding: var(--spacing) var(--spacing-sm);
  }

  .portfolio-grid,
  .blog-grid {
    grid-template-columns: 1fr;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-display {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }

  .social-container {
    padding: 0 var(--spacing-sm);
  }

  .scroll-fade-left {
    left: var(--spacing-sm);
  }

  .scroll-fade-right {
    right: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .modern-profile-avatar img,
  .avatar-placeholder {
    width: 120px;
    height: 120px;
  }

  .profile-name {
    font-size: 2rem;
  }

  .profile-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .tab-btn-modern {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 12px;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .stats-display {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--spacing);
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .social-container {
    padding: 0 var(--spacing-xs);
  }

  .scroll-fade-left {
    left: var(--spacing-xs);
  }

  .scroll-fade-right {
    right: var(--spacing-xs);
  }
}

/* Share Button */
.share-button {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 100;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);
  color: var(--text-secondary);
}

.share-button:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* Share Popup */
.share-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing);
}

.share-popup {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 400px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: popupEnter 0.3s ease;
}

@keyframes popupEnter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.share-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.share-popup-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.share-popup-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.share-popup-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.share-options {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing);
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-lg) var(--spacing);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.share-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.share-option.whatsapp:hover {
  background: rgba(37, 211, 102, 0.1);
  border-color: #25D366;
  color: #25D366;
}

.share-option.telegram:hover {
  background: rgba(0, 136, 204, 0.1);
  border-color: #0088CC;
  color: #0088CC;
}

.share-option.twitter:hover {
  background: rgba(29, 161, 242, 0.1);
  border-color: #1DA1F2;
  color: #1DA1F2;
}

.share-option.facebook:hover {
  background: rgba(24, 119, 242, 0.1);
  border-color: #1877F2;
  color: #1877F2;
}

.share-option.linkedin:hover {
  background: rgba(10, 102, 194, 0.1);
  border-color: #0A66C2;
  color: #0A66C2;
}

.share-link-section {
  padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.share-link-input {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing);
}

.share-link-input input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.copy-button {
  padding: var(--spacing-sm) var(--spacing);
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.copy-button:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.copy-button.copied {
  background: var(--success);
}

@media (max-width: 768px) {
  .share-button {
    top: var(--spacing);
    right: var(--spacing);
    width: 45px;
    height: 45px;
  }

  .share-popup {
    margin: var(--spacing);
    max-width: none;
  }

  .share-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .share-link-input {
    flex-direction: column;
  }

  .copy-button {
    justify-content: center;
  }
}

/* Action Buttons Section */
.action-buttons-section {
    padding: 0 var(--spacing);
    margin: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 100%;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 45px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.whatsapp-btn:hover {
    background: rgba(37, 211, 102, 0.15);
    color: #25D366;
    border-color: rgba(37, 211, 102, 0.3);
}

.phone-btn:hover {
    background: rgba(79, 172, 254, 0.15);
    color: #4facfe;
    border-color: rgba(79, 172, 254, 0.3);
}

.location-btn:hover {
    background: rgba(255, 107, 107, 0.15);
    color: #ff6b6b;
    border-color: rgba(255, 107, 107, 0.3);
}

@media (max-width: 768px) {
    .action-buttons {
        gap: 0.75rem;
    }
    
    .action-btn {
        height: 40px;
    }
} 