import React, { useState, useEffect, Suspense, lazy, memo } from 'react';
import Sidebar from '../../components/user/Sidebar/Sidebar';
import { useSidebar } from '../../contexts/SidebarContext';
import { useNavigate, Outlet, useLocation } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';

import { 
  FiArrowLeft, FiFileText, FiImage, FiVideo, 
  FiHelpCircle, FiPhone, FiGrid, 
  FiInfo, FiX, FiUsers, FiBook, FiHeadphones,
  FiLayers, 
  FiUser, FiActivity, FiLogOut
} from 'react-icons/fi';
import '../../../src/styles/pages/content-management.css';

// Lazy-loaded components
const WelcomeTab = lazy(() => import('./components/WelcomeTab'));
const OverviewTab = lazy(() => import('./components/OverviewTab'));
const VideoTab = lazy(() => import('./components/VideoTab'));
const ContactTab = lazy(() => import('./components/ContactTab'));
const CorporateTab = lazy(() => import('./components/CorporateTab'));
// Not: CatalogTab ve PodcastTab bileşenleri henüz oluşturulmamış


// Tab yükleme skeleti
const TabLoadingSkeleton = memo(() => (
  <div className="content-main-area">
    <div className="content-header">
      <div className="content-header-info">
        <div className="content-icon" style={{ backgroundColor: '#f3f4f6', animation: 'pulse 2s infinite' }}>
          <div style={{ width: '24px', height: '24px', backgroundColor: '#e5e7eb', borderRadius: '4px' }}></div>
        </div>
        <div className="content-title">
          <div style={{ width: '200px', height: '24px', backgroundColor: '#e5e7eb', borderRadius: '4px', marginBottom: '8px' }}></div>
          <div style={{ width: '300px', height: '16px', backgroundColor: '#f3f4f6', borderRadius: '4px' }}></div>
        </div>
      </div>
      <div style={{ width: '80px', height: '40px', backgroundColor: '#e5e7eb', borderRadius: '20px' }}></div>
    </div>
    <div className="content-body">
      <div style={{ width: '100%', height: '200px', backgroundColor: '#f3f4f6', borderRadius: '12px', animation: 'pulse 2s infinite' }}></div>
    </div>
  </div>
));

TabLoadingSkeleton.displayName = 'TabLoadingSkeleton';

// Sekme tanımları
const TABS = [
  { 
    id: 'links', 
    name: 'Bağlantılar', 
    icon: FiGrid,
    color: '#3b82f6',
    description: 'Projelerinizi ve referanslarınızı sergileyebileceğiniz bölüm'
  },
  { 
    id: 'blog', 
    name: 'Blog', 
    icon: FiFileText,
    color: '#3b82f6',
    description: 'Yazılarınızı paylaşabileceğiniz bölüm'
  },
  { 
    id: 'gallery', 
    name: 'Portföyler', 
    icon: FiImage,
    color: '#ec4899',
    description: 'Fotoğraf albümlerinizi ve görsellerinizi sergileyin'
  },
  { 
    id: 'video', 
    name: 'Video', 
    icon: FiVideo,
    color: '#10b981',
    description: 'Video içeriklerinizi paylaşın'
  },
  { 
    id: 'faq', 
    name: 'SSS', 
    icon: FiHelpCircle,
    color: '#8b5cf6',
    description: 'Sık sorulan soruları cevaplayabileceğiniz bölüm'
  },
  { 
    id: 'contact', 
    name: 'İletişim', 
    icon: FiPhone,
    color: '#10b981',
    description: 'İletişim bilgilerinizi paylaşabileceğiniz bölüm'
  },
  { 
    id: 'corporate', 
    name: 'Kurumsal', 
    icon: FiUsers,
    color: '#f59e0b',
    description: 'Kurumsal bilgilerinizi paylaşabileceğiniz bölüm'
  },
  { 
    id: 'catalog', 
    name: 'Katalog', 
    icon: FiBook,
    color: '#ec4899',
    description: 'Ürün ve hizmetlerinizi sergileyebileceğiniz bölüm'
  },
  { 
    id: 'podcast', 
    name: 'Podcast', 
    icon: FiHeadphones,
    color: '#14b8a6',
    description: 'Podcast içeriklerinizi paylaşabileceğiniz bölüm'
  }
];



const ContentManagement = () => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState(null);
  const [showWelcome, setShowWelcome] = useState(false);
  
  // SidebarContext'ten gelen state ve fonksiyonları kullan
  const { 
    activeTab, 
    setActiveTab, 
    tabs, 
    toggleTab,
    isTabEnabled
  } = useSidebar();

  const navigate = useNavigate();
  const location = useLocation();

  // Content renderer - route'lara göre content render eder
  const ContentRenderer = () => {
    // Route tabanlı sistemler için Outlet render et
    if (location.pathname.includes('/blogs') || 
        location.pathname.includes('/faqs') || 
        location.pathname.includes('/links') || 
        location.pathname.includes('/galleries') ||
        location.pathname.includes('/videos') ||
        location.pathname.includes('/catalogs') ||
        location.pathname.includes('/podcasts')) {
      return (
        <Outlet />
      );
    }
    
    // Eğer sadece /dashboard ise, Dashboard component'ini göster
    if (location.pathname === '/dashboard') {
      const Dashboard = React.lazy(() => import('../../components/user/Dashboard'));
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <Dashboard />
        </Suspense>
      );
    }
    
    // Diğer tab'lar için eski sistemi kullan
    return renderTabContent();
  };



  // Resim silme fonksiyonu
  const deleteImageFromStorage = async (imageUrl) => {
    try {
      if (!imageUrl || !imageUrl.includes('supabase')) return;
      if (!user || !user.id) {
        console.warn('User bilgileri yok, resim silme atlandı');
        return;
      }
      
      // URL'den dosya yolunu çıkar
      const urlParts = imageUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const userFolder = user.id;
      const filePath = `${userFolder}/${fileName}`;
      
      console.log('Resim siliniyor:', { fileName, filePath, userFolder });
      
      const { error } = await supabase.storage
        .from('blog')
        .remove([filePath]);
      
      if (error) {
        console.warn('Resim silme hatası:', error);
      } else {
        console.log('Resim başarıyla silindi:', filePath);
      }
    } catch (error) {
      console.warn('Resim silme işlemi başarısız:', error);
    }
  };

  // Not: contentData ve tabOrder artık SidebarContext'ten geliyor

  // Not: Sürükle-bırak state'leri artık SidebarContext'te yönetiliyor
  


  // Özel alanlar (custom sections) için state - corporate sekmesi için
  const [customSections, setCustomSections] = useState([]);
  
  // Not: showWelcome artık yukarıda tanımlandı





  useEffect(() => {
    console.log('🔄 ContentManagement useEffect triggered, pathname:', location.pathname);
    // İlk yükleme
    checkUserAndLoadContent();
  }, [location.pathname]); // URL değiştiğinde tekrar çalıştır

  // Auth state değişikliklerini ayrı bir useEffect'te dinle
  useEffect(() => {
    console.log('🔄 Setting up auth listener');
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state change:', event, session?.user ? 'User exists' : 'No user');
        if (event === 'SIGNED_OUT') {
          console.log('👋 User signed out, redirecting');
          navigate('/');
        }
        // SIGNED_IN durumunda checkUserAndLoadContent zaten çalışacak
      }
    );

    return () => {
      console.log('🧹 Cleaning up auth listener');
      subscription.unsubscribe();
    };
  }, []); // Sadece component mount'ta çalıştır

  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 3000);
  };



  const AlertComponent = ({ message }) => {
    if (!message || !message.text) return null;

    const getAlertConfig = () => {
      switch (message.type) {
        case 'success':
          return {
            icon: FiCheckCircle,
            className: 'alert-success',
            bgColor: 'rgba(34, 197, 94, 0.1)',
            borderColor: 'rgba(34, 197, 94, 0.3)',
            iconColor: '#22c55e'
          };
        case 'error':
          return {
            icon: FiX,
            className: 'alert-error',
            bgColor: 'rgba(239, 68, 68, 0.1)',
            borderColor: 'rgba(239, 68, 68, 0.3)',
            iconColor: '#ef4444'
          };
        case 'warning':
          return {
            icon: FiAlertTriangle,
            className: 'alert-warning',
            bgColor: 'rgba(245, 158, 11, 0.1)',
            borderColor: 'rgba(245, 158, 11, 0.3)',
            iconColor: '#f59e0b'
          };
        case 'info':
        default:
          return {
            icon: FiInfo,
            className: 'alert-info',
            bgColor: 'rgba(59, 130, 246, 0.1)',
            borderColor: 'rgba(59, 130, 246, 0.3)',
            iconColor: '#3b82f6'
          };
      }
    };

    const config = getAlertConfig();
    const IconComponent = config.icon;

    return (
      <div 
        className={`modern-alert ${config.className}`}
        style={{
          background: config.bgColor,
          borderColor: config.borderColor
        }}
      >
        <div className="alert-content">
          <IconComponent 
            size={20} 
            style={{ color: config.iconColor }} 
            className="alert-icon"
          />
          <span className="alert-text">{message.text}</span>
        </div>
        <button 
          className="alert-close"
          onClick={() => setMessage({ type: '', text: '' })}
          style={{ color: config.iconColor }}
        >
          <FiX size={18} />
        </button>
      </div>
    );
  };

  const loadCustomSections = async (userId) => {
    try {
      console.log('📂 Loading custom sections for user:', userId);
      const { data: sections, error } = await supabase
        .from('custom_sections')
        .select('*')
        .eq('profile_id', userId)
        .order('section_order', { ascending: true });

      console.log('📂 Custom sections result:', { sections, error });

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Custom sections error (not 404):', error);
        throw error;
      }

      console.log('✅ Custom sections loaded:', sections?.length || 0, 'items');
      setCustomSections(sections || []);
    } catch (error) {
      console.error('❌ Error loading custom sections:', error);
      // Custom sections yüklenemese bile devam et
      setCustomSections([]);
    }
  };

  const cleanupDuplicateNavigations = async (userId) => {
    try {
      // Duplicate kayıtları bul ve temizle
      const { data: allNav, error: fetchError } = await supabase
        .from('content_navigation')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: true });

      if (fetchError) throw fetchError;

      if (allNav && allNav.length > 0) {
        const seenTabs = new Set();
        const duplicatesToDelete = [];

        allNav.forEach(nav => {
          if (seenTabs.has(nav.tab_id)) {
            duplicatesToDelete.push(nav.id);
          } else {
            seenTabs.add(nav.tab_id);
          }
        });

        // Duplicate kayıtları sil
        if (duplicatesToDelete.length > 0) {
          const { error: deleteError } = await supabase
            .from('content_navigation')
            .delete()
            .in('id', duplicatesToDelete);

          if (deleteError) throw deleteError;
          console.log(`Cleaned up ${duplicatesToDelete.length} duplicate navigation records`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up duplicates:', error);
    }
  };

  const checkUserAndLoadContent = async () => {
    try {
      console.log('🔄 Loading user and content...');
      setLoading(true);

      // Önce session'ı kontrol et
      const { data: { session } } = await supabase.auth.getSession();
      console.log('📋 Session check:', session?.user ? 'User found' : 'No user');

      if (!session?.user) {
        console.log('❌ No user session, redirecting to home');
        navigate('/');
        return;
      }

      const user = session.user;
      console.log('👤 User ID:', user.id);

      // Admin kontrolü ve profil bilgilerini al
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log('👤 Profile fetch result:', { profile, profileError });

      if (profileError) {
        console.error('Profile fetch error:', profileError);
        // Profil bulunamazsa ana sayfaya yönlendir
        navigate('/');
        return;
      }

      if (profile?.role === 'admin') {
        console.log('🔒 Admin user, redirecting to login');
        navigate('/login');
        return;
      }

      // State'leri güncelle
      console.log('✅ Setting user and profile states');
      setUser(user);
      setProfile(profile);

      // Custom sections verilerini yükle (geçici olarak atlandı)
      console.log('📂 Loading custom sections...');
      try {
        await loadCustomSections(user.id);
      } catch (error) {
        console.warn('⚠️ Custom sections loading failed, continuing anyway:', error);
      }
      console.log('✅ Content loading completed');

    } catch (error) {
      console.error('❌ Error loading content:', error);
      navigate('/');
    } finally {
      console.log('🏁 Setting loading to false');
      setLoading(false);
    }
  };

  // Not: handleTabToggle fonksiyonu artık SidebarContext içinde yer alıyor

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      showMessage('error', 'Çıkış yapılırken hata oluştu');
    }
  };

  // Not: saveContentNavigation fonksiyonu artık SidebarContext içinde yer alıyor

  const handleSaveContent = async () => {
    if (!user) return;
    
    setSaving(true);
    try {
      // Sadece custom sections kaydet
      // Not: Content navigation artık SidebarContext tarafından yönetiliyor
      await saveCustomSections();
      
      showMessage('success', 'İçerik ayarları başarıyla kaydedildi!');
    } catch (error) {
      console.error('Error saving content:', error);
      showMessage('error', 'Kaydetme işlemi sırasında hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const saveCustomSections = async () => {
    if (!user?.id) return;

    try {
      for (const section of customSections) {
        if (typeof section.id === 'string' && section.id.includes('temp-')) {
          // Yeni section ekle
          const { data, error } = await supabase
            .from('custom_sections')
            .insert([{
              profile_id: user.id,
              title: section.title,
              description: section.description,
              section_order: section.order
            }])
            .select()
            .single();

          if (error) throw error;

          // Geçici ID'yi gerçek ID ile değiştir
          setCustomSections(prev => 
            prev.map(s => s.id === section.id ? { ...s, id: data.id } : s)
          );
        } else {
          // Mevcut section güncelle
          const { error } = await supabase
            .from('custom_sections')
            .update({
              title: section.title,
              description: section.description,
              section_order: section.order
            })
            .eq('id', section.id);

          if (error) throw error;
        }
      }
    } catch (error) {
      console.error('Error saving custom sections:', error);
      throw error;
    }
  };



  // Custom sections yönetimi fonksiyonları
  const addCustomSection = () => {
    const newSection = {
      id: `temp-${Date.now()}`, // Geçici ID ile işaretle
      title: 'Yeni Bölüm',
      description: '',
      order: customSections.length,
      profile_id: user?.id
    };
    setCustomSections(prev => [...prev, newSection]);
  };

  const updateCustomSection = (id, field, value) => {
    setCustomSections(prev => 
      prev.map(section => 
        section.id === id ? { ...section, [field]: value } : section
      )
    );
  };

  const deleteCustomSection = async (id) => {
    try {
      // Database'den sil (sadece gerçek ID'ler için)
      if (typeof id !== 'string' || !id.includes('temp-')) {
        const { error } = await supabase
          .from('custom_sections')
          .delete()
          .eq('id', id);

        if (error) throw error;
      }

      // State'den sil
      setCustomSections(prev => prev.filter(section => section.id !== id));
      showMessage('success', 'Özel alan silindi!');
    } catch (error) {
      console.error('Error deleting custom section:', error);
      showMessage('error', 'Özel alan silinirken hata oluştu.');
    }
  };

  const moveCustomSection = (id, direction) => {
    const currentIndex = customSections.findIndex(section => section.id === id);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= customSections.length) return;

    const newSections = [...customSections];
    [newSections[currentIndex], newSections[newIndex]] = [newSections[newIndex], newSections[currentIndex]];
    
    // Order değerlerini güncelle
    newSections.forEach((section, index) => {
      section.order = index;
    });

    setCustomSections(newSections);
  };

  // Not: Sürükle-bırak fonksiyonları artık SidebarContext içinde yer alıyor

  // Not: renderPodcastAddContent ve renderPodcastListContent fonksiyonları artık kullanılmıyor
  // Bu fonksiyonların yerine renderTabContent içinde geçici bir içerik gösteriliyor





  const renderTabContent = () => {
    // İlk giriş hoşgeldin mesajını göster
    if (showWelcome || !activeTab) {
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <WelcomeTab setActiveTab={setActiveTab} setShowWelcome={setShowWelcome} />
        </Suspense>
      );
    }

    // Genel bakış sayfası
    if (activeTab === 'overview') {
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <OverviewTab 
            tabs={tabs}
            setActiveTab={setActiveTab}
            setShowWelcome={setShowWelcome}
            navigate={navigate}
          />
        </Suspense>
      );
    }

    // Ana sekmeler - route tabanlı olanlar ContentRenderer'da handle ediliyor

    if (activeTab === 'video') {
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <VideoTab 
            tabs={tabs}
            activeTab={activeTab}
            toggleTab={toggleTab}
          />
        </Suspense>
      );
    }



    if (activeTab === 'catalog') {
      return (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: '#ec4899' }}>
                <FiBook />
              </div>
              <div className="content-title">
                <h2>Katalog</h2>
                <p>Ürün ve hizmetlerinizi sergileyebileceğiniz bölüm</p>
              </div>
            </div>
            <div className="content-actions">
              <button className="content-action-btn" onClick={() => toggleTab('catalog')}>
                {isTabEnabled('catalog') ? 'Pasif Yap' : 'Aktif Yap'}
              </button>
            </div>
          </div>
          <div className="content-body">
            <div className="empty-state">
              <div className="empty-icon">
                <FiBook size={48} />
              </div>
              <h4>Katalog bileşeni henüz oluşturulmamış</h4>
              <p>Bu özellik yakında eklenecektir</p>
            </div>
          </div>
        </div>
      );
    }

    if (activeTab === 'podcast') {
      return (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: '#14b8a6' }}>
                <FiHeadphones />
              </div>
              <div className="content-title">
                <h2>Podcast</h2>
                <p>Podcast içeriklerinizi paylaşabileceğiniz bölüm</p>
              </div>
            </div>
            <div className="content-actions">
              <button className="content-action-btn" onClick={() => toggleTab('podcast')}>
                {isTabEnabled('podcast') ? 'Pasif Yap' : 'Aktif Yap'}
              </button>
            </div>
          </div>
          <div className="content-body">
            <div className="empty-state">
              <div className="empty-icon">
                <FiHeadphones size={48} />
              </div>
              <h4>Podcast bileşeni henüz oluşturulmamış</h4>
              <p>Bu özellik yakında eklenecektir</p>
            </div>
          </div>
        </div>
      );
    }

    if (activeTab === 'contact') {
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <ContactTab 
            tabs={tabs}
            activeTab={activeTab}
            toggleTab={toggleTab}
          />
        </Suspense>
      );
    }

    if (activeTab === 'corporate') {
      return (
        <Suspense fallback={<TabLoadingSkeleton />}>
          <CorporateTab 
            tabs={tabs}
            activeTab={activeTab}
            toggleTab={toggleTab}
            customSections={customSections}
            addCustomSection={addCustomSection}
            updateCustomSection={updateCustomSection}
            deleteCustomSection={deleteCustomSection}
            moveCustomSection={moveCustomSection}
          />
        </Suspense>
      );
    }
    
    // Alt menü içerikleri - geçici olarak boş içerik gösteriyoruz
    if (activeTab === 'podcast-add' || activeTab === 'podcast-list') {
      return (
        <div className="content-main-area">
          <div className="content-header">
            <div className="content-header-info">
              <div className="content-icon" style={{ backgroundColor: '#14b8a6' }}>
                <FiHeadphones />
              </div>
              <div className="content-title">
                <h2>{activeTab === 'podcast-add' ? 'Podcast Ekle' : 'Podcast Listesi'}</h2>
                <p>Bu özellik henüz geliştirme aşamasındadır</p>
              </div>
            </div>
            <div className="content-actions">
              <button className="content-action-btn" onClick={() => setActiveTab('podcast')}>
                <FiArrowLeft /> Geri Dön
              </button>
            </div>
          </div>
          <div className="content-body">
            <div className="empty-state">
              <div className="empty-icon">
                <FiHeadphones size={48} />
              </div>
              <h4>Podcast yönetimi henüz oluşturulmamış</h4>
              <p>Bu özellik yakında eklenecektir</p>
            </div>
          </div>
        </div>
      );
    }

    // Varsayılan boş içerik
    return (
      <div className="content-main-area">
        <div className="empty-state">
          <div className="empty-icon">
            <FiLayers size={48} />
          </div>
          <h4>İçerik seçilmedi</h4>
          <p>Düzenlemek istediğiniz bir sekme seçin</p>
        </div>
      </div>
    );
  };

  // Debug için loading durumunu logla
  console.log('🔍 Render check:', { loading, hasUser: !!user, hasProfile: !!profile });

  if (loading) {
    return (
      <div className="content-management-loading">
        <div className="loading-spinner"></div>
        <p>İçerik yönetimi yükleniyor...</p>
      </div>
    );
  }

  if (!user || !profile) {
    console.log('❌ Missing user or profile, redirecting');
    navigate('/');
    return null;
  }

  return (
    <div className="content-management">
      {/* Header */}
      <header className="content-management-header">
        <div className="header-container">
          <div className="header-left">
            <div className="header-title">
              <h1>Dashboard</h1>
              <p>İçerik yönetimi ve profil ayarları</p>
            </div>
          </div>
          
          <div className="header-center">
            <div className="header-actions">
              <button 
                className="header-action-btn"
                onClick={() => navigate('/dashboard/user-profile')}
                title="Profil Düzenle"
              >
                <FiUser />
                <span>Profil Düzenle</span>
              </button>
              
              <button 
                className="header-action-btn"
                onClick={() => setActiveTab('overview')}
                title="İstatistikler"
              >
                <FiActivity />
                <span>İstatistikler</span>
              </button>
            </div>
          </div>
          
          <div className="header-right">
            <div className="user-welcome">
              <div className="user-avatar">
                {profile?.avatar_url ? (
                  <img src={profile.avatar_url} alt="Profile" />
                ) : (
                  <FiUser />
                )}
              </div>
              <div className="user-info">
                <span className="welcome-text">Hoşgeldin,</span>
                <span className="user-name">
                  {profile?.full_name || profile?.username || user?.email?.split('@')[0] || 'Kullanıcı'}
                </span>
              </div>
              <button 
                className="logout-btn"
                onClick={handleLogout}
                title="Çıkış Yap"
              >
                <FiLogOut />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Layout */}
      <div className="content-management-layout">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <main className="content-main">
          <AlertComponent message={message} />
          <ContentRenderer />
        </main>
      </div>

      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default ContentManagement;