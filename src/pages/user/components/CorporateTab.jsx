import { memo } from 'react';
import { FiSettings, FiBriefcase, FiPlus, FiTrash2, FiChevronUp, FiChevronDown } from 'react-icons/fi';

const CorporateTab = memo(({ 
  contentData, 
  activeTab, 
  handleContentChange, 
  handleTabToggle, 
  customSections,
  addCustomSection,
  updateCustomSection,
  deleteCustomSection,
  moveCustomSection
}) => {
  const currentTab = contentData[activeTab];
  const tabColor = '#6b7280';

  return (
    <div className="content-main-area">
      <div className="content-header">
        <div className="content-header-info">
          <div className="content-icon" style={{ backgroundColor: `${tabColor}20`, color: tabColor }}>
            <FiBriefcase size={24} />
          </div>
          <div className="content-title">
            <h2>{currentTab.title}</h2>
            <p>{currentTab.description}</p>
          </div>
        </div>
        <div className="content-toggle">
          <button className={`toggle-switch ${currentTab.enabled ? 'active' : ''}`} onClick={() => handleTabToggle(activeTab)}>
            <div className="toggle-slider"></div>
            <span className="toggle-label">{currentTab.enabled ? 'Aktif' : 'Pasif'}</span>
          </button>
        </div>
      </div>

      <div className="content-body">
        <div className="content-settings-section">
          <h3><FiSettings size={20} /> Genel Ayarlar</h3>
          <div className="settings-grid">
            <div className="form-group">
              <label>Sekme Başlığı</label>
              <input type="text" value={currentTab.title} onChange={(e) => handleContentChange(activeTab, 'title', e.target.value)} placeholder="Sekme başlığını girin" />
            </div>
            <div className="form-group">
              <label>Açıklama</label>
              <textarea value={currentTab.description} onChange={(e) => handleContentChange(activeTab, 'description', e.target.value)} placeholder="Sekme açıklamasını girin" rows={3} />
            </div>
          </div>
        </div>

        {currentTab.enabled && (
          <div className="content-items-section">
            <div className="section-header">
              <h3><FiBriefcase size={20} /> Özel Bölümler</h3>
              <button className="add-content-btn" onClick={addCustomSection}><FiPlus />Bölüm Ekle</button>
            </div>

            <div className="corporate-sections-grid">
              {customSections.map((section, index) => (
                <div key={section.id} className="corporate-section-card">
                  <div className="section-card-header">
                    <div className="section-info">
                      <span className="section-order">#{index + 1}</span>
                    </div>
                    <div className="section-controls">
                      <button 
                        className={`move-btn ${index === 0 ? 'disabled' : ''}`}
                        onClick={() => index > 0 && moveCustomSection(section.id, 'up')}
                        disabled={index === 0}
                        title="Yukarı taşı"
                      >
                        <FiChevronUp />
                      </button>
                      <button 
                        className={`move-btn ${index === customSections.length - 1 ? 'disabled' : ''}`}
                        onClick={() => index < customSections.length - 1 && moveCustomSection(section.id, 'down')}
                        disabled={index === customSections.length - 1}
                        title="Aşağı taşı"
                      >
                        <FiChevronDown />
                      </button>
                      <button 
                        className="delete-item-btn" 
                        onClick={() => deleteCustomSection(section.id)}
                        title="Sil"
                      >
                        <FiTrash2 />
                      </button>
                    </div>
                  </div>
                  
                  <div className="section-form">
                    <div className="form-group">
                      <label>Bölüm Başlığı</label>
                      <input
                        type="text"
                        placeholder="Bölüm başlığı (örn: Hakkımda, Deneyim, Eğitim)"
                        value={section.title}
                        onChange={(e) => updateCustomSection(section.id, 'title', e.target.value)}
                      />
                    </div>
                    <div className="form-group">
                      <label>İçerik</label>
                      <textarea
                        placeholder="Bölüm içeriği"
                        value={section.content}
                        onChange={(e) => updateCustomSection(section.id, 'content', e.target.value)}
                        rows={6}
                      />
                    </div>
                  </div>
                </div>
              ))}
              
              {customSections.length === 0 && (
                <div className="empty-state">
                  <div className="empty-icon"><FiBriefcase size={48} /></div>
                  <h4>Henüz bölüm yok</h4>
                  <p>Kurumsal sayfanız için ilk bölümü ekleyin</p>
                  <button className="add-first-btn" onClick={addCustomSection}><FiPlus />İlk Bölümü Ekle</button>
                </div>
              )}
            </div>
          </div>
        )}

        {!currentTab.enabled && (
          <div className="disabled-state">
            <div className="disabled-icon"><FiBriefcase size={48} /></div>
            <h4>Kurumsal sekmesi pasif</h4>
            <p>Bu sekmeyi aktif hale getirmek için yukarıdaki toggle'ı kullanın</p>
            <button className="enable-btn" onClick={() => handleTabToggle(activeTab)}>Sekmeyi Aktif Et</button>
          </div>
        )}
      </div>
    </div>
  );
});

CorporateTab.displayName = 'CorporateTab';
export default CorporateTab; 