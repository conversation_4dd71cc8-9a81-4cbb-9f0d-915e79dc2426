import { toast } from 'react-toastify';
import { FiCheckCircle, FiAlertCircle, FiInfo, FiAlertTriangle } from 'react-icons/fi';

const toastOptions = {
  position: "top-right",
  autoClose: 3000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: false,
};

export const showToast = {
  success: (message) => {
    toast.success(
      <div className="toast-content">
        <span>{message}</span>
      </div>,
      toastOptions
    );
  },

  error: (message) => {
    toast.error(
      <div className="toast-content">
        <FiAlertCircle size={18} />
        <span>{message}</span>
      </div>,
      toastOptions
    );
  },

  info: (message) => {
    toast.info(
      <div className="toast-content">
        <FiInfo size={18} />
        <span>{message}</span>
      </div>,
      toastOptions
    );
  },

  warning: (message) => {
    toast.warning(
      <div className="toast-content">
        <FiAlertTriangle size={18} />
        <span>{message}</span>
      </div>,
      toastOptions
    );
  }
};

export default showToast; 