import { useState } from 'react';
import { supabase } from '../supabaseClient';
import { FiX, FiMail, FiLock, FiEye, FiEyeOff, <PERSON><PERSON>ser, FiUserPlus } from 'react-icons/fi';

const LoginModal = ({ isOpen, onClose, onSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Form fields
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setFullName('');
    setError('');
    setShowPassword(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');

      if (isLogin) {
        // Giriş yap
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password
        });
        
        if (error) throw error;
        
        // Başarılı giriş
        onSuccess(data.user);
        handleClose();
      } else {
        // Kayıt ol
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName
            }
          }
        });
        
        if (error) throw error;
        
        if (data.user) {
          // Profil oluştur
          const { error: profileError } = await supabase
            .from('profiles')
            .insert([
              {
                id: data.user.id,
                full_name: fullName,
                email: email
              }
            ]);
          
          if (profileError) {
            console.error('Profil oluşturma hatası:', profileError);
          }
          
          setError('Kayıt başarılı! E-posta adresinizi doğrulayın.');
          setTimeout(() => {
            setIsLogin(true);
            setError('');
          }, 3000);
        }
      }
    } catch (error) {
      setError(error.message || 'Bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const switchMode = () => {
    setIsLogin(!isLogin);
    setError('');
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="login-modal" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="modal-header">
          <h2>{isLogin ? 'Giriş Yap' : 'Kayıt Ol'}</h2>
          <button className="modal-close" onClick={handleClose}>
            <FiX />
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className={`modal-error ${error.includes('başarılı') ? 'success' : ''}`}>
            <p>{error}</p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="modal-form">
          {!isLogin && (
            <div className="form-group">
              <label htmlFor="fullName">Ad Soyad</label>
              <div className="input-wrapper">
                <FiUser className="input-icon" />
                <input
                  id="fullName"
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Adınız ve soyadınız"
                  required={!isLogin}
                  disabled={loading}
                />
              </div>
            </div>
          )}

          <div className="form-group">
            <label htmlFor="modal-email">E-posta Adresi</label>
            <div className="input-wrapper">
              <FiMail className="input-icon" />
              <input
                id="modal-email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                disabled={loading}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="modal-password">Şifre</label>
            <div className="input-wrapper">
              <FiLock className="input-icon" />
              <input
                id="modal-password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
                disabled={loading}
                minLength={6}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </button>
            </div>
          </div>

          <button 
            type="submit" 
            className="modal-submit-btn"
            disabled={loading || !email || !password || (!isLogin && !fullName)}
          >
            {loading ? (
              <>
                <div className="spinner"></div>
                {isLogin ? 'Giriş Yapılıyor...' : 'Kayıt Oluşturuluyor...'}
              </>
            ) : (
              <>
                {isLogin ? 'Giriş Yap' : 'Kayıt Ol'}
                {isLogin ? <FiUser /> : <FiUserPlus />}
              </>
            )}
          </button>
        </form>

        {/* Switch Mode */}
        <div className="modal-switch">
          <p>
            {isLogin ? 'Hesabınız yok mu?' : 'Zaten hesabınız var mı?'}
            <button 
              type="button" 
              className="switch-btn" 
              onClick={switchMode}
              disabled={loading}
            >
              {isLogin ? 'Kayıt Ol' : 'Giriş Yap'}
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginModal; 