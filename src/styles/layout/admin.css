/* Admin Layout Styles */

/* Admin Layout */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  transition: var(--transition-slow);
  box-shadow: var(--shadow-lg);
}

.sidebar-header {
  padding: 2rem 1.5rem 1.5rem;
  border-radius: var(--radius-lg);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.sidebar-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.1;
  border-radius: var(--radius-lg);
}

.sidebar-header h2 {
  margin: 0;
  color: white;
  font-size: 1.75rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  position: relative;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-nav {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.875rem 1.25rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius);
  transition: var(--transition);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  font-size: 0.9375rem;
  letter-spacing: 0.01em;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.nav-item:hover::before {
  opacity: 0.05;
}

.nav-item:hover {
  color: var(--text-primary);
  transform: translateX(3px);
}

.nav-item.active {
  background: rgba(79, 70, 229, 0.08);
  color: var(--accent-primary);
  font-weight: 600;
}

.nav-item.active::before {
  opacity: 0.05;
}

.nav-icon {
  font-size: 1.125rem;
  min-width: 20px;
  opacity: 0.9;
}

.sidebar-footer {
  padding: 1.5rem var(--spacing);
  border-top: 1px solid var(--border-color);
}

.logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.875rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--danger);
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: var(--danger);
  transform: translateY(-1px);
}

/* Main Content */
.admin-main {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--bg-primary);
}

.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
}

.admin-header h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 700;
}

.admin-content {
  flex: 1;
  padding: var(--spacing-xl);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .admin-sidebar.sidebar-open {
    transform: translateX(0);
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }
}

/* Loading States */
.admin-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-muted);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
} 