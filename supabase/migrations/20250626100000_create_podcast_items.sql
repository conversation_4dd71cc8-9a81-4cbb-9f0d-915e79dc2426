-- Create the table for podcast items
CREATE TABLE public.podcast_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    audio_url TEXT NOT NULL,
    cover_image_url TEXT,
    duration_seconds INTEGER,
    view_count INTEGER DEFAULT 0 NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    display_order INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Add comments to the new table and columns
COMMENT ON TABLE public.podcast_items IS 'Stores podcast episodes for users.';
COMMENT ON COLUMN public.podcast_items.title IS 'The title of the podcast episode.';
COMMENT ON COLUMN public.podcast_items.audio_url IS 'The public URL of the stored MP3 file.';
COMMENT ON COLUMN public.podcast_items.cover_image_url IS 'Optional cover image for the podcast episode.';
COMMENT ON COLUMN public.podcast_items.duration_seconds IS 'Duration of the audio in seconds.';
COMMENT ON COLUMN public.podcast_items.view_count IS 'The number of times the podcast has been played (hit count).';
COMMENT ON COLUMN public.podcast_items.is_active IS 'Whether the podcast is visible to the public.';
COMMENT ON COLUMN public.podcast_items.display_order IS 'The display order of the podcast episodes.';

-- Create a trigger to update the updated_at column using the existing function
CREATE TRIGGER handle_podcast_items_updated_at
BEFORE UPDATE ON public.podcast_items
FOR EACH ROW
EXECUTE PROCEDURE public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.podcast_items ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
CREATE POLICY "Users can view their own podcasts"
ON public.podcast_items FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own podcasts"
ON public.podcast_items FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own podcasts"
ON public.podcast_items FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own podcasts"
ON public.podcast_items FOR DELETE
USING (auth.uid() = user_id);

-- Create a new storage bucket for podcast audio files
INSERT INTO storage.buckets (id, name, public)
VALUES ('podcast-audio', 'podcast-audio', TRUE)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for the new bucket
CREATE POLICY "Users can upload audio to their own folder"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'podcast-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own audio files"
ON storage.objects FOR UPDATE
TO authenticated
USING (
    bucket_id = 'podcast-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own audio files"
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'podcast-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
); 