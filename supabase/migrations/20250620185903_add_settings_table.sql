-- Create settings table for site configuration
CREATE TABLE IF NOT EXISTS site_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type TEXT DEFAULT 'text' CHECK (setting_type IN ('text', 'textarea', 'url', 'email', 'file', 'boolean', 'number')),
    category TEXT NOT NULL CHECK (category IN ('seo', 'branding', 'contact', 'general', 'social')),
    display_name TEXT NOT NULL,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;

-- Only admins can view and modify settings
CREATE POLICY "Only admins can view settings" ON site_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Only admins can insert settings" ON site_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Only admins can update settings" ON site_settings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Insert default settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, category, display_name, description, is_required, display_order) VALUES
-- SEO Settings
('site_title', 'Zylome - Dijital Kartvizit Platformu', 'text', 'seo', 'Site Başlığı', 'Web sitesinin ana başlığı (SEO için önemli)', true, 1),
('site_description', 'Modern dijital kartvizit ve profil yönetim platformu. Profesyonel online varlığınızı kolayca oluşturun.', 'textarea', 'seo', 'Site Açıklaması', 'Meta description alanı (SEO için kritik)', true, 2),
('site_keywords', 'dijital kartvizit, online profil, sosyal medya, portfolio, iş kartı', 'textarea', 'seo', 'Site Anahtar Kelimeleri', 'Meta keywords (virgülle ayırın)', false, 3),
('google_analytics_id', '', 'text', 'seo', 'Google Analytics ID', 'GA4 Tracking ID (örn: G-XXXXXXXXXX)', false, 4),
('google_search_console', '', 'textarea', 'seo', 'Google Search Console Meta', 'Google Search Console doğrulama meta tagı', false, 5),

-- Branding Settings
('site_logo', '', 'file', 'branding', 'Site Logosu', 'Ana logo dosyası (PNG/JPG/SVG)', false, 1),
('site_favicon', '', 'file', 'branding', 'Favicon', 'Site ikonu (ICO/PNG 32x32)', false, 2),
('site_logo_dark', '', 'file', 'branding', 'Koyu Tema Logo', 'Koyu tema için logo', false, 3),
('brand_color_primary', '#6366f1', 'text', 'branding', 'Ana Marka Rengi', 'Hex renk kodu (#RRGGBB)', false, 4),
('brand_color_secondary', '#8b5cf6', 'text', 'branding', 'İkincil Marka Rengi', 'Hex renk kodu (#RRGGBB)', false, 5),

-- Contact Settings
('contact_email', '<EMAIL>', 'email', 'contact', 'İletişim E-postası', 'Genel iletişim e-posta adresi', true, 1),
('contact_phone', '', 'text', 'contact', 'İletişim Telefonu', 'Genel iletişim telefon numarası', false, 2),
('contact_address', '', 'textarea', 'contact', 'Adres', 'Fiziksel adres bilgisi', false, 3),
('support_email', '<EMAIL>', 'email', 'contact', 'Destek E-postası', 'Teknik destek e-posta adresi', false, 4),
('business_hours', '09:00 - 18:00 (Pazartesi - Cuma)', 'text', 'contact', 'Çalışma Saatleri', 'İş saatleri bilgisi', false, 5),

-- General Settings
('site_status', 'active', 'text', 'general', 'Site Durumu', 'Site durumu (active/maintenance)', true, 1),
('maintenance_message', 'Site bakımda. Lütfen daha sonra tekrar deneyin.', 'textarea', 'general', 'Bakım Mesajı', 'Bakım modunda gösterilecek mesaj', false, 2),
('user_registration_enabled', 'true', 'boolean', 'general', 'Kullanıcı Kaydına İzin Ver', 'Yeni kullanıcı kaydına izin ver/verme', true, 3),
('default_membership_duration', '365', 'number', 'general', 'Varsayılan Üyelik Süresi (Gün)', 'Yeni kullanıcılar için varsayılan üyelik süresi', true, 4);

-- Create indexes for better performance
CREATE INDEX idx_site_settings_category ON site_settings(category);
CREATE INDEX idx_site_settings_key ON site_settings(setting_key);
CREATE INDEX idx_site_settings_order ON site_settings(category, display_order);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_site_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_site_settings_updated_at
    BEFORE UPDATE ON site_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_site_settings_updated_at();
