/* Dashboard Layout Styles */

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
}

.dashboard-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.dashboard-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing);
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.section-title svg {
  flex-shrink: 0;
  color: var(--accent-primary);
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Responsive Dashboard */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 var(--spacing);
  }

  .dashboard-section {
    padding: var(--spacing);
  }

  .section-header {
    flex-direction: column;
    gap: var(--spacing);
    align-items: stretch;
  }

  .section-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0 var(--spacing-sm);
  }

  .dashboard-section {
    padding: var(--spacing-sm);
  }
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing);
}

/* Activity Lists */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  transition: var(--transition);
}

.activity-item:hover {
  background: var(--bg-primary);
  border-color: var(--accent-primary);
}

/* Dashboard Cards */
.dashboard-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: var(--transition);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing);
}

.card-title {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.card-content {
  color: var(--text-secondary);
  line-height: 1.5;
}

.card-footer {
  margin-top: var(--spacing);
  padding-top: var(--spacing);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Loading States */
.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-muted);
  flex-direction: column;
  gap: var(--spacing);
}

.dashboard-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Empty States */
.dashboard-empty {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-muted);
}

.dashboard-empty svg {
  margin-bottom: var(--spacing);
  opacity: 0.5;
  font-size: 3rem;
}

.dashboard-empty h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 1.25rem;
  font-weight: 600;
}

.dashboard-empty p {
  margin: 0;
  font-size: 0.875rem;
} 