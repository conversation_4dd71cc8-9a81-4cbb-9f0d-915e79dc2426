import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import {
    FaInstagram, FaFacebook, FaLinkedin, FaXTwitter, FaYoutube, FaTiktok,
    FaTelegram, FaSnapchat, FaSpotify, FaReddit, FaPinterest, FaWhatsapp
} from 'react-icons/fa6';
import { 
    FiExternalLink, FiHeart, FiMail, FiMapPin, FiCalendar, 
    FiTrendingUp, FiEye, FiLink, FiFileText, FiImage, FiUserPlus, 
    FiPlay, FiHelpCircle, FiBriefcase, FiShare2, FiCopy, FiX, FiPhone
} from "react-icons/fi";

const Profile = () => {
    const { username } = useParams();
    const [loading, setLoading] = useState(true);
    const [profile, setProfile] = useState(null);
    const [links, setLinks] = useState([]);
    const [activeTab, setActiveTab] = useState('Bağlantılar');
    const [showSharePopup, setShowSharePopup] = useState(false);
    const [copied, setCopied] = useState(false);

    const [viewCount, setViewCount] = useState(0);

    const TABS = [
        { id: 'Bağlantılar', name: 'Bağlantılar', icon: FiLink },
        { id: 'Blog', name: 'Blog', icon: FiFileText },
        { id: 'Galeri', name: 'Galeri', icon: FiImage },
        { id: 'Video', name: 'Video', icon: FiPlay },
        { id: 'SSS', name: 'SSS', icon: FiHelpCircle },
        { id: 'İletişim', name: 'İletişim', icon: FiMail },
        { id: 'Kurumsal', name: 'Kurumsal', icon: FiBriefcase }
    ];

    useEffect(() => {
        const fetchProfile = async () => {
            try {
                setLoading(true);
                const { data, error } = await supabase
                    .from('profiles')
                    .select('*, link_items(*)')
                    .eq('username', username)
                    .order('display_order', { referencedTable: 'link_items', ascending: true })
                    .single();

                if (error) {
                    console.warn(error);
                    setProfile(null);
                } else if (data) {
                    setProfile(data);
                    setLinks(data.link_items || []);
                    
                    // Simulate view count
                    setViewCount(Math.floor(Math.random() * 1000) + 100);
                }
            } catch (error) {
                console.error('Profil çekme hatası:', error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchProfile();
    }, [username]);

    const handleShare = (platform) => {
        const url = window.location.href;
        const text = `${profile.full_name} - ${profile.slogan || 'Zylome Profili'}`;
        
        const shareUrls = {
            whatsapp: `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`,
            telegram: `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
            twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
            linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
        };

        if (shareUrls[platform]) {
            window.open(shareUrls[platform], '_blank', 'width=600,height=400');
        }
        setShowSharePopup(false);
    };

    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(window.location.href);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Kopyalama başarısız:', err);
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return null;
        try {
            return new Date(dateString).toLocaleDateString('tr-TR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (e) {
            return null;
        }
    };

    const socialPlatforms = [
        { key: 'instagram', name: 'Instagram', url: 'https://instagram.com/', icon: FaInstagram, color: '#E4405F' },
        { key: 'facebook', name: 'Facebook', url: 'https://facebook.com/', icon: FaFacebook, color: '#1877F2' },
        { key: 'linkedin', name: 'LinkedIn', url: 'https://linkedin.com/in/', icon: FaLinkedin, color: '#0A66C2' },
        { key: 'x_twitter', name: 'X', url: 'https://x.com/', icon: FaXTwitter, color: '#000000' },
        { key: 'youtube', name: 'YouTube', url: 'https://youtube.com/', icon: FaYoutube, color: '#FF0000' },
        { key: 'tiktok', name: 'TikTok', url: 'https://tiktok.com/@', icon: FaTiktok, color: '#000000' },
        { key: 'telegram', name: 'Telegram', url: 'https://t.me/', icon: FaTelegram, color: '#0088CC' },
        { key: 'snapchat', name: 'Snapchat', url: 'https://snapchat.com/add/', icon: FaSnapchat, color: '#FFFC00' },
        { key: 'spotify', name: 'Spotify', url: 'https://open.spotify.com/user/', icon: FaSpotify, color: '#1DB954' },
        { key: 'reddit', name: 'Reddit', url: 'https://reddit.com/user/', icon: FaReddit, color: '#FF4500' },
        { key: 'pinterest', name: 'Pinterest', url: 'https://pinterest.com/', icon: FaPinterest, color: '#BD081C' }
    ];



    const TabContent = () => {
        switch (activeTab) {
            case 'Bağlantılar':
                return (
                    <div className="modern-links-grid">
                        {links.length > 0 ? (
                            links.map(link => (
                                <div key={link.id} className="modern-link-card">
                                    <div className="link-card-header">
                                        {link.icon_url ? (
                                            <img src={link.icon_url} alt={`${link.title} icon`} className="link-card-icon" />
                                        ) : (
                                            <div className="link-card-icon-placeholder">
                                                <FiLink size={24} />
                                            </div>
                                        )}
                                        <button className="link-card-action">
                                            <FiExternalLink size={16} />
                                        </button>
                                    </div>
                                    <div className="link-card-content">
                                        <h4 className="link-card-title">{link.title}</h4>
                                        <p className="link-card-url">{link.link}</p>
                                    </div>
                                    <a 
                                        href={link.link} 
                                        target="_blank" 
                                        rel="noopener noreferrer" 
                                        className="link-card-overlay"
                                    >
                                        <span>Ziyaret Et</span>
                                    </a>
                                </div>
                            ))
                        ) : (
                            <div className="empty-state">
                                <FiLink size={48} />
                                <h3>Henüz Bağlantı Yok</h3>
                                <p>Bu kullanıcı henüz bir bağlantı eklememiş.</p>
                            </div>
                        )}
                    </div>
                );
            
            case 'Blog':
                return (
                    <div className="blog-section">
                        <div className="empty-state">
                            <FiFileText size={48} />
                            <h3>Blog Yazıları</h3>
                            <p>Henüz blog yazısı paylaşılmamış.</p>
                        </div>
                    </div>
                );

            case 'Galeri':
                return (
                    <div className="gallery-section">
                        <div className="empty-state">
                            <FiImage size={48} />
                            <h3>Fotoğraf Galerisi</h3>
                            <p>Henüz fotoğraf paylaşılmamış.</p>
                        </div>
                    </div>
                );

            case 'Video':
                return (
                    <div className="multimedia-section">
                        <div className="empty-state">
                            <FiPlay size={48} />
                            <h3>Video & Ses</h3>
                            <p>Henüz video içeriği paylaşılmamış.</p>
                        </div>
                    </div>
                );

            case 'SSS':
                return (
                    <div className="faq-section">
                        <div className="empty-state">
                            <FiHelpCircle size={48} />
                            <h3>Sıkça Sorulan Sorular</h3>
                            <p>Henüz SSS eklenmemiş.</p>
                        </div>
                    </div>
                );

            case 'Kurumsal':
                return (
                    <div className="corporate-section">
                        <div className="about-card">
                            <h3>
                                <FiBriefcase size={20} />
                                Kurumsal Bilgiler
                            </h3>
                            <p>{profile.bio || 'Bu kullanıcı henüz kurumsal bilgi eklememiş.'}</p>
                        </div>
                        
                        {(profile.title || profile.company) && (
                            <div className="about-card">
                                <h3>
                                    <FiCalendar size={20} />
                                    Kariyer & Pozisyon
                                </h3>
                                <div className="career-info">
                                    {profile.title && <span className="job-title">{profile.title}</span>}
                                    {profile.company && <span className="company">@ {profile.company}</span>}
                                </div>
                            </div>
                        )}
                        
                        <div className="about-card">
                            <h3>
                                <FiTrendingUp size={20} />
                                İstatistikler
                            </h3>
                            <div className="stats-grid">
                                <div className="stat-item">
                                    <FiEye size={20} />
                                    <span className="stat-value">{viewCount}</span>
                                    <span className="stat-label">Görüntülenme</span>
                                </div>
                                <div className="stat-item">
                                    <FiLink size={20} />
                                    <span className="stat-value">{links.length}</span>
                                    <span className="stat-label">Bağlantı</span>
                                </div>
                                <div className="stat-item">
                                    <FiHeart size={20} />
                                    <span className="stat-value">{Math.floor(viewCount * 0.1)}</span>
                                    <span className="stat-label">Beğeni</span>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            
            case 'İletişim':
                return (
                    <div className="contact-section">
                        <div className="contact-grid">
                            {profile.whatsapp_number && (
                                <a 
                                    href={`https://wa.me/${profile.whatsapp_number}`} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    className="contact-card whatsapp"
                                >
                                    <FaWhatsapp size={24} />
                                    <span>WhatsApp</span>
                                    <FiExternalLink size={16} />
                                </a>
                            )}
                            
                            <div className="contact-card email">
                                <FiMail size={24} />
                                <span>E-posta Gönder</span>
                                <FiExternalLink size={16} />
                            </div>
                            
                            <div className="contact-card location">
                                <FiMapPin size={24} />
                                <span>Konum</span>
                                <FiExternalLink size={16} />
                            </div>
                        </div>
                    </div>
                );
            

            
            default:
                return null;
        }
    };

    if (loading) {
        return (
            <div className="profile-loading">
                <div className="loading-spinner-large"></div>
                <p>Profil yükleniyor...</p>
            </div>
        );
    }

    if (!profile) {
        return (
            <div className="profile-not-found">
                <div className="not-found-content">
                    <FiUserPlus size={64} />
                    <h2>Profil Bulunamadı</h2>
                    <p>Aradığınız kullanıcı mevcut değil veya profili gizli olabilir.</p>
                </div>
            </div>
        );
    }



    return (
        <div className="modern-profile-container">
            {/* Background */}
            <div className="profile-background">
                <div className="bg-pattern"></div>
            </div>

            {/* Share Button */}
            <button 
                className="share-button" 
                onClick={() => setShowSharePopup(true)}
                title="Profili Paylaş"
            >
                <FiShare2 size={20} />
            </button>

            {/* Share Popup */}
            {showSharePopup && (
                <div className="share-popup-overlay" onClick={() => setShowSharePopup(false)}>
                    <div className="share-popup" onClick={(e) => e.stopPropagation()}>
                        <div className="share-popup-header">
                            <h3>Profili Paylaş</h3>
                            <button 
                                className="share-popup-close" 
                                onClick={() => setShowSharePopup(false)}
                            >
                                <FiX size={20} />
                            </button>
                        </div>
                        
                        <div className="share-options">
                            <button 
                                className="share-option whatsapp" 
                                onClick={() => handleShare('whatsapp')}
                            >
                                <FaWhatsapp size={24} />
                                <span>WhatsApp</span>
                            </button>
                            
                            <button 
                                className="share-option telegram" 
                                onClick={() => handleShare('telegram')}
                            >
                                <FaTelegram size={24} />
                                <span>Telegram</span>
                            </button>
                            
                            <button 
                                className="share-option twitter" 
                                onClick={() => handleShare('twitter')}
                            >
                                <FaXTwitter size={24} />
                                <span>X</span>
                            </button>
                            
                            <button 
                                className="share-option facebook" 
                                onClick={() => handleShare('facebook')}
                            >
                                <FaFacebook size={24} />
                                <span>Facebook</span>
                            </button>
                            
                            <button 
                                className="share-option linkedin" 
                                onClick={() => handleShare('linkedin')}
                            >
                                <FaLinkedin size={24} />
                                <span>LinkedIn</span>
                            </button>
                        </div>
                        
                        <div className="share-link-section">
                            <div className="share-link-input">
                                <input 
                                    type="text" 
                                    value={window.location.href} 
                                    readOnly 
                                />
                                <button 
                                    className={`copy-button ${copied ? 'copied' : ''}`} 
                                    onClick={copyToClipboard}
                                >
                                    <FiCopy size={16} />
                                    {copied ? 'Kopyalandı!' : 'Kopyala'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Header */}
            <div className="profile-header">
                <div className="profile-header-content">
                    {/* Avatar - Centered */}
                    <div className="modern-profile-avatar">
                        {profile.avatar_url ? (
                            <img src={profile.avatar_url} alt={`${profile.full_name} avatar`} />
                        ) : (
                            <div className="avatar-placeholder">
                                <span>{(profile.full_name || 'U').charAt(0).toUpperCase()}</span>
                            </div>
                        )}
                    </div>

                    {/* Profile Info - Centered */}
                    <div className="profile-info">
                        <h1 className="profile-name">{profile.full_name || 'İsim Belirtilmemiş'}</h1>
                        
                        
                        <div className="profile-meta text-center">
                            {profile.profession && (
                                <span className="meta-item">
                                    
                                    {profile.profession}
                                </span>
                            )}
                            {profile.title && (
                                <span className="meta-item">
                                    <FiCalendar size={14} />
                                    {profile.title}
                                </span>
                            )}
                        </div>
                        {profile.slogan && <p className="profile-slogan text-center">"{profile.slogan}"</p>}
                    </div>
                </div>
            </div>

            {/* Social Links */}
            {socialPlatforms.some(platform => profile[platform.key]) && (
                <div className="modern-social-section">
                    <div className="social-container">
                        <div className="social-links-modern">
                            {socialPlatforms.map(platform => {
                                const value = profile[platform.key];
                                if (value) {
                                    const IconComponent = platform.icon;
                                    return (
                                        <a 
                                            key={platform.key} 
                                            href={`${platform.url}${value}`} 
                                            target="_blank" 
                                            rel="noopener noreferrer" 
                                            className="social-link-modern"
                                            style={{ '--social-color': platform.color }}
                                            title={platform.name}
                                        >
                                            <IconComponent size={24} />
                                        </a>
                                    );
                                }
                                return null;
                            })}
                        </div>
                        <div className="scroll-fade-left"></div>
                        <div className="scroll-fade-right"></div>
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            {(profile.whatsapp_number || profile.phone || profile.location) && (
                <div className="action-buttons-section">
                    <div className="action-buttons">
                        {profile.whatsapp_number && (
                            <a 
                                href={`https://wa.me/${profile.whatsapp_number}`} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="action-btn whatsapp-btn"
                                title="WhatsApp"
                            >
                                <FaWhatsapp size={20} />
                            </a>
                        )}
                        
                        {profile.phone && (
                            <a 
                                href={`tel:${profile.phone}`} 
                                className="action-btn phone-btn"
                                title="Telefon"
                            >
                                <FiPhone size={20} />
                            </a>
                        )}
                        
                        {profile.location && (
                            <a 
                                href={`https://www.google.com/maps/search/${encodeURIComponent(profile.location)}`} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="action-btn location-btn"
                                title="Konum"
                            >
                                <FiMapPin size={20} />
                            </a>
                        )}
                    </div>
                </div>
            )}

            {/* Tabs */}
            <div className="modern-tabs-container">
                <div className="tabs-nav">
                    {TABS.map(tab => {
                        const IconComponent = tab.icon;
                        return (
                            <button 
                                key={tab.id} 
                                className={`tab-btn-modern ${activeTab === tab.id ? 'active' : ''}`} 
                                onClick={() => setActiveTab(tab.id)}
                            >
                                <IconComponent size={18} />
                                <span>{tab.name}</span>
                            </button>
                        );
                    })}
                </div>
            </div>

            {/* Tab Content */}
            <div className="modern-tab-content">
                <TabContent />
            </div>
        </div>
    );
};

export default Profile; 