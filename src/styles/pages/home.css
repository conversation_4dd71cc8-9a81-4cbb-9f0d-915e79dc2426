/* Home Page Styles */

.home-container {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

.home-hero {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  padding: var(--spacing-2xl) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.home-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

.home-hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing);
}

.home-hero h1 {
  margin: 0 0 var(--spacing) 0;
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.home-hero p {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 1.25rem;
  opacity: 0.9;
  line-height: 1.6;
}

.home-cta {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.home-cta:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.home-features {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.home-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
}

.home-features h2 {
  text-align: center;
  margin: 0 0 var(--spacing-2xl) 0;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto var(--spacing-lg) auto;
}

.feature-card h3 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.feature-card p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.home-stats {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-primary);
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
  text-align: center;
}

.stats-container h2 {
  margin: 0 0 var(--spacing-2xl) 0;
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  padding: var(--spacing-lg);
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
}

.home-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-xl) 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
  text-align: center;
}

.footer-container p {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-hero h1 {
    font-size: 2.5rem;
  }

  .home-hero p {
    font-size: 1.125rem;
  }

  .home-features h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .feature-card {
    padding: var(--spacing-lg);
  }

  .feature-icon {
    width: 64px;
    height: 64px;
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-number {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .home-hero {
    padding: var(--spacing-xl) 0;
  }

  .home-hero h1 {
    font-size: 2rem;
  }

  .home-hero p {
    font-size: 1rem;
  }

  .home-cta {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .home-features {
    padding: var(--spacing-xl) 0;
  }

  .home-features h2 {
    font-size: 1.75rem;
  }

  .feature-card {
    padding: var(--spacing);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .home-hero-content,
  .home-features-container,
  .stats-container,
  .footer-container {
    padding: 0 var(--spacing-sm);
  }
}

/* Landing Page Styles */

.landing-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

/* Header */
.landing-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing);
}

.nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--accent-primary);
  font-size: 1.5rem;
  font-weight: 700;
}

.logo-icon {
  font-size: 1.75rem;
}

.logo-text {
  font-weight: 700;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-menu a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.nav-menu a:hover {
  color: var(--accent-primary);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.btn-register {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.btn-register:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: var(--transition);
}

.mobile-menu-toggle:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.mobile-menu-toggle.hidden {
  display: none;
}

.mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-secondary);
  z-index: 1000;
  padding: var(--spacing);
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.mobile-nav.active {
  display: block;
  transform: translateX(0);
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing);
  border-bottom: 1px solid var(--border-color);
}

.mobile-menu-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--accent-primary);
  font-size: 1.5rem;
  font-weight: 700;
}

.mobile-menu-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: var(--transition);
}

.mobile-menu-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.mobile-menu-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.mobile-menu-content a {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius);
  transition: var(--transition);
}

.mobile-menu-content a:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.mobile-nav-actions {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.mobile-btn-register {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing) var(--spacing-lg);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.mobile-btn-register:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Hero Section */
.hero-section {
  position: relative;
  padding: var(--spacing-2xl) 0;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(67, 56, 202, 0.05));
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(67, 56, 202, 0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-content {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.hero-text h1 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.highlight {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-text p {
  margin: 0 0 var(--spacing-xl) 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: var(--spacing);
  margin-bottom: var(--spacing-2xl);
}

.btn-primary-large {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary-large:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-demo {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 1rem 2rem;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
}

.btn-demo:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
}

.hero-stats .stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mockup-container {
  position: relative;
  transform: rotate(5deg);
}

.mockup-phone {
  width: 280px;
  height: 560px;
  background: var(--bg-secondary);
  border: 8px solid var(--border-color);
  border-radius: 2rem;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  padding: var(--spacing);
}

.screen-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing);
  background: var(--bg-secondary);
  border-radius: var(--radius);
}

.profile-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 50%;
}

.profile-info {
  flex: 1;
}

.profile-name {
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xs);
}

.profile-title {
  height: 12px;
  width: 80px;
  background: var(--border-color);
  border-radius: var(--radius-sm);
}

.property-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
  flex: 1;
}

.property-card {
  height: 120px;
  background: var(--bg-secondary);
  border-radius: var(--radius);
  position: relative;
  overflow: hidden;
}

.property-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.1;
}

/* Features Section */
.features-section {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
}

.section-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto var(--spacing-lg) auto;
}

.feature-card h3 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.feature-card p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* About Section */
.about-section {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-primary);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.about-text h2 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
}

.about-text p {
  margin: 0 0 var(--spacing-xl) 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
  line-height: 1.6;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.about-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  color: var(--text-secondary);
  font-weight: 500;
}

.about-feature svg {
  color: var(--success);
  flex-shrink: 0;
}

.about-visual {
  display: flex;
  justify-content: center;
}

.dashboard-mockup {
  width: 100%;
  max-width: 480px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.mockup-header {
  padding: var(--spacing);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.mockup-tabs {
  display: flex;
  gap: var(--spacing-sm);
}

.tab {
  padding: var(--spacing-sm) var(--spacing);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.tab.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.mockup-content {
  padding: var(--spacing-lg);
}

.chart-placeholder {
  height: 120px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0.1;
  border-radius: var(--radius);
  margin-bottom: var(--spacing-lg);
}

.cards-placeholder {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing);
}

.card-placeholder {
  height: 80px;
  background: var(--bg-tertiary);
  border-radius: var(--radius);
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.testimonial-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.testimonial-content {
  margin-bottom: var(--spacing-lg);
}

.stars {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing);
}

.star {
  color: var(--border-color);
}

.star.filled {
  color: var(--warning);
}

.testimonial-content p {
  margin: 0;
  color: var(--text-secondary);
  font-style: italic;
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.author-avatar {
  width: 48px;
  height: 48px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  overflow: hidden;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-weight: 600;
}

.author-info span {
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  text-align: center;
}

.cta-content h2 {
  margin: 0 0 var(--spacing) 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.cta-content p {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 1.125rem;
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing);
}

.cta-actions .btn-primary-large {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.cta-actions .btn-primary-large:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.cta-note {
  font-size: 0.875rem;
  opacity: 0.8;
  margin: 0;
}

/* Footer */
.landing-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-2xl) 0 var(--spacing-xl) 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-2xl);
}

.footer-brand .logo {
  margin-bottom: var(--spacing);
}

.footer-brand p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-xl);
}

.link-group h4 {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-primary);
  font-weight: 600;
}

.link-group a {
  display: block;
  color: var(--text-secondary);
  text-decoration: none;
  margin-bottom: var(--spacing-sm);
  transition: var(--transition);
}

.link-group a:hover {
  color: var(--accent-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu,
  .nav-actions {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .about-text h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .cta-content h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
  }

  .hero-stats {
    grid-template-columns: 1fr;
  }

  .mockup-phone {
    width: 200px;
    height: 400px;
  }

  .feature-card,
  .testimonial-card {
    padding: var(--spacing);
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.login-modal {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 420px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.modal-error {
  margin: 1rem 1.5rem 0;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius);
  color: #ef4444;
}

.modal-error.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.modal-error p {
  margin: 0;
  font-size: 0.875rem;
}

.modal-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1rem;
  z-index: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.password-toggle:hover {
  color: var(--text-secondary);
  background: var(--bg-tertiary);
}

.modal-submit-btn {
  width: 100%;
  padding: 1rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.modal-submit-btn:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.modal-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.modal-switch {
  padding: 1rem 1.5rem 1.5rem;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.modal-switch p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.switch-btn {
  background: none;
  border: none;
  color: var(--accent-primary);
  cursor: pointer;
  font-weight: 600;
  margin-left: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.switch-btn:hover:not(:disabled) {
  background: rgba(79, 70, 229, 0.1);
}

.switch-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .login-modal {
    margin: 1rem;
    max-width: none;
  }
  
  .modal-header,
  .modal-form,
  .modal-switch {
    padding-left: 1rem;
    padding-right: 1rem;
  }
} 