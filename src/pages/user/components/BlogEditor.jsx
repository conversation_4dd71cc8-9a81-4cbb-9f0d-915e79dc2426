import { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { showToast } from '../../../utils/toast.jsx';
import { 
  FiSave, 
  FiUploadCloud,
  FiTrash2,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle,
  FiArrowLeft,
  FiEye,
  FiEdit,
  FiSettings,
  FiSend,
  FiImage,
  FiInfo
} from 'react-icons/fi';
import { useBlog } from '../../../hooks/useBlog';
import { useAuth } from '../../../hooks/useAuth';

const BlogEditor = ({ postId }) => {
  const { user, loading: authLoading } = useAuth();
  const { createPost, updatePost, fetchPostById, uploadImage, loading: blogLoading } = useBlog();
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    content: '',
    featured_image: '',
    status: 'published'
  });

  const [initialLoading, setInitialLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  const isEditing = useMemo(() => !!postId, [postId]);

  useEffect(() => {
    const loadPost = async () => {
      // Auth loading devam ediyorsa bekle
      if (authLoading) {
        return;
      }
      
      // User yoksa (oturum açılmamış)
      if (!user) {
        navigate('/login');
        return;
      }
      
      if (isEditing) {
        try {
          setInitialLoading(true);
          const postData = await fetchPostById(postId);
          if (postData) {
            setFormData({
              title: postData.title || '',
              excerpt: postData.excerpt || '',
              content: postData.content || '',
              featured_image: postData.featured_image || '',
              status: postData.status || 'published'
            });
          } else {
            showNotification('error', 'Yazı bulunamadı.');
            navigate('/dashboard/blogs/list');
          }
        } catch (error) {
          showNotification('error', 'Yazı yüklenirken bir hata oluştu.');
          console.error('Yazı yükleme hatası:', error);
        } finally {
          setInitialLoading(false);
        }
      } else {
        setInitialLoading(false);
        setFormData({
          title: '',
          excerpt: '',
          content: '',
          featured_image: '',
          status: 'published'
        });
      }
    };
    loadPost();
  }, [postId, isEditing, fetchPostById, user, authLoading, navigate]);

  const showNotification = (type, message) => {
    showToast[type](message);
  };

  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: null }));
    }
  };
  
  const handleContentChange = (content) => {
    handleFieldChange('content', content);
  };

  const resizeImage = (file, maxWidth = 1280, maxHeight = 720, quality = 0.8) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = URL.createObjectURL(file);
        img.onload = () => {
            const canvas = document.createElement('canvas');
            let { width, height } = img;

            if (width > height) {
                if (width > maxWidth) {
                    height = Math.round((height * maxWidth) / width);
                    width = maxWidth;
                }
            } else {
                if (height > maxHeight) {
                    width = Math.round((width * maxHeight) / height);
                    height = maxHeight;
                }
            }
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob(blob => {
                if (blob) {
                    resolve(new File([blob], file.name, {
                        type: file.type,
                        lastModified: Date.now()
                    }));
                } else {
                    reject(new Error('Canvas to Blob conversion failed'));
                }
            }, file.type, quality);
        };
        img.onerror = (error) => reject(error);
    });
  };

  const handleImageUploadInEditor = useCallback(async (blobInfo, progress) => {
    return new Promise(async (resolve, reject) => {
      try {
        const file = blobInfo.blob();
        const resizedFile = await resizeImage(file);
        const imageUrl = await uploadImage(resizedFile);
        resolve(imageUrl.url);
      } catch (error) {
        showNotification('error', 'Resim yüklenirken bir hata oluştu.');
        reject(error);
      }
    });
  }, [uploadImage]);



  const handleFeaturedImageUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      showNotification('error', 'Lütfen bir resim dosyası seçin');
      return;
    }

    try {
      setUploading(true);
      const resizedFile = await resizeImage(file);
      const imageUrl = await uploadImage(resizedFile);
      handleFieldChange('featured_image', imageUrl.url);
      showNotification('success', 'Kapak resmi başarıyla yüklendi');
    } catch (error) {
      console.error('Kapak resmi yükleme hatası:', error);
      showNotification('error', 'Kapak resmi yüklenirken hata oluştu');
    } finally {
      setUploading(false);
      e.target.value = '';
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!formData.title.trim()) {
      errors.title = 'Başlık alanı zorunludur.';
    }
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (statusOverride) => {
    const status = statusOverride || formData.status;
    if (!validateForm()) {
      showNotification('error', 'Lütfen tüm zorunlu alanları doldurun.');
      return;
    }

    setSaving(true);
    const postData = { ...formData, status };

    try {
      let savedPost;
      if (isEditing) {
        savedPost = await updatePost(postId, postData);
      } else {
        savedPost = await createPost(postData);
      }
      const message = status === 'published' 
        ? isEditing 
          ? 'Yazınız başarıyla güncellendi ve yayında!'
          : 'Yeni yazınız başarıyla yayınlandı!'
        : isEditing
          ? 'Yazınız taslak olarak güncellendi'
          : 'Yazınız taslak olarak kaydedildi';
      
      showNotification('success', message);
      setTimeout(() => navigate('/dashboard/blogs/list'), 2000);
    } catch (error) {
      showNotification('error', 'Yazı kaydedilirken bir hata oluştu.');
      console.error('Yazı kaydetme hatası:', error);
    } finally {
      setSaving(false);
    }
  };
  


  if (authLoading || initialLoading) {
    return (
      <div className="modern-loading-container">
        <div className="modern-loading-content">
          <div className="loading-spinner-modern">
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
          </div>
          <p className="loading-text">
            {authLoading ? 'Oturum kontrol ediliyor...' : 'Yazı yükleniyor...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="modern-blog-editor">
      <header className="blog-header">
        <div className="header-left">
          <button onClick={() => navigate('/dashboard/blogs/list')} className="back-btn">
            <FiArrowLeft />
            <span className="ml-2">Yazı Listesine Dön</span>
          </button>
          <div className="header-info">
            <h1>{isEditing ? 'Yazıyı Düzenle' : 'Yeni Yazı Oluştur'}</h1>
            <p>{isEditing ? `"${formData.title}" yazısını düzenliyorsunuz.` : 'Yeni bir blog gönderisi oluşturun.'}</p>
          </div>
        </div>
        <div className="header-actions">
          <button
            onClick={() => handleSubmit('published')}
            className="publish-btn"
            disabled={saving}
          >
            {saving ? <FiLoader className="animate-spin" /> : <FiSend />}
            <span className="ml-2">{isEditing ? 'Güncelle' : 'Yayınla'}</span>
          </button>
        </div>
      </header>

      <main className="blog-main">
        <div className="form-grid">
          <div className="content-column">
            <input
              type="text"
              placeholder="Yazı Başlığı..."
              className={`title-input ${validationErrors.title ? 'border-red-500' : ''}`}
              value={formData.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
            />
            {validationErrors.title && <p className="text-red-500 text-sm mt-1">{validationErrors.title}</p>}
            
            <textarea
              placeholder="Yazı özeti (meta açıklama için kullanılır)"
              className="excerpt-input"
              value={formData.excerpt}
              onChange={(e) => handleFieldChange('excerpt', e.target.value)}
              rows={3}
            />
            
            <div className="editor-wrapper">
              <ReactQuill
                value={formData.content}
                onChange={handleContentChange}
                modules={{
                  toolbar: [
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link'],
                    ['clean']
                  ],
                }}
                formats={[
                  'header', 'bold', 'italic', 'underline', 'strike',
                  'list', 'bullet', 'align', 'link', 'image'
                ]}
                style={{ height: '500px'}}
                placeholder="Blog yazınızı buraya yazın..."
              />
            </div>
          </div>

          <aside className="settings-column">
            <div className="settings-panel">
              <div className="panel-section">
                <h3><FiSettings className="inline-block mr-2" />Ayarlar</h3>
                <label htmlFor="status" className="form-label">Durum</label>
                <select
                  id="status"
                  className="status-select"
                  value={formData.status}
                  onChange={(e) => handleFieldChange('status', e.target.value)}
                >
                  <option value="published">Yayınlanmış</option>
                  <option value="draft">Taslak</option>
                </select>
              </div>

              <div className="panel-section">
                <h3><FiImage className="inline-block mr-2" />Kapak Resmi</h3>
                <div className="image-upload">
                  <input
                    type="file"
                    id="featured-image-upload"
                    className="file-input-hidden"
                    accept="image/*"
                    onChange={handleFeaturedImageUpload}
                    ref={fileInputRef}
                  />
                  <label htmlFor="featured-image-upload" className="image-upload-area">
                    {uploading && !formData.featured_image ? (
                       <div className="upload-loading">
                         <FiLoader className="animate-spin text-2xl"/>
                         <span>Yükleniyor...</span>
                       </div>
                    ) : formData.featured_image ? (
                      <div className="uploaded-image-container">
                        <img src={formData.featured_image} alt="Kapak Resmi" className="uploaded-image" />
                        <button 
                          onClick={(e) => { e.preventDefault(); handleFieldChange('featured_image', ''); }} 
                          className="remove-image-btn"
                        >
                          <FiTrash2 size={16}/>
                        </button>
                      </div>
                    ) : (
                      <div className="upload-placeholder">
                        <FiUploadCloud className="text-4xl" />
                        <span>Resim Yükle</span>
                        <small>Önerilen: 1280x720</small>
                      </div>
                    )}
                  </label>
                </div>
              </div>



              <button
                onClick={() => handleSubmit(formData.status)}
                className="publish-btn-sidebar"
                disabled={saving}
              >
                {saving ? <FiLoader className="animate-spin" /> : <FiSave />}
                <span className="ml-2">{isEditing ? 'Değişiklikleri Kaydet' : 'Yazıyı Oluştur'}</span>
              </button>
            </div>
          </aside>
        </div>
      </main>
    </div>
  );
};

export default BlogEditor; 