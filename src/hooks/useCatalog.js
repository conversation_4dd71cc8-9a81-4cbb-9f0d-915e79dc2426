import { useBaseResource } from './base/useBaseResource';
import { catalogConfig } from './base/useResourceConfigs';

export function useCatalog() {
  const {
    items: catalogs,
    loading,
    error,
    createItem,
    updateItem,
    deleteItem,
    getItemById,
    updateItemOrder
  } = useBaseResource(catalogConfig);

  // PDF ve kapak dosyası ile katalog oluştur
  const createCatalog = async (catalogData, pdfFile, coverFile) => {
    const files = {
      ...(pdfFile && { pdf: pdfFile }),
      ...(coverFile && { cover: coverFile })
    };
    return await createItem(catalogData, files);
  };

  // PDF ve kapak dosyası ile katalog güncelle
  const updateCatalog = async (id, catalogData, pdfFile, coverFile) => {
    const files = {
      ...(pdfFile && { pdf: pdfFile }),
      ...(coverFile && { cover: coverFile })
    };
    return await updateItem(id, catalogData, files);
  };

  return {
    catalogs,
    loading,
    error,
    getCatalogById: getItemById,
    createCatalog,
    updateCatalog,
    deleteCatalog: deleteItem,
    updateCatalogOrder: updateItemOrder
  };
} 