-- Create the table for catalog items
CREATE TABLE public.catalog_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    pdf_url TEXT NOT NULL,
    cover_image_url TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    display_order INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Add comments to the new table and columns
COMMENT ON TABLE public.catalog_items IS 'Stores catalog items for users, linking to PDF files.';
COMMENT ON COLUMN public.catalog_items.title IS 'The title of the catalog.';
COMMENT ON COLUMN public.catalog_items.pdf_url IS 'The public URL of the stored PDF file.';
COMMENT ON COLUMN public.catalog_items.cover_image_url IS 'Optional cover image for the catalog.';
COMMENT ON COLUMN public.catalog_items.is_active IS 'Whether the catalog is visible to the public.';
COMMENT ON COLUMN public.catalog_items.display_order IS 'The display order of the catalog items.';

-- Create a function to handle updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at column
CREATE TRIGGER handle_catalog_items_updated_at
BEFORE UPDATE ON public.catalog_items
FOR EACH ROW
EXECUTE PROCEDURE public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.catalog_items ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
CREATE POLICY "Users can view their own catalogs"
ON public.catalog_items FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own catalogs"
ON public.catalog_items FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own catalogs"
ON public.catalog_items FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own catalogs"
ON public.catalog_items FOR DELETE
USING (auth.uid() = user_id);

-- Create a new storage bucket for catalog PDFs
INSERT INTO storage.buckets (id, name, public)
VALUES ('catalog-pdfs', 'catalog-pdfs', TRUE)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for the new bucket
CREATE POLICY "Users can upload PDFs to their own folder"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'catalog-pdfs' AND
    auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own PDFs"
ON storage.objects FOR UPDATE
TO authenticated
USING (
    bucket_id = 'catalog-pdfs' AND
    auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own PDFs"
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'catalog-pdfs' AND
    auth.uid()::text = (storage.foldername(name))[1]
); 